"""
基础系统测试脚本
Basic System Test Script

测试核心功能，不依赖外部API
"""

import asyncio
import logging
import sys
from datetime import datetime
import pandas as pd
import numpy as np

def test_config_manager():
    """测试配置管理器"""
    print("🔧 测试配置管理器...")
    
    try:
        from src.core.config_manager import ConfigManager
        
        config = ConfigManager()
        
        # 创建环境变量模板
        config.create_env_template()
        
        # 保存配置
        config.save_config()
        
        # 显示配置信息
        print(f"✅ 交易模式: {'模拟' if config.trading.simulation_mode else '实盘'}")
        print(f"✅ 监控间隔: {config.monitoring.interval}")
        print(f"✅ 监控交易对: {', '.join(config.monitoring.symbols)}")
        print(f"✅ 启用策略: {', '.join(config.strategy.enabled_strategies)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_signal_generator():
    """测试信号生成器"""
    print("\n🎯 测试信号生成器...")
    
    try:
        from src.services.signal_generator import StrategySignalGenerator
        
        # 创建信号生成器
        generator = StrategySignalGenerator(initial_capital=10000)
        
        # 添加策略
        generator.add_strategy("BTCUSDT", "CRSI")
        generator.add_strategy("BTCUSDT", "EMA")
        
        # 生成模拟数据
        dates = pd.date_range(start='2024-01-01', periods=100, freq='4H')
        np.random.seed(42)
        
        prices = 50000 + np.cumsum(np.random.randn(100) * 100)
        data = pd.DataFrame({
            'open': prices * (1 + np.random.randn(100) * 0.001),
            'high': prices * (1 + np.abs(np.random.randn(100)) * 0.002),
            'low': prices * (1 - np.abs(np.random.randn(100)) * 0.002),
            'close': prices,
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # 生成信号
        signals = generator.generate_signals("BTCUSDT", data)
        print(f"✅ 生成了 {len(signals)} 个原始信号")
        
        # 测试信号融合
        if signals:
            fused_signal = generator.fuse_signals(signals)
            if fused_signal:
                print(f"✅ 融合信号: {fused_signal.signal.name} (置信度: {fused_signal.confidence:.1f}%)")
            else:
                print("ℹ️ 没有生成融合信号")
        
        # 测试仓位管理
        portfolio = generator.get_portfolio_status()
        print(f"✅ 投资组合状态: 总价值 ${portfolio['total_value']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 信号生成器测试失败: {e}")
        return False

async def test_execution_manager():
    """测试执行管理器"""
    print("\n📢 测试执行管理器...")
    
    try:
        from src.services.signal_generator import StrategySignalGenerator, SignalRecord
        from src.services.execution_manager import ExecutionManager, LogNotifier
        from src.strategies.base import SignalType
        
        # 创建信号生成器和执行管理器
        signal_generator = StrategySignalGenerator()
        execution_manager = ExecutionManager(signal_generator)
        
        # 添加日志通知器
        log_notifier = LogNotifier()
        execution_manager.add_notifier(log_notifier)
        
        # 启动管理器
        execution_manager.start()
        
        # 创建测试信号
        test_signal = SignalRecord(
            symbol="BTCUSDT",
            strategy="TEST",
            signal=SignalType.BUY,
            confidence=75.0,
            reason="测试信号",
            price=50000.0,
            timestamp=datetime.now()
        )
        
        # 处理信号
        success = await execution_manager.process_signal(test_signal)
        print(f"✅ 信号处理{'成功' if success else '失败'}")
        
        # 发送健康报告
        await execution_manager.send_health_report()
        print("✅ 健康报告发送完成")
        
        # 获取统计信息
        stats = execution_manager.get_statistics()
        print(f"✅ 统计信息: {stats['stats']['total_signals']} 个信号")
        
        execution_manager.stop()
        return True
        
    except Exception as e:
        print(f"❌ 执行管理器测试失败: {e}")
        return False

def test_strategies():
    """测试策略系统"""
    print("\n🎯 测试策略系统...")
    
    try:
        from src.strategies import get_strategy
        
        # 生成测试数据
        dates = pd.date_range(start='2024-01-01', periods=100, freq='4H')
        np.random.seed(42)
        
        prices = 50000 + np.cumsum(np.random.randn(100) * 100)
        data = pd.DataFrame({
            'open': prices * (1 + np.random.randn(100) * 0.001),
            'high': prices * (1 + np.abs(np.random.randn(100)) * 0.002),
            'low': prices * (1 - np.abs(np.random.randn(100)) * 0.002),
            'close': prices,
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # 测试各个策略
        strategies = ['CRSI', 'EMA', 'Bollinger']
        
        for strategy_name in strategies:
            try:
                strategy = get_strategy(strategy_name)
                strategy.load_data(data)
                signal = strategy.get_signal()
                
                if signal:
                    print(f"✅ {strategy_name} 策略: {signal['signal'].name} (置信度: {signal['confidence']})")
                else:
                    print(f"⚠️ {strategy_name} 策略: 无信号")
                    
            except Exception as e:
                print(f"❌ {strategy_name} 策略测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略系统测试失败: {e}")
        return False

def test_data_processor_basic():
    """测试数据处理器基础功能"""
    print("\n📊 测试数据处理器基础功能...")
    
    try:
        from src.services.real_time_data_processor import RealTimeDataProcessor
        
        # 创建数据处理器
        processor = RealTimeDataProcessor(interval="4h")
        
        # 添加交易对
        processor.add_symbol("BTCUSDT")
        processor.add_symbol("ETHUSDT")
        
        # 获取状态
        status = processor.get_status()
        print(f"✅ 监控交易对: {len(status['symbols'])} 个")
        print(f"✅ 运行状态: {status['is_running']}")
        
        # 测试数据库初始化
        print("✅ 数据库初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理器测试失败: {e}")
        return False

async def run_integration_test():
    """运行集成测试"""
    print("\n🔄 运行集成测试...")
    
    try:
        from src.core.config_manager import ConfigManager
        from src.services.signal_generator import StrategySignalGenerator
        from src.services.execution_manager import ExecutionManager, LogNotifier
        
        # 创建配置管理器
        config = ConfigManager()
        
        # 创建各个组件
        signal_generator = StrategySignalGenerator(initial_capital=10000)
        execution_manager = ExecutionManager(signal_generator)
        
        # 添加通知器
        log_notifier = LogNotifier()
        execution_manager.add_notifier(log_notifier)
        
        # 配置策略
        signal_generator.add_strategy("BTCUSDT", "CRSI")
        signal_generator.add_strategy("BTCUSDT", "EMA")
        
        print("✅ 集成测试组件创建完成")
        
        # 模拟一些操作
        await execution_manager.send_health_report()
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 开始基础系统测试")
    print("=" * 50)
    
    # 配置日志
    logging.basicConfig(
        level=logging.WARNING,  # 减少日志输出
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("配置管理器", test_config_manager()),
        ("策略系统", test_strategies()),
        ("信号生成器", test_signal_generator()),
        ("执行管理器", test_execution_manager()),
        ("数据处理器", test_data_processor_basic()),
        ("集成测试", run_integration_test())
    ]
    
    for test_name, test_coro in tests:
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            test_results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"• {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！核心系统功能正常。")
        print("\n📋 下一步:")
        print("1. 安装 python-binance: pip install python-binance")
        print("2. 配置 .env 文件中的API密钥")
        print("3. 运行完整的实时监控系统")
    else:
        print("⚠️ 部分测试失败，请检查代码和依赖。")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试运行异常: {e}")
        sys.exit(1) 