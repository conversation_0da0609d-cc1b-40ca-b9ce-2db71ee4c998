#!/usr/bin/env python3
"""
本地数据更新脚本
Local Data Update Script

简化的数据更新脚本，用于定期更新本地4小时K线数据
"""

import sqlite3
import sys
from datetime import datetime, timedelta
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.data_layer.historical_data_fetcher import USDTHistoricalFetcher, save_klines

class LocalDataUpdater:
    """本地数据更新器"""
    
    def __init__(self, db_path: str = "./data/usdt_historical_data.db"):
        """
        初始化数据更新器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = Path(db_path)
        self.table_name = "usdt_klines_historical_4h"
        
        # 默认监控的交易对
        self.symbols = [
            "BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT",
            "BNBUSDT", "XRPUSDT", "SOLUSDT", "MATICUSDT", "AVAXUSDT"
        ]
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据获取器
        self.fetcher = USDTHistoricalFetcher(use_proxy=False, batch_delay=0.5)
    
    def check_database(self) -> bool:
        """检查数据库是否存在"""
        if not self.db_path.exists():
            self.logger.error(f"❌ 数据库文件不存在: {self.db_path}")
            return False
        
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?", 
                (self.table_name,)
            )
            table_exists = cursor.fetchone() is not None
            conn.close()
            
            if not table_exists:
                self.logger.error(f"❌ 数据表不存在: {self.table_name}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 数据库检查失败: {e}")
            return False
    
    def get_data_status(self) -> dict:
        """获取数据状态"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            
            # 获取每个交易对的最新数据时间
            query = f"""
            SELECT symbol, MAX(open_time) as latest_time, COUNT(*) as record_count
            FROM {self.table_name}
            WHERE symbol IN ({','.join(['?' for _ in self.symbols])})
            GROUP BY symbol
            ORDER BY symbol
            """
            
            cursor = conn.execute(query, self.symbols)
            results = cursor.fetchall()
            conn.close()
            
            data_status = {}
            current_time = datetime.now()
            
            for symbol, latest_timestamp, record_count in results:
                if latest_timestamp:
                    latest_time = datetime.fromtimestamp(latest_timestamp / 1000)
                    time_diff = current_time - latest_time
                    
                    # 4小时数据，如果超过5小时就认为需要更新
                    needs_update = time_diff > timedelta(hours=5)
                    
                    data_status[symbol] = {
                        'latest_time': latest_time,
                        'time_diff_hours': time_diff.total_seconds() / 3600,
                        'needs_update': needs_update,
                        'record_count': record_count
                    }
                else:
                    # 没有数据的交易对
                    data_status[symbol] = {
                        'latest_time': None,
                        'time_diff_hours': float('inf'),
                        'needs_update': True,
                        'record_count': 0
                    }
            
            # 检查是否有交易对完全缺失
            existing_symbols = set(data_status.keys())
            missing_symbols = set(self.symbols) - existing_symbols
            
            for symbol in missing_symbols:
                data_status[symbol] = {
                    'latest_time': None,
                    'time_diff_hours': float('inf'),
                    'needs_update': True,
                    'record_count': 0
                }
            
            return data_status
            
        except Exception as e:
            self.logger.error(f"❌ 获取数据状态失败: {e}")
            return {}
    
    def update_data(self, force_update: bool = False) -> bool:
        """
        更新数据
        
        Args:
            force_update: 是否强制更新所有数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            self.logger.info("🔄 开始检查数据更新需求...")
            
            # 获取数据状态
            data_status = self.get_data_status()
            
            if not data_status:
                self.logger.error("❌ 无法获取数据状态")
                return False
            
            # 确定需要更新的交易对
            if force_update:
                symbols_need_update = self.symbols
                self.logger.info("🔄 强制更新所有交易对")
            else:
                symbols_need_update = [
                    symbol for symbol, status in data_status.items() 
                    if status['needs_update']
                ]
            
            if not symbols_need_update:
                self.logger.info("✅ 所有数据都是最新的，无需更新")
                return True
            
            self.logger.info(f"📋 需要更新的交易对 ({len(symbols_need_update)}): {', '.join(symbols_need_update)}")
            
            # 显示当前数据状态
            for symbol in symbols_need_update:
                status = data_status[symbol]
                if status['latest_time']:
                    self.logger.info(f"  {symbol}: 最新数据 {status['latest_time'].strftime('%Y-%m-%d %H:%M')} ({status['time_diff_hours']:.1f}小时前)")
                else:
                    self.logger.info(f"  {symbol}: 无数据")
            
            # 计算更新时间范围
            end_time = datetime.now()
            
            # 对于有数据的交易对，从最新时间开始更新
            # 对于无数据的交易对，获取最近30天的数据
            all_klines = []
            
            for symbol in symbols_need_update:
                status = data_status[symbol]
                
                if status['latest_time'] and not force_update:
                    # 增量更新：从最新时间开始
                    start_time = status['latest_time'] - timedelta(hours=4)  # 重叠一个周期确保连续性
                    days = (end_time - start_time).days + 1
                    self.logger.info(f"📈 增量更新 {symbol}: 最近 {days} 天")
                else:
                    # 完整更新：获取最近30天
                    start_time = end_time - timedelta(days=30)
                    self.logger.info(f"📈 完整更新 {symbol}: 最近 30 天")
                
                # 获取数据
                symbol_klines = self.fetcher.batch_get_historical_data(
                    [symbol],
                    interval='4h',
                    start_time=start_time,
                    end_time=end_time
                )
                
                if symbol_klines:
                    all_klines.extend(symbol_klines)
                    self.logger.info(f"✅ {symbol}: 获取 {len(symbol_klines)} 条记录")
                else:
                    self.logger.warning(f"⚠️ {symbol}: 未获取到数据")
            
            if not all_klines:
                self.logger.warning("⚠️ 未获取到任何新数据")
                return False
            
            # 保存到数据库
            self.logger.info(f"💾 保存 {len(all_klines)} 条记录到数据库...")
            
            conn = sqlite3.connect(str(self.db_path))
            save_klines(conn, all_klines, self.table_name)
            conn.close()
            
            self.logger.info("✅ 数据更新完成")
            
            # 显示更新后的状态
            self._show_update_summary()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 数据更新失败: {e}")
            return False
    
    def _show_update_summary(self):
        """显示更新摘要"""
        try:
            data_status = self.get_data_status()
            
            self.logger.info("\n📊 更新后数据状态:")
            for symbol in self.symbols:
                if symbol in data_status:
                    status = data_status[symbol]
                    if status['latest_time']:
                        self.logger.info(f"  {symbol}: {status['record_count']} 条记录, 最新: {status['latest_time'].strftime('%Y-%m-%d %H:%M')}")
                    else:
                        self.logger.info(f"  {symbol}: 无数据")
                else:
                    self.logger.info(f"  {symbol}: 未找到")
                    
        except Exception as e:
            self.logger.error(f"❌ 显示摘要失败: {e}")
    
    def run_update(self, force: bool = False):
        """运行更新"""
        self.logger.info("🚀 启动本地数据更新器")
        self.logger.info("=" * 50)
        
        # 检查数据库
        if not self.check_database():
            return False
        
        # 更新数据
        success = self.update_data(force_update=force)
        
        if success:
            self.logger.info("🎉 数据更新成功完成")
        else:
            self.logger.error("❌ 数据更新失败")
        
        return success

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="本地数据更新脚本")
    parser.add_argument("--force", action="store_true", help="强制更新所有数据")
    parser.add_argument("--db", default="./data/usdt_historical_data.db", help="数据库路径")
    
    args = parser.parse_args()
    
    # 创建更新器
    updater = LocalDataUpdater(db_path=args.db)
    
    # 运行更新
    success = updater.run_update(force=args.force)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
