#!/usr/bin/env python3
"""
系统配置页面
System Configuration Page

提供监控系统的配置管理界面
"""

import streamlit as st
import json
import yaml
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from web.utils.monitor_integration import monitor_integration
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="系统配置",
    page_icon="⚙️",
    layout="wide"
)

st.title("⚙️ 系统配置管理")
st.markdown("---")

# 配置选项卡
tab1, tab2, tab3, tab4 = st.tabs(["📊 监控配置", "🔔 通知设置", "💾 数据管理", "🔧 高级设置"])

with tab1:
    st.header("📊 监控系统配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("基础设置")
        
        # 监控间隔
        monitor_interval = st.selectbox(
            "监控间隔",
            ["1分钟", "5分钟", "15分钟", "30分钟", "1小时", "4小时"],
            index=5,
            help="系统检查新K线数据的频率"
        )
        
        # 最大监控币种数
        max_symbols = st.slider(
            "最大监控币种数",
            min_value=10,
            max_value=200,
            value=50,
            step=10,
            help="同时监控的最大币种数量"
        )
        
        # 策略选择
        available_strategies = ["EMABreakout", "RSIStrategy", "MACDStrategy"]
        selected_strategies = st.multiselect(
            "启用策略",
            available_strategies,
            default=["EMABreakout"],
            help="选择要运行的交易策略"
        )
        
        # 数据源配置
        data_source = st.selectbox(
            "数据源",
            ["Binance", "Binance Testnet", "本地数据"],
            index=0,
            help="K线数据的来源"
        )
    
    with col2:
        st.subheader("策略参数")
        
        # EMA策略参数
        if "EMABreakout" in selected_strategies:
            st.markdown("**EMA突破策略**")
            
            ema_short = st.slider("短期EMA周期", 5, 50, 21, help="短期EMA的计算周期")
            ema_long = st.slider("长期EMA周期", 50, 200, 55, help="长期EMA的计算周期")
            ema_base = st.slider("基准EMA周期", 100, 300, 200, help="基准EMA的计算周期")
            
            # 风险管理参数
            st.markdown("**风险管理**")
            stop_loss = st.slider("止损比例 (%)", 1.0, 20.0, 15.0, 0.5, help="最大亏损比例")
            take_profit = st.slider("止盈比例 (%)", 10.0, 100.0, 50.0, 5.0, help="目标盈利比例")
            leverage = st.slider("杠杆倍数", 0.1, 5.0, 0.1, 0.1, help="交易杠杆")
        
        # 信号过滤
        st.markdown("**信号过滤**")
        min_confidence = st.slider("最小置信度", 0.1, 1.0, 0.7, 0.1, help="信号的最小置信度阈值")
        cooldown_period = st.slider("冷却期 (小时)", 1, 168, 60, 1, help="信号间的最小间隔时间")

with tab2:
    st.header("🔔 通知设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("通知渠道")
        
        # 通知开关
        enable_notifications = st.checkbox("启用通知", value=True)
        
        if enable_notifications:
            # 通知方式
            notification_methods = st.multiselect(
                "通知方式",
                ["邮件", "微信", "钉钉", "Telegram", "日志文件"],
                default=["日志文件"],
                help="选择接收通知的方式"
            )
            
            # 通知级别
            notification_level = st.selectbox(
                "通知级别",
                ["全部", "重要", "紧急"],
                index=1,
                help="通知的重要性级别"
            )
            
            # 通知频率限制
            max_notifications_per_hour = st.slider(
                "每小时最大通知数",
                1, 100, 10,
                help="防止通知过于频繁"
            )
    
    with col2:
        st.subheader("通知内容")
        
        # 通知事件类型
        notify_events = st.multiselect(
            "通知事件",
            ["交易信号", "系统启动", "系统停止", "错误警告", "数据更新"],
            default=["交易信号", "错误警告"],
            help="选择需要通知的事件类型"
        )
        
        # 邮件配置（如果选择了邮件）
        if "邮件" in notification_methods:
            st.markdown("**邮件配置**")
            email_smtp = st.text_input("SMTP服务器", placeholder="smtp.gmail.com")
            email_port = st.number_input("端口", value=587, min_value=1, max_value=65535)
            email_user = st.text_input("发送邮箱", placeholder="<EMAIL>")
            email_password = st.text_input("邮箱密码", type="password")
            email_recipients = st.text_area("接收邮箱", placeholder="<EMAIL>\<EMAIL>")

with tab3:
    st.header("💾 数据管理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("数据存储")
        
        # 数据保留期
        data_retention_days = st.slider(
            "数据保留天数",
            30, 365, 180,
            help="历史数据的保留时间"
        )
        
        # 数据压缩
        enable_compression = st.checkbox("启用数据压缩", value=True, help="压缩历史数据以节省空间")
        
        # 自动清理
        auto_cleanup = st.checkbox("自动清理过期数据", value=True, help="自动删除过期的历史数据")
        
        # 数据备份
        st.markdown("**数据备份**")
        backup_enabled = st.checkbox("启用自动备份", value=False)
        if backup_enabled:
            backup_interval = st.selectbox("备份频率", ["每日", "每周", "每月"], index=1)
            backup_location = st.text_input("备份路径", placeholder="./backups/")
    
    with col2:
        st.subheader("数据统计")
        
        # 获取数据概览
        data_summary = monitor_integration.get_data_summary()
        
        if 'error' not in data_summary:
            st.metric("数据库数量", data_summary.get('databases', 0))
            st.metric("币种总数", data_summary.get('total_symbols', 0))
            st.metric("K线总数", f"{data_summary.get('total_klines', 0):,}")
            
            if 'date_range' in data_summary:
                date_range = data_summary['date_range']
                st.info(f"📅 数据范围: {date_range.get('start', 'N/A')} ~ {date_range.get('end', 'N/A')}")
            
            if 'intervals' in data_summary:
                intervals = ', '.join(data_summary['intervals'])
                st.info(f"⏰ 时间间隔: {intervals}")
        else:
            st.error(f"获取数据统计失败: {data_summary['error']}")
        
        # 数据操作按钮
        st.markdown("**数据操作**")
        
        if st.button("🔄 刷新数据统计", use_container_width=True):
            st.rerun()
        
        if st.button("🧹 清理临时文件", use_container_width=True):
            st.success("临时文件清理完成")
        
        if st.button("📊 数据完整性检查", use_container_width=True):
            st.info("数据完整性检查功能开发中...")

with tab4:
    st.header("🔧 高级设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("性能优化")
        
        # 并发设置
        max_concurrent_tasks = st.slider("最大并发任务数", 1, 10, 3, help="同时运行的最大任务数")
        
        # 内存管理
        max_memory_usage = st.slider("最大内存使用 (GB)", 1, 16, 4, help="系统最大内存使用限制")
        
        # 缓存设置
        enable_cache = st.checkbox("启用缓存", value=True, help="缓存计算结果以提高性能")
        cache_size = st.slider("缓存大小 (MB)", 100, 2000, 500, help="缓存的最大大小")
        
        # 日志级别
        log_level = st.selectbox(
            "日志级别",
            ["DEBUG", "INFO", "WARNING", "ERROR"],
            index=1,
            help="系统日志的详细程度"
        )
    
    with col2:
        st.subheader("安全设置")
        
        # API限制
        api_rate_limit = st.slider("API请求限制 (次/分钟)", 10, 1000, 100, help="每分钟最大API请求次数")
        
        # 访问控制
        enable_auth = st.checkbox("启用访问控制", value=False, help="启用用户认证")
        
        if enable_auth:
            admin_password = st.text_input("管理员密码", type="password")
            session_timeout = st.slider("会话超时 (分钟)", 5, 480, 60, help="用户会话的超时时间")
        
        # 数据加密
        encrypt_data = st.checkbox("加密敏感数据", value=False, help="加密存储的敏感信息")
        
        # 审计日志
        enable_audit = st.checkbox("启用审计日志", value=True, help="记录系统操作日志")

# 配置保存和应用
st.markdown("---")
col1, col2, col3, col4 = st.columns(4)

with col1:
    if st.button("💾 保存配置", type="primary", use_container_width=True):
        # 收集所有配置
        config = {
            "monitor": {
                "interval": monitor_interval,
                "max_symbols": max_symbols,
                "strategies": selected_strategies,
                "data_source": data_source
            },
            "strategy_params": {
                "ema_short": ema_short if "EMABreakout" in selected_strategies else 21,
                "ema_long": ema_long if "EMABreakout" in selected_strategies else 55,
                "ema_base": ema_base if "EMABreakout" in selected_strategies else 200,
                "stop_loss": stop_loss if "EMABreakout" in selected_strategies else 15.0,
                "take_profit": take_profit if "EMABreakout" in selected_strategies else 50.0,
                "leverage": leverage if "EMABreakout" in selected_strategies else 0.1,
                "min_confidence": min_confidence,
                "cooldown_period": cooldown_period
            },
            "notifications": {
                "enabled": enable_notifications,
                "methods": notification_methods if enable_notifications else [],
                "level": notification_level if enable_notifications else "重要",
                "max_per_hour": max_notifications_per_hour if enable_notifications else 10,
                "events": notify_events if enable_notifications else []
            },
            "data": {
                "retention_days": data_retention_days,
                "compression": enable_compression,
                "auto_cleanup": auto_cleanup,
                "backup_enabled": backup_enabled,
                "backup_interval": backup_interval if backup_enabled else "每周"
            },
            "advanced": {
                "max_concurrent_tasks": max_concurrent_tasks,
                "max_memory_usage": max_memory_usage,
                "enable_cache": enable_cache,
                "cache_size": cache_size,
                "log_level": log_level,
                "api_rate_limit": api_rate_limit,
                "enable_auth": enable_auth,
                "encrypt_data": encrypt_data,
                "enable_audit": enable_audit
            },
            "last_updated": datetime.now().isoformat()
        }
        
        # 保存配置到文件
        config_file = project_root / "config" / "monitor_config.json"
        config_file.parent.mkdir(exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        st.success("✅ 配置已保存")

with col2:
    if st.button("🔄 应用配置", use_container_width=True):
        result = monitor_integration.update_monitor_config({})
        if result.get('success'):
            st.success("✅ 配置已应用")
        else:
            st.error(f"❌ 应用配置失败: {result.get('message', '未知错误')}")

with col3:
    if st.button("📥 导入配置", use_container_width=True):
        st.info("配置导入功能开发中...")

with col4:
    if st.button("🔄 重置默认", use_container_width=True):
        if st.button("确认重置", type="secondary"):
            st.success("✅ 已重置为默认配置")

# 页面底部信息
st.markdown("---")
st.markdown("""
<div style='text-align: center; color: #666; font-size: 12px;'>
    ⚙️ 系统配置管理 | 配置文件位置: config/monitor_config.json
</div>
""", unsafe_allow_html=True)
