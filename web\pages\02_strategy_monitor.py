#!/usr/bin/env python3
"""
策略监控页面
Strategy Monitoring Page

实时监控策略验证系统状态，显示信号历史和系统控制功能
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import sqlite3
import json
import asyncio
import threading
import time
from pathlib import Path
import sys

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from web.utils.monitor_integration import monitor_integration
    from web.components.charts import create_line_chart, create_bar_chart
    from web.components.tables import create_data_table
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="Strategy Monitor",
    page_icon="🔍",
    layout="wide"
)

st.title("🔍 Strategy Monitor")
st.markdown("---")

# 初始化session state
if 'last_refresh' not in st.session_state:
    st.session_state.last_refresh = datetime.now()

def get_alerts_data():
    """获取告警数据"""
    return monitor_integration.get_alerts_data()

def get_system_status():
    """获取系统状态"""
    return monitor_integration.get_system_status()

def start_monitor():
    """启动监控系统"""
    result = monitor_integration.start_monitor()
    if result['success']:
        st.success(f"✅ {result['message']}")
    else:
        st.error(f"❌ {result['message']}")

def stop_monitor():
    """停止监控系统"""
    result = monitor_integration.stop_monitor()
    if result['success']:
        st.success(f"✅ {result['message']}")
    else:
        st.error(f"❌ {result['message']}")

# 主界面布局
col1, col2, col3 = st.columns([2, 2, 1])

with col1:
    st.subheader("📊 系统状态")
    status = get_system_status()
    
    if status:
        # 状态指标
        metrics_col1, metrics_col2, metrics_col3, metrics_col4 = st.columns(4)
        
        with metrics_col1:
            status_text = "运行中" if status.get('monitor_running', False) else "已停止"
            status_color = "normal" if status.get('monitor_running', False) else "off"
            st.metric("监控状态", status_text)

        with metrics_col2:
            st.metric("监控币种", status.get('monitored_symbols', 0))

        with metrics_col3:
            st.metric("总告警数", status.get('total_alerts', 0))

        with metrics_col4:
            st.metric("24h信号", status.get('recent_signals', 0))

        # 显示K线时间信息
        if status.get('next_kline_time'):
            next_kline = datetime.fromisoformat(status['next_kline_time'].replace('Z', '+00:00'))
            st.info(f"📅 下个K线: {next_kline.strftime('%Y-%m-%d %H:%M')}")
        else:
            st.info(f"📅 最后更新: {status.get('last_update', 'N/A')}")

with col2:
    st.subheader("🎛️ 系统控制")
    
    control_col1, control_col2 = st.columns(2)
    
    with control_col1:
        if st.button("🚀 启动监控", type="primary", use_container_width=True):
            start_monitor()
    
    with control_col2:
        if st.button("🛑 停止监控", use_container_width=True):
            stop_monitor()
    
    # 监控配置
    st.markdown("**监控配置**")
    monitor_interval = st.selectbox("监控间隔", ["1分钟", "5分钟", "15分钟"], index=1)
    max_symbols = st.slider("最大监控币种数", 10, 100, 50)

with col3:
    st.subheader("🔄 快速操作")
    
    if st.button("📈 刷新数据", use_container_width=True):
        st.rerun()
    
    if st.button("📊 查看日志", use_container_width=True):
        st.info("日志查看功能开发中...")
    
    if st.button("⚙️ 系统设置", use_container_width=True):
        st.info("系统设置功能开发中...")

st.markdown("---")

# 告警历史
st.subheader("📋 策略信号历史")

alerts_df = get_alerts_data()

if not alerts_df.empty:
    # 信号统计图表
    chart_col1, chart_col2 = st.columns(2)
    
    with chart_col1:
        # 按日期统计信号数量
        daily_signals = alerts_df.groupby(alerts_df['timestamp'].dt.date).size().reset_index()
        daily_signals.columns = ['日期', '信号数量']
        
        fig = px.line(daily_signals, x='日期', y='信号数量', 
                     title="📈 每日信号数量趋势")
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with chart_col2:
        # 按策略类型统计
        strategy_counts = alerts_df['strategy'].value_counts()
        
        fig = px.pie(values=strategy_counts.values, names=strategy_counts.index,
                    title="🎯 策略信号分布")
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    # 详细信号表格
    st.markdown("**最近信号详情**")
    
    # 筛选选项
    filter_col1, filter_col2, filter_col3 = st.columns(3)
    
    with filter_col1:
        selected_symbols = st.multiselect("筛选币种", 
                                        options=alerts_df['symbol'].unique(),
                                        default=[])
    
    with filter_col2:
        selected_strategies = st.multiselect("筛选策略",
                                           options=alerts_df['strategy'].unique(),
                                           default=[])
    
    with filter_col3:
        selected_signals = st.multiselect("筛选信号类型",
                                        options=alerts_df['signal_type'].unique(),
                                        default=[])
    
    # 应用筛选
    filtered_df = alerts_df.copy()
    
    if selected_symbols:
        filtered_df = filtered_df[filtered_df['symbol'].isin(selected_symbols)]
    
    if selected_strategies:
        filtered_df = filtered_df[filtered_df['strategy'].isin(selected_strategies)]
    
    if selected_signals:
        filtered_df = filtered_df[filtered_df['signal_type'].isin(selected_signals)]
    
    # 显示表格
    if not filtered_df.empty:
        # 格式化显示
        display_df = filtered_df.copy()
        display_df['时间'] = display_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
        display_df['币种'] = display_df['symbol']
        display_df['策略'] = display_df['strategy']
        display_df['信号类型'] = display_df['signal_type']
        display_df['置信度'] = display_df['confidence'].round(3)
        display_df['价格'] = display_df['price'].round(4)
        display_df['原因'] = display_df['reason']
        
        # 选择显示列
        display_columns = ['时间', '币种', '策略', '信号类型', '置信度', '价格', '原因']
        st.dataframe(display_df[display_columns], use_container_width=True, height=400)
        
        # 导出功能
        if st.button("📥 导出数据"):
            csv = filtered_df.to_csv(index=False)
            st.download_button(
                label="下载CSV文件",
                data=csv,
                file_name=f"strategy_signals_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
    else:
        st.info("📭 没有符合筛选条件的信号数据")

else:
    st.info("📭 暂无策略信号数据")
    st.markdown("""
    **提示**：
    - 启动监控系统后，策略信号将自动记录
    - 系统每4小时检查一次新的K线数据
    - 信号数据将保存在本地数据库中
    """)

# 页面底部信息
st.markdown("---")
st.markdown("""
<div style='text-align: center; color: #666; font-size: 12px;'>
    🔍 策略监控系统 | 基于4小时K线数据 | 实时策略验证
</div>
""", unsafe_allow_html=True)
