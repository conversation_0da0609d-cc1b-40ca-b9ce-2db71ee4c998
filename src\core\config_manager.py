"""
配置管理器
Configuration Manager

统一管理系统配置、环境变量和API密钥
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

@dataclass
class DatabaseConfig:
    """数据库配置"""
    path: str = "./data/market_data.db"
    backup_path: str = "./data/backups"
    max_backup_files: int = 10

@dataclass
class BinanceConfig:
    """Binance API配置"""
    api_key: str = ""
    api_secret: str = ""
    testnet: bool = True
    base_url: str = "https://api.binance.com"
    testnet_url: str = "https://testnet.binance.vision"

@dataclass
class DiscordConfig:
    """Discord通知配置"""
    enabled: bool = False
    webhook_url: str = ""
    bot_token: str = ""
    channel_id: int = 0

@dataclass
class EmailConfig:
    """邮件通知配置"""
    enabled: bool = False
    smtp_server: str = "smtp.gmail.com"
    smtp_port: int = 587
    username: str = ""
    password: str = ""
    recipients: List[str] = None
    
    def __post_init__(self):
        if self.recipients is None:
            self.recipients = []

@dataclass
class TradingConfig:
    """交易配置"""
    simulation_mode: bool = True
    auto_execution: bool = False
    initial_capital: float = 10000.0
    max_position_pct: float = 0.1
    max_total_position_pct: float = 0.8
    stop_loss_pct: float = 0.05
    take_profit_pct: float = 0.15
    min_confidence: float = 60.0
    signal_cooldown_hours: int = 4
    max_signals_per_day: int = 5

@dataclass
class MonitoringConfig:
    """监控配置"""
    interval: str = "4h"
    symbols: List[str] = None
    blacklist: List[str] = None
    health_check_interval_minutes: int = 30
    log_level: str = "INFO"
    log_file: str = "./logs/trading.log"
    max_log_files: int = 10

    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ["BTCUSDT", "ETHUSDT"]
        if self.blacklist is None:
            self.blacklist = []

@dataclass
class StrategyConfig:
    """策略配置"""
    enabled_strategies: List[str] = None
    strategy_weights: Dict[str, float] = None
    custom_params: Dict[str, Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.enabled_strategies is None:
            self.enabled_strategies = ["CRSI", "EMA", "Bollinger"]
        if self.strategy_weights is None:
            self.strategy_weights = {
                "CRSIStrategy": 0.3,
                "EMAStrategy": 0.25,
                "RSIStrategy": 0.2,
                "BollingerStrategy": 0.25
            }
        if self.custom_params is None:
            self.custom_params = {}

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "./config", env_file: str = ".env"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
            env_file: 环境变量文件路径
        """
        self.config_dir = Path(config_dir)
        self.env_file = Path(env_file)
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置对象
        self.database = DatabaseConfig()
        self.binance = BinanceConfig()
        self.discord = DiscordConfig()
        self.email = EmailConfig()
        self.trading = TradingConfig()
        self.monitoring = MonitoringConfig()
        self.strategy = StrategyConfig()
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
        # 加载配置
        self._load_env_file()
        self._load_config_files()
        self._validate_config()
    
    def _load_env_file(self):
        """加载环境变量文件"""
        if DOTENV_AVAILABLE and self.env_file.exists():
            load_dotenv(self.env_file)
            self.logger.info(f"✅ 加载环境变量文件: {self.env_file}")
        elif self.env_file.exists():
            self.logger.warning("⚠️ python-dotenv未安装，无法加载.env文件")
        
        # 从环境变量加载敏感配置
        self.binance.api_key = os.getenv("BINANCE_API_KEY", "")
        self.binance.api_secret = os.getenv("BINANCE_API_SECRET", "")
        self.binance.testnet = os.getenv("BINANCE_TESTNET", "true").lower() == "true"
        
        self.discord.webhook_url = os.getenv("DISCORD_WEBHOOK_URL", "")
        self.discord.bot_token = os.getenv("DISCORD_BOT_TOKEN", "")
        self.discord.channel_id = int(os.getenv("DISCORD_CHANNEL_ID", "0"))
        self.discord.enabled = bool(self.discord.webhook_url or self.discord.bot_token)
        
        self.email.username = os.getenv("EMAIL_USERNAME", "")
        self.email.password = os.getenv("EMAIL_PASSWORD", "")
        self.email.enabled = bool(self.email.username and self.email.password)
        
        # 其他配置
        self.trading.simulation_mode = os.getenv("SIMULATION_MODE", "true").lower() == "true"
        self.trading.auto_execution = os.getenv("AUTO_EXECUTION", "false").lower() == "true"
        self.monitoring.log_level = os.getenv("LOG_LEVEL", "INFO")
    
    def _load_config_files(self):
        """加载配置文件"""
        config_files = {
            "database.json": self.database,
            "trading.json": self.trading,
            "monitoring.json": self.monitoring,
            "strategy.json": self.strategy,
            "email.json": self.email
        }
        
        for filename, config_obj in config_files.items():
            config_path = self.config_dir / filename
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 更新配置对象
                    for key, value in data.items():
                        if hasattr(config_obj, key):
                            setattr(config_obj, key, value)
                    
                    self.logger.info(f"✅ 加载配置文件: {filename}")
                    
                except Exception as e:
                    self.logger.error(f"❌ 加载配置文件失败 {filename}: {e}")
    
    def _validate_config(self):
        """验证配置"""
        errors = []
        
        # 验证数据库配置
        if not self.database.path:
            errors.append("数据库路径不能为空")
        
        # 验证交易配置
        if self.trading.initial_capital <= 0:
            errors.append("初始资金必须大于0")
        
        if not (0 < self.trading.max_position_pct <= 1):
            errors.append("最大仓位比例必须在0-1之间")
        
        if not (0 < self.trading.min_confidence <= 100):
            errors.append("最小置信度必须在0-100之间")
        
        # 验证监控配置
        if self.monitoring.interval not in ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]:
            errors.append("无效的K线间隔")
        
        if not self.monitoring.symbols:
            errors.append("监控交易对列表不能为空")
        
        # 验证策略配置
        if not self.strategy.enabled_strategies:
            errors.append("启用策略列表不能为空")
        
        # 验证通知配置
        if not (self.discord.enabled or self.email.enabled):
            self.logger.warning("⚠️ 没有启用任何通知渠道")
        
        if errors:
            error_msg = "配置验证失败:\n" + "\n".join(f"• {error}" for error in errors)
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        self.logger.info("✅ 配置验证通过")
    
    def save_config(self):
        """保存配置到文件"""
        config_files = {
            "database.json": self.database,
            "trading.json": self.trading,
            "monitoring.json": self.monitoring,
            "strategy.json": self.strategy,
            "email.json": self.email
        }
        
        for filename, config_obj in config_files.items():
            config_path = self.config_dir / filename
            try:
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(asdict(config_obj), f, indent=2, ensure_ascii=False)
                
                self.logger.info(f"✅ 保存配置文件: {filename}")
                
            except Exception as e:
                self.logger.error(f"❌ 保存配置文件失败 {filename}: {e}")
    
    def create_env_template(self):
        """创建环境变量模板文件"""
        template_content = """# Binance API配置
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here
BINANCE_TESTNET=true

# Discord通知配置
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_url
DISCORD_BOT_TOKEN=your_bot_token_here
DISCORD_CHANNEL_ID=your_channel_id_here

# 邮件通知配置
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password_here

# 交易配置
SIMULATION_MODE=true
AUTO_EXECUTION=false

# 日志配置
LOG_LEVEL=INFO
"""
        
        template_path = self.env_file.parent / "env.template"
        try:
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(template_content)
            
            self.logger.info(f"✅ 创建环境变量模板: {template_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 创建环境变量模板失败: {e}")
    
    def get_binance_client_config(self) -> Dict[str, Any]:
        """获取Binance客户端配置"""
        return {
            "api_key": self.binance.api_key,
            "api_secret": self.binance.api_secret,
            "testnet": self.binance.testnet,
            "base_url": self.binance.testnet_url if self.binance.testnet else self.binance.base_url
        }
    
    def get_discord_config(self) -> Dict[str, Any]:
        """获取Discord配置"""
        return {
            "enabled": self.discord.enabled,
            "webhook_url": self.discord.webhook_url,
            "bot_token": self.discord.bot_token,
            "channel_id": self.discord.channel_id
        }
    
    def get_email_config(self) -> Dict[str, Any]:
        """获取邮件配置"""
        return {
            "enabled": self.email.enabled,
            "smtp_server": self.email.smtp_server,
            "smtp_port": self.email.smtp_port,
            "username": self.email.username,
            "password": self.email.password,
            "recipients": self.email.recipients
        }
    
    def get_trading_config(self) -> Dict[str, Any]:
        """获取交易配置"""
        return asdict(self.trading)
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return asdict(self.monitoring)
    
    def get_strategy_config(self) -> Dict[str, Any]:
        """获取策略配置"""
        return asdict(self.strategy)
    
    def update_config(self, section: str, **kwargs):
        """更新配置"""
        config_map = {
            "database": self.database,
            "binance": self.binance,
            "discord": self.discord,
            "email": self.email,
            "trading": self.trading,
            "monitoring": self.monitoring,
            "strategy": self.strategy
        }
        
        if section not in config_map:
            raise ValueError(f"未知配置节: {section}")
        
        config_obj = config_map[section]
        
        for key, value in kwargs.items():
            if hasattr(config_obj, key):
                setattr(config_obj, key, value)
                self.logger.info(f"✅ 更新配置 {section}.{key} = {value}")
            else:
                self.logger.warning(f"⚠️ 未知配置项: {section}.{key}")
        
        # 重新验证配置
        self._validate_config()
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            "database": asdict(self.database),
            "binance": {
                **asdict(self.binance),
                "api_key": "***" if self.binance.api_key else "",
                "api_secret": "***" if self.binance.api_secret else ""
            },
            "discord": {
                **asdict(self.discord),
                "webhook_url": "***" if self.discord.webhook_url else "",
                "bot_token": "***" if self.discord.bot_token else ""
            },
            "email": {
                **asdict(self.email),
                "password": "***" if self.email.password else ""
            },
            "trading": asdict(self.trading),
            "monitoring": asdict(self.monitoring),
            "strategy": asdict(self.strategy)
        }
    
    def export_config(self, export_path: str = "./config_export.yaml"):
        """导出配置到YAML文件"""
        try:
            config_data = self.get_all_config()
            
            if YAML_AVAILABLE:
                with open(export_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                self.logger.info(f"✅ 配置导出成功: {export_path}")
            else:
                # 使用JSON格式作为备选
                import json
                export_path = export_path.replace('.yaml', '.json')
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
                self.logger.info(f"✅ 配置导出成功 (JSON格式): {export_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 配置导出失败: {e}")
    
    def setup_logging(self):
        """设置日志配置"""
        log_file = Path(self.monitoring.log_file)
        log_file.parent.mkdir(exist_ok=True)
        
        # 配置根日志器
        logging.basicConfig(
            level=getattr(logging, self.monitoring.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger.info(f"✅ 日志配置完成: {log_file}")

# 全局配置实例
config = ConfigManager()

# 使用示例
if __name__ == "__main__":
    # 创建配置管理器
    config_manager = ConfigManager()
    
    # 创建环境变量模板
    config_manager.create_env_template()
    
    # 保存配置
    config_manager.save_config()
    
    # 设置日志
    config_manager.setup_logging()
    
    # 显示配置信息
    print("📊 当前配置:")
    print(f"• 交易模式: {'模拟' if config_manager.trading.simulation_mode else '实盘'}")
    print(f"• 监控间隔: {config_manager.monitoring.interval}")
    print(f"• 监控交易对: {', '.join(config_manager.monitoring.symbols)}")
    print(f"• 启用策略: {', '.join(config_manager.strategy.enabled_strategies)}")
    print(f"• Discord通知: {'✅' if config_manager.discord.enabled else '❌'}")
    print(f"• 邮件通知: {'✅' if config_manager.email.enabled else '❌'}")
    
    print("\n🎉 配置管理器初始化完成！") 