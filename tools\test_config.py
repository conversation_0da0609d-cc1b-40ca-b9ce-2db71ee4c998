#!/usr/bin/env python3
"""
测试配置文件加载
Test Configuration Loading
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from auto_4h_monitor import Auto4HMonitor

def test_config():
    """测试配置加载"""
    print("🧪 测试配置文件加载")
    print("=" * 50)
    
    monitor = Auto4HMonitor()
    
    # 测试默认配置
    print("\n1. 默认配置:")
    config = monitor._load_data_fetcher_config()
    print(f"  时间范围: {config['days']} 天 ({config['time_label']})")
    print(f"  币种选择: {config['selection_mode']} (前{config.get('top_n', 'N/A')}个)")
    print(f"  最小交易量: {config['min_volume']:,} USDT")
    print(f"  K线间隔: {config['interval']}")
    print(f"  使用代理: {config['use_proxy']}")
    print(f"  代理端口: {config['proxy_port']}")
    
    # 测试实时更新预设
    print("\n2. 实时更新预设:")
    config = monitor._load_data_fetcher_config("real_time_update")
    print(f"  时间范围: {config['days']} 天 ({config['time_label']})")
    print(f"  币种选择: {config['selection_mode']} (前{config.get('top_n', 'N/A')}个)")
    print(f"  最小交易量: {config['min_volume']:,} USDT")
    print(f"  K线间隔: {config['interval']}")
    print(f"  使用代理: {config['use_proxy']}")
    print(f"  代理端口: {config['proxy_port']}")
    
    # 测试历史验证预设
    print("\n3. 历史验证预设:")
    config = monitor._load_data_fetcher_config("historical_validation")
    print(f"  时间范围: {config['days']} 天 ({config['time_label']})")
    print(f"  币种选择: {config['selection_mode']} (前{config.get('top_n', 'N/A')}个)")
    print(f"  最小交易量: {config['min_volume']:,} USDT")
    print(f"  K线间隔: {config['interval']}")
    print(f"  使用代理: {config['use_proxy']}")
    print(f"  代理端口: {config['proxy_port']}")
    
    print("\n✅ 配置测试完成")

if __name__ == "__main__":
    test_config()
