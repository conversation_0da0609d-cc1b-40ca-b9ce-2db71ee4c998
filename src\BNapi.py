import requests
from src.config_loader import load_config

config_data = load_config('./config.json')
perpkline_url = config_data['perpkline_url']
proxies = config_data['proxies']

def get_klines_info(symbol, start_time, end_time, interval):
    try:
        start_time = int(start_time.timestamp() * 1000)
        end_time = int(end_time.timestamp() * 1000)
        response = requests.get(perpkline_url, params={
            "symbol": symbol,
            "interval": interval, "startTime": start_time, "endTime": end_time
            # ,"apikey": API_KEY
        }, proxies=proxies, timeout=5)

        data = response.json()
        return data

    except Exception as e:
        # global g_unfinished_token
        # g_unfinished_token.append(symbol)
        print("get_binance_"+symbol+"_kinfo Error:", e)
        return None