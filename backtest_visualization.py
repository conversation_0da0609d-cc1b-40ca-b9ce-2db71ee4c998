#!/usr/bin/env python3
"""
回测结果可视化
Backtest Results Visualization

生成K线图表、信号分析图和交易记录可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns
from datetime import datetime, timedelta
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class BacktestVisualizer:
    """回测结果可视化类"""
    
    def __init__(self):
        self.colors = {
            'up': '#00ff88',      # 上涨K线颜色
            'down': '#ff4444',    # 下跌K线颜色
            'ema21': '#ff6600',   # EMA21颜色
            'ema50': '#0066ff',   # EMA50颜色
            'ema200': '#888888',  # EMA200颜色
            'buy': '#00ff00',     # 买入信号颜色
            'sell': '#ff0000',    # 卖出信号颜色
            'bg': '#1e1e1e',      # 背景颜色
            'grid': '#333333'     # 网格颜色
        }
        
        # 设置图表样式
        plt.style.use('dark_background')
    
    def plot_kline_with_signals(self, data, symbol, save_path=None):
        """
        绘制K线图和交易信号
        
        Args:
            data: 包含价格数据和信号的DataFrame
            symbol: 币种名称
            save_path: 保存路径
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12), 
                                       gridspec_kw={'height_ratios': [3, 1]})
        
        # 准备数据
        data = data.copy()
        data['datetime'] = pd.to_datetime(data['datetime'])
        
        # 过滤有效数据
        valid_data = data[data['valid_signal'] == True].copy()
        
        if len(valid_data) == 0:
            print(f"⚠️ {symbol} 无有效数据用于绘图")
            return
        
        # 绘制K线图
        self._plot_candlesticks(ax1, valid_data)
        
        # 绘制EMA线
        self._plot_ema_lines(ax1, valid_data)
        
        # 绘制交易信号
        self._plot_signals(ax1, valid_data)
        
        # 绘制成交量
        self._plot_volume(ax2, valid_data)
        
        # 设置标题和标签
        ax1.set_title(f'{symbol} - EMA突破策略回测结果', fontsize=16, fontweight='bold')
        ax1.set_ylabel('价格 (USDT)', fontsize=12)
        ax2.set_ylabel('成交量', fontsize=12)
        ax2.set_xlabel('时间', fontsize=12)
        
        # 设置时间轴格式
        self._format_time_axis(ax1, ax2, valid_data)
        
        # 添加图例
        self._add_legend(ax1)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='#1e1e1e', edgecolor='none')
            print(f"📊 图表已保存: {save_path}")
        
        plt.show()
    
    def _plot_candlesticks(self, ax, data):
        """绘制K线"""
        for i, row in data.iterrows():
            color = self.colors['up'] if row['close'] >= row['open'] else self.colors['down']
            
            # 绘制影线
            ax.plot([i, i], [row['low'], row['high']], color=color, linewidth=1)
            
            # 绘制实体
            height = abs(row['close'] - row['open'])
            bottom = min(row['open'], row['close'])
            
            rect = Rectangle((i-0.3, bottom), 0.6, height, 
                           facecolor=color, edgecolor=color, alpha=0.8)
            ax.add_patch(rect)
    
    def _plot_ema_lines(self, ax, data):
        """绘制EMA线"""
        x = range(len(data))
        
        # EMA21
        if 'ema21' in data.columns and not data['ema21'].isna().all():
            ax.plot(x, data['ema21'], color=self.colors['ema21'], 
                   linewidth=2, label='EMA21', alpha=0.8)
        
        # EMA50
        if 'ema50' in data.columns and not data['ema50'].isna().all():
            ax.plot(x, data['ema50'], color=self.colors['ema50'], 
                   linewidth=2, label='EMA50', alpha=0.8)
        
        # EMA200
        if 'ema200' in data.columns and not data['ema200'].isna().all():
            valid_ema200 = data['ema200'].dropna()
            if len(valid_ema200) > 0:
                ax.plot(x, data['ema200'], color=self.colors['ema200'], 
                       linewidth=2, label='EMA200', alpha=0.6, linestyle='--')
    
    def _plot_signals(self, ax, data):
        """绘制交易信号"""
        x = range(len(data))
        
        # 买入信号
        buy_signals = data[data['signal'] == 1]
        if not buy_signals.empty:
            buy_indices = [i for i, row in data.iterrows() if row['signal'] == 1]
            buy_prices = buy_signals['close'].values
            ax.scatter(buy_indices, buy_prices, color=self.colors['buy'], 
                      marker='^', s=100, label='买入信号', zorder=5)
        
        # 卖出信号
        sell_signals = data[data['signal'] == -1]
        if not sell_signals.empty:
            sell_indices = [i for i, row in data.iterrows() if row['signal'] == -1]
            sell_prices = sell_signals['close'].values
            ax.scatter(sell_indices, sell_prices, color=self.colors['sell'], 
                      marker='v', s=100, label='卖出信号', zorder=5)
    
    def _plot_volume(self, ax, data):
        """绘制成交量"""
        x = range(len(data))
        colors = [self.colors['up'] if row['close'] >= row['open'] 
                 else self.colors['down'] for _, row in data.iterrows()]
        
        ax.bar(x, data['volume'], color=colors, alpha=0.6, width=0.8)
    
    def _format_time_axis(self, ax1, ax2, data):
        """格式化时间轴"""
        # 设置x轴刻度
        n_ticks = min(10, len(data))
        tick_indices = np.linspace(0, len(data)-1, n_ticks, dtype=int)
        tick_labels = [data.iloc[i]['datetime'].strftime('%m-%d %H:%M') 
                      for i in tick_indices]
        
        for ax in [ax1, ax2]:
            ax.set_xticks(tick_indices)
            ax.set_xticklabels(tick_labels, rotation=45)
            ax.grid(True, alpha=0.3, color=self.colors['grid'])
    
    def _add_legend(self, ax):
        """添加图例"""
        ax.legend(loc='upper left', frameon=True, fancybox=True, 
                 shadow=True, framealpha=0.8)
    
    def plot_signal_details(self, data, symbol, signal_indices, save_path=None):
        """
        绘制信号详细图（信号前后的K线）
        
        Args:
            data: 价格数据
            symbol: 币种名称
            signal_indices: 信号索引列表
            save_path: 保存路径
        """
        if not signal_indices:
            print(f"⚠️ {symbol} 无信号可绘制")
            return
        
        n_signals = len(signal_indices)
        cols = min(3, n_signals)
        rows = (n_signals + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(6*cols, 4*rows))
        if n_signals == 1:
            axes = [axes]
        elif rows == 1 and cols > 1:
            axes = list(axes)
        elif rows > 1:
            axes = axes.flatten()
        else:
            axes = [axes]
        
        for i, signal_idx in enumerate(signal_indices):
            if i >= len(axes):
                break

            ax = axes[i]
            
            # 获取信号前后20个K线
            start_idx = max(0, signal_idx - 20)
            end_idx = min(len(data), signal_idx + 21)
            
            signal_data = data.iloc[start_idx:end_idx].copy().reset_index(drop=True)
            signal_pos = signal_idx - start_idx
            
            # 绘制K线和EMA
            self._plot_candlesticks(ax, signal_data)
            self._plot_ema_lines(ax, signal_data)
            
            # 标记信号点
            signal_row = data.iloc[signal_idx]
            signal_type = "买入" if signal_row['signal'] == 1 else "卖出"
            signal_color = self.colors['buy'] if signal_row['signal'] == 1 else self.colors['sell']
            
            ax.scatter([signal_pos], [signal_row['close']], 
                      color=signal_color, marker='*', s=200, zorder=5)
            
            # 设置标题
            signal_time = signal_row['datetime'].strftime('%Y-%m-%d %H:%M')
            ax.set_title(f'{symbol} - {signal_type}信号\n{signal_time}', fontsize=10)
            
            # 设置坐标轴
            ax.grid(True, alpha=0.3)
            
            # 隐藏多余的子图
            if i >= n_signals:
                ax.set_visible(False)
        
        # 隐藏多余的子图
        for j in range(n_signals, len(axes)):
            axes[j].set_visible(False)
        
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight',
                       facecolor='#1e1e1e', edgecolor='none')
            print(f"📊 信号详细图已保存: {save_path}")
        
        plt.show()
    
    def plot_backtest_summary(self, results, save_path=None):
        """
        绘制回测汇总图表
        
        Args:
            results: 回测结果字典
            save_path: 保存路径
        """
        if not results:
            print("⚠️ 无回测结果可绘制")
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 准备数据
        symbols = list(results.keys())
        returns = [results[s]['total_return'] for s in symbols]
        win_rates = [results[s]['win_rate'] for s in symbols]
        trade_counts = [results[s]['total_trades'] for s in symbols]
        
        # 1. 收益率分布
        ax1.hist(returns, bins=20, color=self.colors['ema21'], alpha=0.7, edgecolor='white')
        ax1.set_title('收益率分布', fontsize=14, fontweight='bold')
        ax1.set_xlabel('收益率 (%)')
        ax1.set_ylabel('币种数量')
        ax1.axvline(0, color='red', linestyle='--', alpha=0.7, label='盈亏平衡线')
        ax1.legend()
        
        # 2. 胜率分布
        ax2.hist(win_rates, bins=20, color=self.colors['ema50'], alpha=0.7, edgecolor='white')
        ax2.set_title('胜率分布', fontsize=14, fontweight='bold')
        ax2.set_xlabel('胜率 (%)')
        ax2.set_ylabel('币种数量')
        ax2.axvline(50, color='red', linestyle='--', alpha=0.7, label='50%胜率线')
        ax2.legend()
        
        # 3. 收益率 vs 胜率散点图
        colors = ['green' if r > 0 else 'red' for r in returns]
        ax3.scatter(win_rates, returns, c=colors, alpha=0.6, s=60)
        ax3.set_title('收益率 vs 胜率', fontsize=14, fontweight='bold')
        ax3.set_xlabel('胜率 (%)')
        ax3.set_ylabel('收益率 (%)')
        ax3.axhline(0, color='red', linestyle='--', alpha=0.5)
        ax3.axvline(50, color='red', linestyle='--', alpha=0.5)
        ax3.grid(True, alpha=0.3)
        
        # 4. 交易次数分布
        ax4.hist(trade_counts, bins=15, color=self.colors['ema200'], alpha=0.7, edgecolor='white')
        ax4.set_title('交易次数分布', fontsize=14, fontweight='bold')
        ax4.set_xlabel('交易次数')
        ax4.set_ylabel('币种数量')
        
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight',
                       facecolor='#1e1e1e', edgecolor='none')
            print(f"📊 汇总图表已保存: {save_path}")
        
        plt.show()
