"""
辅助函数
提供通用的辅助功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Tuple
import hashlib
import json

def generate_session_id() -> str:
    """生成会话ID"""
    timestamp = datetime.now().isoformat()
    return hashlib.md5(timestamp.encode()).hexdigest()[:8]

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """安全除法，避免除零错误"""
    if denominator == 0 or pd.isna(denominator) or pd.isna(numerator):
        return default
    return numerator / denominator

def calculate_percentage_change(current: float, previous: float) -> float:
    """计算百分比变化"""
    if previous == 0 or pd.isna(previous) or pd.isna(current):
        return 0.0
    return (current - previous) / previous

def validate_date_range(start_date: str, end_date: str) -> Tuple[bool, str]:
    """
    验证日期范围
    
    Returns:
        (is_valid, error_message)
    """
    try:
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        
        if start >= end:
            return False, "开始日期必须早于结束日期"
        
        if end > datetime.now():
            return False, "结束日期不能超过当前日期"
        
        if (end - start).days > 365 * 3:
            return False, "日期范围不能超过3年"
        
        return True, ""
        
    except Exception as e:
        return False, f"日期格式错误: {e}"

def create_download_link(data: Union[pd.DataFrame, dict, str], 
                        filename: str, 
                        link_text: str = "下载") -> str:
    """
    创建下载链接
    
    Args:
        data: 要下载的数据
        filename: 文件名
        link_text: 链接文本
    
    Returns:
        HTML下载链接
    """
    if isinstance(data, pd.DataFrame):
        csv = data.to_csv(index=False)
        b64 = base64.b64encode(csv.encode()).decode()
        href = f'<a href="data:file/csv;base64,{b64}" download="{filename}">{link_text}</a>'
    elif isinstance(data, dict):
        json_str = json.dumps(data, indent=2, ensure_ascii=False)
        b64 = base64.b64encode(json_str.encode()).decode()
        href = f'<a href="data:file/json;base64,{b64}" download="{filename}">{link_text}</a>'
    else:
        b64 = base64.b64encode(str(data).encode()).decode()
        href = f'<a href="data:file/txt;base64,{b64}" download="{filename}">{link_text}</a>'
    
    return href

def show_loading_spinner(message: str = "处理中..."):
    """显示加载动画"""
    return st.spinner(message)

def show_success_message(message: str, duration: int = 3):
    """显示成功消息"""
    success_placeholder = st.empty()
    success_placeholder.success(message)
    
    # 自动清除消息（在实际应用中可能需要使用JavaScript）
    import time
    time.sleep(duration)
    success_placeholder.empty()

def show_error_message(message: str, details: str = None):
    """显示错误消息"""
    st.error(message)
    if details:
        with st.expander("错误详情"):
            st.code(details)

def create_metric_card(title: str, value: str, delta: str = None, 
                      help_text: str = None) -> None:
    """创建指标卡片"""
    st.metric(
        label=title,
        value=value,
        delta=delta,
        help=help_text
    )

def create_info_box(title: str, content: str, box_type: str = "info") -> None:
    """创建信息框"""
    if box_type == "info":
        st.info(f"**{title}**\n\n{content}")
    elif box_type == "warning":
        st.warning(f"**{title}**\n\n{content}")
    elif box_type == "error":
        st.error(f"**{title}**\n\n{content}")
    elif box_type == "success":
        st.success(f"**{title}**\n\n{content}")

def create_progress_bar(current: int, total: int, message: str = "") -> None:
    """创建进度条"""
    progress = current / total if total > 0 else 0
    st.progress(progress)
    if message:
        st.text(f"{message} ({current}/{total})")

def validate_numeric_input(value: Any, min_val: float = None, 
                          max_val: float = None) -> Tuple[bool, str]:
    """
    验证数值输入
    
    Returns:
        (is_valid, error_message)
    """
    try:
        num_value = float(value)
        
        if min_val is not None and num_value < min_val:
            return False, f"值不能小于 {min_val}"
        
        if max_val is not None and num_value > max_val:
            return False, f"值不能大于 {max_val}"
        
        return True, ""
        
    except (ValueError, TypeError):
        return False, "请输入有效的数字"

def format_large_number(number: float, precision: int = 2) -> str:
    """格式化大数字显示"""
    if abs(number) >= 1e12:
        return f"{number/1e12:.{precision}f}T"
    elif abs(number) >= 1e9:
        return f"{number/1e9:.{precision}f}B"
    elif abs(number) >= 1e6:
        return f"{number/1e6:.{precision}f}M"
    elif abs(number) >= 1e3:
        return f"{number/1e3:.{precision}f}K"
    else:
        return f"{number:.{precision}f}"

def get_color_by_value(value: float, positive_color: str = "green", 
                      negative_color: str = "red", neutral_color: str = "gray") -> str:
    """根据数值获取颜色"""
    if value > 0:
        return positive_color
    elif value < 0:
        return negative_color
    else:
        return neutral_color

def create_status_indicator(status: str, status_map: Dict[str, str] = None) -> str:
    """创建状态指示器"""
    default_map = {
        'success': '🟢',
        'warning': '🟡',
        'error': '🔴',
        'info': '🔵',
        'neutral': '⚫'
    }
    
    if status_map:
        default_map.update(status_map)
    
    return default_map.get(status, '❓')

def calculate_statistics(data: pd.Series) -> Dict[str, float]:
    """计算基础统计信息"""
    if data.empty:
        return {}
    
    return {
        'count': len(data),
        'mean': data.mean(),
        'median': data.median(),
        'std': data.std(),
        'min': data.min(),
        'max': data.max(),
        'q25': data.quantile(0.25),
        'q75': data.quantile(0.75)
    }

def create_comparison_table(data1: Dict, data2: Dict, 
                           labels: Tuple[str, str] = ("方案A", "方案B")) -> pd.DataFrame:
    """创建对比表格"""
    comparison_data = []
    
    all_keys = set(data1.keys()) | set(data2.keys())
    
    for key in all_keys:
        comparison_data.append({
            '指标': key,
            labels[0]: data1.get(key, 'N/A'),
            labels[1]: data2.get(key, 'N/A')
        })
    
    return pd.DataFrame(comparison_data)

def filter_dataframe(df: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
    """根据过滤条件过滤DataFrame"""
    filtered_df = df.copy()
    
    for column, filter_value in filters.items():
        if column not in df.columns:
            continue
        
        if isinstance(filter_value, list):
            filtered_df = filtered_df[filtered_df[column].isin(filter_value)]
        elif isinstance(filter_value, tuple) and len(filter_value) == 2:
            min_val, max_val = filter_value
            filtered_df = filtered_df[
                (filtered_df[column] >= min_val) & 
                (filtered_df[column] <= max_val)
            ]
        elif isinstance(filter_value, str):
            filtered_df = filtered_df[
                filtered_df[column].str.contains(filter_value, case=False, na=False)
            ]
        else:
            filtered_df = filtered_df[filtered_df[column] == filter_value]
    
    return filtered_df

def cache_data(key: str, data: Any, ttl: int = 3600) -> None:
    """缓存数据（使用Streamlit session state）"""
    if 'cache' not in st.session_state:
        st.session_state.cache = {}
    
    st.session_state.cache[key] = {
        'data': data,
        'timestamp': datetime.now(),
        'ttl': ttl
    }

def get_cached_data(key: str) -> Optional[Any]:
    """获取缓存数据"""
    if 'cache' not in st.session_state:
        return None
    
    if key not in st.session_state.cache:
        return None
    
    cache_entry = st.session_state.cache[key]
    
    # 检查是否过期
    if (datetime.now() - cache_entry['timestamp']).seconds > cache_entry['ttl']:
        del st.session_state.cache[key]
        return None
    
    return cache_entry['data']
