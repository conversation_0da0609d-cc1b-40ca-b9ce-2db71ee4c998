#!/usr/bin/env python3
"""
回测输出格式化工具
"""

from datetime import datetime
from typing import Dict, List, Any
import pandas as pd
import os
import sys
from io import StringIO

class LogCapture:
    """日志捕获类，同时输出到终端和保存到文件"""

    def __init__(self, log_file: str = None):
        self.log_file = log_file
        self.original_stdout = sys.stdout
        self.log_buffer = StringIO()
        self.is_capturing = False

    def start_capture(self):
        """开始捕获输出"""
        if self.log_file:
            self.is_capturing = True
            sys.stdout = self

    def stop_capture(self):
        """停止捕获输出"""
        if self.is_capturing:
            sys.stdout = self.original_stdout
            self.is_capturing = False

            # 保存日志到文件
            if self.log_file:
                try:
                    with open(self.log_file, 'w', encoding='utf-8') as f:
                        f.write(self.log_buffer.getvalue())
                    print(f"📄 完整运行日志已保存到: {self.log_file}")
                except Exception as e:
                    print(f"⚠️ 保存日志失败: {e}")

    def write(self, text):
        """重写write方法，同时输出到终端和缓存"""
        # 输出到原始终端
        self.original_stdout.write(text)
        self.original_stdout.flush()

        # 保存到缓存
        self.log_buffer.write(text)

    def flush(self):
        """刷新输出"""
        self.original_stdout.flush()

    def __enter__(self):
        self.start_capture()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop_capture()

class BacktestOutputFormatter:
    """回测输出格式化器"""
    
    def __init__(self, verbose_level: int = 1):
        """
        初始化格式化器
        
        Args:
            verbose_level: 输出详细程度 (0=简洁, 1=标准, 2=详细)
        """
        self.verbose_level = verbose_level
        self.trade_count = 0
        self.signal_count = 0
        
    def format_timestamp(self, timestamp: datetime) -> str:
        """格式化时间戳"""
        return timestamp.strftime('%m-%d %H:%M')
    
    def format_price(self, price: float) -> str:
        """格式化价格"""
        if price >= 1000:
            return f"${price:,.0f}"
        elif price >= 1:
            return f"${price:.2f}"
        else:
            return f"${price:.4f}"
    
    def format_amount(self, amount: float) -> str:
        """格式化金额"""
        return f"{amount:,.0f}元"
    
    def format_percentage(self, pct: float) -> str:
        """格式化百分比"""
        sign = "+" if pct > 0 else ""
        return f"{sign}{pct:.1f}%"
    
    def print_trading_opportunity(self, timestamp: datetime, opportunities: List[Dict], 
                                available_funds: float, total_portfolio: float):
        """打印交易机会分析"""
        if self.verbose_level == 0:
            return
            
        num_opportunities = len(opportunities)
        
        print(f"\n🎯 [{self.format_timestamp(timestamp)}] 交易机会分析")
        print(f"├─ 发现机会: {num_opportunities}个 ({', '.join([op['symbol'] for op in opportunities])})")
        print(f"├─ 可用资金: {self.format_amount(available_funds)} (总仓位: {self.format_amount(total_portfolio)})")
        
        if num_opportunities == 1:
            print(f"├─ 分配策略: 单机会全额分配")
        else:
            allocation = available_funds / num_opportunities
            print(f"├─ 分配策略: 多机会平均分配 (每个{self.format_amount(allocation)})")
        
        print(f"└─ 执行结果: 等待开仓...")
    
    def print_position_open(self, timestamp: datetime, symbol: str, price: float, 
                          amount: float, leverage: float, reason: str, 
                          ema_data: Dict = None):
        """打印开仓信息"""
        if self.verbose_level == 0:
            return
            
        self.trade_count += 1
        
        print(f"\n📈 [{symbol}] 开仓详情")
        print(f"├─ 价格: {self.format_price(price)}")
        print(f"├─ 金额: {self.format_amount(amount)} (杠杆: {leverage:.1f}x)")
        
        if self.verbose_level >= 2 and ema_data:
            print(f"├─ 技术条件: EMA21({ema_data.get('ema21', 0):.0f}) > EMA200({ema_data.get('ema200', 0):.0f})")
        
        print(f"└─ 原因: {reason}")
    
    def print_position_close(self, timestamp: datetime, symbol: str, entry_price: float,
                           exit_price: float, pnl_pct: float, reason: str):
        """打印平仓信息"""
        if self.verbose_level == 0:
            return

        # 根据盈亏程度选择emoji
        if pnl_pct > 10:
            pnl_emoji = "🚀"  # 大涨
        elif pnl_pct > 5:
            pnl_emoji = "📈"  # 上涨
        elif pnl_pct > 0:
            pnl_emoji = "📊"  # 小涨
        elif pnl_pct > -5:
            pnl_emoji = "📉"  # 小跌
        elif pnl_pct > -10:
            pnl_emoji = "⬇️"   # 下跌
        else:
            pnl_emoji = "💥"  # 大跌

        print(f"\n{pnl_emoji} [{symbol}] 平仓详情")
        print(f"├─ 入场: {self.format_price(entry_price)} → 出场: {self.format_price(exit_price)}")
        print(f"├─ 收益率: {self.format_percentage(pnl_pct)}")
        print(f"└─ 原因: {reason}")
    
    def print_portfolio_status(self, available_cash: float, total_portfolio: float, 
                             active_positions: int, unrealized_pnl: float = 0):
        """打印组合状态"""
        if self.verbose_level == 0:
            return
            
        print(f"\n💼 当前组合状态")
        print(f"├─ 持仓数: {active_positions}个")
        print(f"├─ 总权益: {self.format_amount(total_portfolio)}")
        print(f"├─ 可用资金: {self.format_amount(available_cash)}")
        
        if total_portfolio > 0:
            position_value = total_portfolio - available_cash
            utilization = position_value / total_portfolio * 100
            print(f"└─ 资金利用率: {utilization:.1f}%")
        else:
            print(f"└─ 资金利用率: 0%")
    
    def print_stage_summary(self, progress_pct: float, start_date: datetime, 
                          current_date: datetime, stats: Dict):
        """打印阶段性总结"""
        if self.verbose_level == 0:
            return
            
        print(f"\n📊 [进度: {progress_pct:.1f}%] 阶段总结 ({start_date.strftime('%Y-%m-%d')} ~ {current_date.strftime('%Y-%m-%d')})")
        print("┌─────────────────────────────────────────────────────────┐")
        print("│ 交易统计                                                │")
        print(f"├─ 总信号: {stats.get('total_signals', 0)}个  │ 成功开仓: {stats.get('successful_entries', 0)}个  │ 资金不足: {stats.get('failed_entries', 0)}个      │")
        print(f"├─ 平仓交易: {stats.get('closed_trades', 0)}个  │ 盈利: {stats.get('profitable_trades', 0)}个      │ 亏损: {stats.get('losing_trades', 0)}个          │")
        print(f"├─ 当前持仓: {stats.get('active_positions', 0)}个  │ 浮盈浮亏: {self.format_percentage(stats.get('unrealized_pnl_pct', 0))}                    │")
        print("└─────────────────────────────────────────────────────────┘")
        print("┌─────────────────────────────────────────────────────────┐")
        print("│ 资金状况                                                │")
        print(f"├─ 初始资金: {self.format_amount(stats.get('initial_capital', 0))}  │ 当前权益: {self.format_amount(stats.get('current_equity', 0))}             │")
        print(f"├─ 可用资金: {self.format_amount(stats.get('available_cash', 0))}   │ 持仓价值: {self.format_amount(stats.get('position_value', 0))}              │")
        print(f"├─ 收益率: {self.format_percentage(stats.get('total_return_pct', 0))}      │ 最大回撤: {self.format_percentage(stats.get('max_drawdown_pct', 0))}                │")
        print("└─────────────────────────────────────────────────────────┘")
    
    def print_progress_simple(self, progress_pct: float, current_time: datetime,
                            event_type: str = None):
        """智能进度显示"""
        if event_type:
            print(f"🕐 [{progress_pct:.1f}%] {self.format_timestamp(current_time)} {event_type}...")
        else:
            # 只在关键里程碑显示（每20%）
            if progress_pct > 0 and progress_pct % 20 == 0:
                print(f"📊 进度: {progress_pct:.1f}% - {self.format_timestamp(current_time)}")

    def print_trading_event_progress(self, progress_pct: float, current_time: datetime,
                                   event_description: str):
        """交易事件进度显示"""
        print(f"🎯 [{progress_pct:.1f}%] {self.format_timestamp(current_time)} - {event_description}")
    
    def print_final_summary(self, stats: Dict, trade_history: List[Dict] = None):
        """打印最终总结"""
        print(f"\n" + "="*70)
        print(f"🎯 回测完成总结")
        print(f"="*70)

        # 基础统计
        print(f"📊 交易统计:")
        print(f"   总交易次数: {stats.get('total_trades', 0)}")
        print(f"   胜率: {self.format_percentage(stats.get('win_rate', 0))}")
        print(f"   总收益率: {self.format_percentage(stats.get('total_return', 0))}")

        # 资金状况
        print(f"\n💰 资金状况:")
        print(f"   初始资金: {self.format_amount(stats.get('initial_capital', 0))}")
        print(f"   最终权益: {self.format_amount(stats.get('final_equity', 0))}")
        print(f"   最大回撤: {self.format_percentage(stats.get('max_drawdown', 0))}")

        # 风险指标
        if stats.get('sharpe_ratio') is not None:
            print(f"   夏普比率: {stats.get('sharpe_ratio', 0):.2f}")

        # 交易分析
        if trade_history:
            profitable_trades = [t for t in trade_history if t.get('action') == '平仓' and t.get('return', 0) > 0]
            losing_trades = [t for t in trade_history if t.get('action') == '平仓' and t.get('return', 0) <= 0]

            if profitable_trades or losing_trades:
                print(f"\n📈 交易分析:")
                if profitable_trades:
                    best_trade = max(profitable_trades, key=lambda x: x.get('return', 0))
                    print(f"   最佳交易: {best_trade['symbol']} {self.format_percentage(best_trade['return'])}")
                    avg_profit = sum(t.get('return', 0) for t in profitable_trades) / len(profitable_trades)
                    print(f"   平均盈利: {self.format_percentage(avg_profit)}")

                if losing_trades:
                    worst_trade = min(losing_trades, key=lambda x: x.get('return', 0))
                    print(f"   最差交易: {worst_trade['symbol']} {self.format_percentage(worst_trade['return'])}")
                    avg_loss = sum(t.get('return', 0) for t in losing_trades) / len(losing_trades)
                    print(f"   平均亏损: {self.format_percentage(avg_loss)}")

        # 信号统计
        print(f"\n🎯 信号统计:")
        print(f"   总信号数: {stats.get('total_signals', 0)}")
        print(f"   成功开仓: {stats.get('successful_entries', 0)}")
        print(f"   资金不足: {stats.get('failed_entries', 0)}")

        # 如果统计数据为0但有交易历史，从交易历史中计算
        if stats.get('total_signals', 0) == 0 and trade_history:
            entry_trades = [t for t in trade_history if t.get('action') == '开仓']
            if entry_trades:
                print(f"   (从交易历史计算: 总信号{len(entry_trades)}个, 成功开仓{len(entry_trades)}个)")

        # 时间分析
        if stats.get('backtest_duration'):
            print(f"\n⏰ 时间分析:")
            print(f"   回测时长: {stats.get('backtest_duration')}")
            if stats.get('avg_holding_period'):
                print(f"   平均持仓: {stats.get('avg_holding_period')}")

        print(f"="*70)

    def save_final_summary_to_file(self, stats: Dict, trade_history: List[Dict] = None,
                                  output_file: str = None):
        """将最终总结保存到文件"""
        if not output_file:
            return

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                # 重定向print输出到文件
                import sys
                original_stdout = sys.stdout
                sys.stdout = f

                # 调用打印方法，输出会写入文件
                self.print_final_summary(stats, trade_history)

                # 添加详细的交易历史
                if trade_history:
                    print(f"\n" + "="*70)
                    print(f"📋 完整交易历史")
                    print(f"="*70)

                    for i, trade in enumerate(trade_history, 1):
                        action_emoji = "📈" if trade.get('action') == '开仓' else "📉"
                        print(f"{i:2d}. {action_emoji} {self.format_timestamp(trade['timestamp'])} "
                              f"{trade['symbol']} {trade['action']}")
                        print(f"    价格: {self.format_price(trade['price'])}")

                        if trade.get('action') == '开仓':
                            print(f"    金额: {self.format_amount(trade.get('amount', 0))}")
                            if trade.get('leverage', 1) > 1:
                                print(f"    杠杆: {trade.get('leverage', 1):.1f}x")
                        else:
                            if 'return' in trade:
                                print(f"    收益率: {self.format_percentage(trade['return'])}")
                        print()

                # 恢复stdout
                sys.stdout = original_stdout

            print(f"\n📄 详细总结已保存到: {output_file}")

        except Exception as e:
            print(f"⚠️ 保存文件失败: {e}")
    
    def print_trade_table(self, trades: List[Dict], max_rows: int = 10):
        """打印交易表格"""
        if self.verbose_level == 0 or not trades:
            return
            
        print(f"\n📋 最近交易记录:")
        print("┌─────────────┬──────────┬──────────┬──────────┬──────────┐")
        print("│ 时间        │ 币种     │ 动作     │ 价格     │ 收益率   │")
        print("├─────────────┼──────────┼──────────┼──────────┼──────────┤")
        
        recent_trades = trades[-max_rows:] if len(trades) > max_rows else trades
        
        for trade in recent_trades:
            time_str = self.format_timestamp(trade['timestamp'])
            symbol = trade['symbol'][:8]  # 限制长度
            action = trade['action']
            price = self.format_price(trade['price'])[:8]
            
            if trade['action'] == '平仓':
                return_str = self.format_percentage(trade.get('return', 0))[:8]
            else:
                return_str = "--"
                
            print(f"│ {time_str:<11} │ {symbol:<8} │ {action:<8} │ {price:<8} │ {return_str:<8} │")
        
        print("└─────────────┴──────────┴──────────┴──────────┴──────────┘")

    def print_performance_chart(self, returns: List[float], width: int = 50):
        """打印简单的收益率图表"""
        if not returns or self.verbose_level == 0:
            return

        print(f"\n📊 收益率走势图:")

        # 计算图表参数
        max_return = max(returns)
        min_return = min(returns)
        range_return = max_return - min_return

        if range_return == 0:
            range_return = 1

        # 绘制图表
        for i, ret in enumerate(returns[-20:]):  # 只显示最近20个点
            # 计算位置
            if range_return > 0:
                pos = int((ret - min_return) / range_return * width)
            else:
                pos = width // 2

            # 绘制条形
            bar = "█" * max(1, pos) if ret >= 0 else "▓" * max(1, abs(pos))
            sign = "+" if ret >= 0 else ""

            print(f"   {i+1:2d}: {bar:<{width}} {sign}{ret:.1f}%")

        print(f"   {'─' * (width + 20)}")
        print(f"   范围: {min_return:.1f}% 至 {max_return:.1f}%")

    def create_time_series_chart(self, trade_history: List[Dict], output_file: str = None):
        """创建时间序列曲线图"""
        if not trade_history:
            return

        try:
            import matplotlib
            matplotlib.use('Agg')  # 使用非交互式后端
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            from datetime import datetime
            import numpy as np

            # 提取数据
            timestamps = []
            cumulative_returns = []
            current_return = 0

            for trade in trade_history:
                if trade.get('action') == '平仓' and 'return' in trade:
                    timestamps.append(trade['timestamp'])
                    current_return += trade['return']
                    cumulative_returns.append(current_return)

            if not timestamps:
                print("⚠️ 没有足够的数据生成图表")
                return

            # 设置中文字体（如果可用）
            try:
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
            except:
                pass

            # 创建图表
            plt.figure(figsize=(12, 8))

            # 主图：累计收益率
            plt.subplot(2, 1, 1)
            plt.plot(timestamps, cumulative_returns, 'b-', linewidth=2, label='Cumulative Returns')
            plt.axhline(y=0, color='r', linestyle='--', alpha=0.5, label='Break-even Line')
            plt.title('Cumulative Returns Time Series', fontsize=14, fontweight='bold')
            plt.ylabel('Cumulative Returns (%)')
            plt.grid(True, alpha=0.3)
            plt.legend()

            # 格式化x轴
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            plt.gca().xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            plt.xticks(rotation=45)

            # 子图：单笔交易收益率
            plt.subplot(2, 1, 2)
            single_returns = [trade['return'] for trade in trade_history if trade.get('action') == '平仓' and 'return' in trade]
            colors = ['green' if r > 0 else 'red' for r in single_returns]

            bars = plt.bar(range(len(single_returns)), single_returns, color=colors, alpha=0.7)
            plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            plt.title('Individual Trade Returns Distribution', fontsize=12, fontweight='bold')
            plt.ylabel('Individual Returns (%)')
            plt.xlabel('Trade Number')
            plt.grid(True, alpha=0.3)

            # 添加数值标签
            for i, (bar, value) in enumerate(zip(bars, single_returns)):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + (0.5 if value > 0 else -1),
                        f'{value:.1f}%', ha='center', va='bottom' if value > 0 else 'top', fontsize=8)

            plt.tight_layout()

            # 保存图表
            if output_file:
                plt.savefig(output_file, dpi=300, bbox_inches='tight')
                print(f"📊 时间序列图表已保存到: {output_file}")

            # 显示图表（如果在交互环境中）
            try:
                plt.show()
            except:
                pass  # 在非交互环境中忽略

            plt.close()

        except ImportError:
            print("⚠️ 需要安装matplotlib来生成图表: pip install matplotlib")
        except Exception as e:
            print(f"⚠️ 生成图表失败: {e}")
