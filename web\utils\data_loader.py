"""
数据加载工具
提供数据加载和预处理功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import sqlite3
from pathlib import Path

from .tbtrade_integration import tbtrade_integration

def load_market_data(symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
    """
    加载市场数据
    
    Args:
        symbol: 交易币种
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    
    Returns:
        包含OHLCV数据的DataFrame
    """
    try:
        return tbtrade_integration.load_symbol_data(symbol, start_date, end_date)
    except Exception as e:
        # 如果加载失败，返回模拟数据
        print(f"加载真实数据失败，使用模拟数据: {e}")
        return generate_mock_data(symbol, start_date, end_date)

def generate_mock_data(symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
    """
    生成模拟市场数据
    
    Args:
        symbol: 交易币种
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        模拟的OHLCV数据DataFrame
    """
    # 设置默认日期范围
    if not end_date:
        end_date = datetime.now().date()
    else:
        end_date = pd.to_datetime(end_date).date()
    
    if not start_date:
        start_date = end_date - timedelta(days=365)
    else:
        start_date = pd.to_datetime(start_date).date()
    
    # 生成日期范围
    dates = pd.date_range(start=start_date, end=end_date, freq='4H')
    
    # 设置初始价格（根据币种设置不同的基础价格）
    base_prices = {
        'BTCUSDT': 45000,
        'ETHUSDT': 2500,
        'ADAUSDT': 0.5,
        'BNBUSDT': 300,
        'SOLUSDT': 100
    }
    
    initial_price = base_prices.get(symbol, 1000)
    
    # 生成价格数据（使用随机游走）
    returns = np.random.normal(0.0001, 0.02, len(dates))  # 小幅随机波动
    prices = [initial_price]
    
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, 0.01))  # 确保价格不为负
    
    # 生成OHLCV数据
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # 生成高低价
        volatility = 0.01  # 1%的波动率
        high = price * (1 + np.random.uniform(0, volatility))
        low = price * (1 - np.random.uniform(0, volatility))
        
        # 生成开盘价和收盘价
        if i == 0:
            open_price = price
        else:
            open_price = prices[i-1]
        
        close_price = price
        
        # 确保OHLC逻辑正确
        high = max(high, open_price, close_price)
        low = min(low, open_price, close_price)
        
        # 生成成交量
        volume = np.random.uniform(1000000, 10000000)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=dates)
    return df

def get_available_symbols() -> List[str]:
    """获取可用的交易币种列表"""
    try:
        return tbtrade_integration.get_available_symbols()
    except Exception as e:
        print(f"获取币种列表失败，使用默认列表: {e}")
        return ["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT", 
                "XRPUSDT", "DOGEUSDT", "AVAXUSDT", "DOTUSDT", "MATICUSDT"]

def get_data_summary() -> Dict[str, Any]:
    """获取数据概览"""
    try:
        return tbtrade_integration.get_data_info()
    except Exception as e:
        print(f"获取数据概览失败: {e}")
        return {
            'error': str(e),
            'total_symbols': 0,
            'databases': [],
            'total_records': 0
        }

def validate_data_quality(df: pd.DataFrame) -> Dict[str, Any]:
    """
    验证数据质量
    
    Args:
        df: 市场数据DataFrame
    
    Returns:
        数据质量报告
    """
    report = {
        'total_records': len(df),
        'date_range': {
            'start': df.index[0].strftime('%Y-%m-%d') if not df.empty else None,
            'end': df.index[-1].strftime('%Y-%m-%d') if not df.empty else None
        },
        'missing_data': {},
        'data_issues': [],
        'quality_score': 0
    }
    
    if df.empty:
        report['data_issues'].append('数据为空')
        return report
    
    # 检查缺失数据
    for col in ['open', 'high', 'low', 'close', 'volume']:
        if col in df.columns:
            missing_count = df[col].isna().sum()
            missing_pct = missing_count / len(df) * 100
            report['missing_data'][col] = {
                'count': int(missing_count),
                'percentage': round(missing_pct, 2)
            }
            
            if missing_pct > 5:
                report['data_issues'].append(f'{col}列缺失数据超过5%')
    
    # 检查OHLC逻辑
    if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
        # 检查高价是否为最高
        high_issues = ((df['high'] < df['open']) | 
                      (df['high'] < df['close']) | 
                      (df['high'] < df['low'])).sum()
        
        # 检查低价是否为最低
        low_issues = ((df['low'] > df['open']) | 
                     (df['low'] > df['close']) | 
                     (df['low'] > df['high'])).sum()
        
        if high_issues > 0:
            report['data_issues'].append(f'发现{high_issues}条高价逻辑错误')
        
        if low_issues > 0:
            report['data_issues'].append(f'发现{low_issues}条低价逻辑错误')
    
    # 检查异常值
    if 'close' in df.columns:
        # 检查价格跳跃
        price_changes = df['close'].pct_change().abs()
        extreme_changes = (price_changes > 0.5).sum()  # 50%以上的价格变化
        
        if extreme_changes > 0:
            report['data_issues'].append(f'发现{extreme_changes}条极端价格变化')
    
    # 计算质量分数
    issues_count = len(report['data_issues'])
    missing_avg = np.mean([v['percentage'] for v in report['missing_data'].values()])
    
    quality_score = 100
    quality_score -= issues_count * 10  # 每个问题扣10分
    quality_score -= missing_avg * 2    # 缺失数据百分比 * 2
    quality_score = max(0, quality_score)
    
    report['quality_score'] = round(quality_score, 1)
    
    return report

def preprocess_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    数据预处理
    
    Args:
        df: 原始数据DataFrame
    
    Returns:
        预处理后的DataFrame
    """
    if df.empty:
        return df
    
    df_processed = df.copy()
    
    # 处理缺失值
    numeric_columns = ['open', 'high', 'low', 'close', 'volume']
    for col in numeric_columns:
        if col in df_processed.columns:
            # 使用前向填充处理缺失值
            df_processed[col] = df_processed[col].fillna(method='ffill')
            # 如果还有缺失值，使用后向填充
            df_processed[col] = df_processed[col].fillna(method='bfill')
    
    # 确保数据类型正确
    for col in numeric_columns:
        if col in df_processed.columns:
            df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce')
    
    # 移除仍然存在缺失值的行
    df_processed = df_processed.dropna()
    
    # 确保时间索引排序
    df_processed = df_processed.sort_index()
    
    # 移除重复的时间索引
    df_processed = df_processed[~df_processed.index.duplicated(keep='first')]
    
    return df_processed
