"""
安全的配置管理模块
使用环境变量管理敏感信息，避免硬编码
"""
import os
import json
from typing import Dict, Any, Optional
from dotenv import load_dotenv
import logging

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器 - 统一管理所有配置项"""
    
    def __init__(self):
        self._config = self._load_config()
        self._validate_required_configs()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置信息"""
        return {
            # Binance API 配置
            'binance': {
                'api_key': os.getenv('BINANCE_API_KEY', ''),
                'api_secret': os.getenv('BINANCE_API_SECRET', ''),
                'price_url': 'https://api.binance.com/api/v3/ticker/price',
                'perp_price_url': 'https://fapi.binance.com/fapi/v1/ticker/price',
                'kline_url': 'https://api.binance.com/api/v3/klines',
                'perp_kline_url': 'https://fapi.binance.com/fapi/v1/klines',
                'hr_url': 'https://fapi.binance.com/fapi/v1/ticker/24hr',
                'oi_url': 'https://fapi.binance.com/futures/data/openInterestHist',
            },
            
            # Discord 配置
            'discord': {
                'bot_token': os.getenv('DISCORD_BOT_TOKEN', ''),
                'channel_private_1': os.getenv('DISCORD_CHANNEL_ID_PRIVATE_1', ''),
                'channel_private_2': os.getenv('DISCORD_CHANNEL_ID_PRIVATE_2', ''),
                'channel_public_1': os.getenv('DISCORD_CHANNEL_ID_PUBLIC_1', ''),
            },
            
            # 代理配置
            'proxy': {
                'http': os.getenv('HTTP_PROXY'),
                'https': os.getenv('HTTPS_PROXY'),
            },
            
            # 邮件配置
            'email': {
                'smtp_server': os.getenv('SMTP_SERVER', 'smtp.gmail.com'),
                'smtp_port': int(os.getenv('SMTP_PORT', '587')),
                'username': os.getenv('EMAIL_USERNAME', ''),
                'password': os.getenv('EMAIL_PASSWORD', ''),
            },
            
            # 交易参数
            'trading': {
                'default_timeframe': os.getenv('DEFAULT_TIMEFRAME', '1h'),
                'default_window_size': int(os.getenv('DEFAULT_WINDOW_SIZE', '60')),
                'default_volume_threshold': int(os.getenv('DEFAULT_VOLUME_THRESHOLD', '300')),
                'default_alerts_limit': int(os.getenv('DEFAULT_ALERTS_LIMIT', '5')),
                'time_gap': '1h',
                'average': 60,
                'threshold': 300,
                'require_limit': 5,
            },
            
            # 系统配置
            'system': {
                'debug': os.getenv('DEBUG', 'false').lower() == 'true',
                'log_level': os.getenv('LOG_LEVEL', 'INFO'),
            }
        }
    
    def _validate_required_configs(self):
        """验证必需的配置项"""
        required_configs = [
            ('binance.api_key', self._config['binance']['api_key']),
            ('binance.api_secret', self._config['binance']['api_secret']),
        ]
        
        missing_configs = []
        for config_name, config_value in required_configs:
            if not config_value:
                missing_configs.append(config_name)
        
        if missing_configs:
            logger.warning(f"缺少以下必需配置: {', '.join(missing_configs)}")
            logger.warning("请设置相应的环境变量或创建 .env 文件")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        例: get('binance.api_key')
        """
        try:
            keys = key.split('.')
            value = self._config
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_proxies(self) -> Optional[Dict[str, str]]:
        """获取代理配置"""
        http_proxy = self._config['proxy']['http']
        https_proxy = self._config['proxy']['https']
        
        if http_proxy or https_proxy:
            return {
                'http': http_proxy,
                'https': https_proxy or http_proxy,
            }
        return None
    
    def is_debug(self) -> bool:
        """是否为调试模式"""
        return self._config['system']['debug']
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        return self._config['system']['log_level']
    
    def get_state_summary(self) -> str:
        """获取配置状态摘要"""
        summary = []
        
        # 检查 Binance API 配置
        binance_ok = bool(self._config['binance']['api_key'] and 
                         self._config['binance']['api_secret'])
        summary.append(f"Binance API: {'✅' if binance_ok else '❌'}")
        
        # 检查 Discord 配置
        discord_ok = bool(self._config['discord']['bot_token'])
        summary.append(f"Discord: {'✅' if discord_ok else '❌'}")
        
        # 检查代理配置
        proxy_configured = bool(self._config['proxy']['http'] or 
                               self._config['proxy']['https'])
        summary.append(f"代理: {'✅' if proxy_configured else '⭕'}")
        
        return f"[{', '.join(summary)}]"


# 全局配置实例
config_manager = ConfigManager()


# 向后兼容的配置变量（逐步迁移）
API_KEY = config_manager.get('binance.api_key')
API_SECRET = config_manager.get('binance.api_secret')
PROXIES = config_manager.get_proxies()

PRICE_URL = config_manager.get('binance.price_url')
PERP_PRICE_URL = config_manager.get('binance.perp_price_url')
KLINE_URL = config_manager.get('binance.kline_url')
PERP_KLINE_URL = config_manager.get('binance.perp_kline_url')
HR_URL = config_manager.get('binance.hr_url')
OI_URL = config_manager.get('binance.oi_url')

DEFAULT_TIMEFRAME = config_manager.get('trading.default_timeframe')
DEFAULT_WINDOW_SIZE = config_manager.get('trading.default_window_size')
DEFAULT_VOLUME_THRESHOLD = config_manager.get('trading.default_volume_threshold')
DEFAULT_ALERTS_LIMIT = config_manager.get('trading.default_alerts_limit')

TIME_GAP = config_manager.get('trading.time_gap')
AVERAGE = config_manager.get('trading.average')
THRESHOLD = config_manager.get('trading.threshold')
REQUIRE_LIMIT = config_manager.get('trading.require_limit') 