"""
状态管理模块
统一管理应用状态，替代全局变量
"""
import threading
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime
from src.logger_config import get_logger

logger = get_logger(__name__)


@dataclass
class TradingState:
    """交易状态数据类"""
    mux_lock: bool = False
    trace_flag: bool = False
    unfinished_tokens: List[str] = field(default_factory=list)
    finished_num: int = 0
    unfinished_num: int = 0
    abnormal_vol_tokens: List[Dict[str, Any]] = field(default_factory=list)
    getklines_flag: bool = False


@dataclass
class OpenInterestState:
    """持仓量状态数据类"""
    openinterest: List[Any] = field(default_factory=list)
    all_exist: List[Any] = field(default_factory=list)
    abnormal_oi: List[Any] = field(default_factory=list)
    closeprice_1h: List[float] = field(default_factory=list)
    
    # OI级别相关
    oi_level_list: List[Any] = field(default_factory=list)
    oi_level_500: List[Any] = field(default_factory=list)
    oi_level_1000: List[Any] = field(default_factory=list)
    oi_level_2000: List[Any] = field(default_factory=list)
    oi_level_5000: List[Any] = field(default_factory=list)
    oi_level_10000: List[Any] = field(default_factory=list)
    oi_level_nolimit: List[Any] = field(default_factory=list)
    oi_level: List[Any] = field(default_factory=list)
    oilevel_flag: bool = False


@dataclass
class EMATrendState:
    """EMA趋势状态数据类"""
    ematrend_flag: bool = False
    if_traceema: bool = False
    trackema_tokens: List[str] = field(default_factory=list)
    ematrend_list: List[Any] = field(default_factory=list)


@dataclass
class ApplicationState:
    """应用程序总状态"""
    trace_list: List[Any] = field(default_factory=list)
    trading: TradingState = field(default_factory=TradingState)
    open_interest: OpenInterestState = field(default_factory=OpenInterestState)
    ema_trend: EMATrendState = field(default_factory=EMATrendState)


class StateManager:
    """状态管理器 - 线程安全的状态管理"""
    
    def __init__(self):
        self._state = ApplicationState()
        self._lock = threading.RLock()  # 可重入锁
        logger.info("状态管理器初始化完成")
    
    def get_state(self) -> ApplicationState:
        """获取当前状态（只读）"""
        with self._lock:
            return self._state
    
    # 交易状态管理方法
    def set_mux_lock(self, value: bool):
        """设置互斥锁状态"""
        with self._lock:
            self._state.trading.mux_lock = value
            logger.debug(f"设置 mux_lock: {value}")
    
    def get_mux_lock(self) -> bool:
        """获取互斥锁状态"""
        with self._lock:
            return self._state.trading.mux_lock
    
    def set_trace_flag(self, value: bool):
        """设置跟踪标志"""
        with self._lock:
            self._state.trading.trace_flag = value
            logger.debug(f"设置 trace_flag: {value}")
    
    def get_trace_flag(self) -> bool:
        """获取跟踪标志"""
        with self._lock:
            return self._state.trading.trace_flag
    
    def add_unfinished_token(self, token: str):
        """添加未完成的代币"""
        with self._lock:
            if token not in self._state.trading.unfinished_tokens:
                self._state.trading.unfinished_tokens.append(token)
                self._state.trading.unfinished_num = len(self._state.trading.unfinished_tokens)
                logger.debug(f"添加未完成代币: {token}")
    
    def remove_unfinished_token(self, token: str):
        """移除未完成的代币"""
        with self._lock:
            if token in self._state.trading.unfinished_tokens:
                self._state.trading.unfinished_tokens.remove(token)
                self._state.trading.unfinished_num = len(self._state.trading.unfinished_tokens)
                logger.debug(f"移除未完成代币: {token}")
    
    def get_unfinished_tokens(self) -> List[str]:
        """获取未完成的代币列表"""
        with self._lock:
            return self._state.trading.unfinished_tokens.copy()
    
    def increment_finished_num(self):
        """增加完成数量"""
        with self._lock:
            self._state.trading.finished_num += 1
            logger.debug(f"完成数量增加至: {self._state.trading.finished_num}")
    
    def get_finished_num(self) -> int:
        """获取完成数量"""
        with self._lock:
            return self._state.trading.finished_num
    
    def add_abnormal_vol_token(self, token_data: Dict[str, Any]):
        """添加异常交易量代币"""
        with self._lock:
            self._state.trading.abnormal_vol_tokens.append(token_data)
            logger.info(f"添加异常交易量代币: {token_data.get('token', 'Unknown')}")
    
    def get_abnormal_vol_tokens(self) -> List[Dict[str, Any]]:
        """获取异常交易量代币列表"""
        with self._lock:
            return self._state.trading.abnormal_vol_tokens.copy()
    
    def clear_abnormal_vol_tokens(self):
        """清空异常交易量代币列表"""
        with self._lock:
            self._state.trading.abnormal_vol_tokens.clear()
            logger.debug("清空异常交易量代币列表")
    
    # 持仓量状态管理方法
    def set_openinterest(self, data: List[Any]):
        """设置持仓量数据"""
        with self._lock:
            self._state.open_interest.openinterest = data
            logger.debug(f"设置持仓量数据，长度: {len(data)}")
    
    def get_openinterest(self) -> List[Any]:
        """获取持仓量数据"""
        with self._lock:
            return self._state.open_interest.openinterest.copy()
    
    def set_abnormal_oi(self, data: List[Any]):
        """设置异常持仓量数据"""
        with self._lock:
            self._state.open_interest.abnormal_oi = data
            logger.debug(f"设置异常持仓量数据，长度: {len(data)}")
    
    def get_abnormal_oi(self) -> List[Any]:
        """获取异常持仓量数据"""
        with self._lock:
            return self._state.open_interest.abnormal_oi.copy()
    
    def set_closeprice_1h(self, prices: List[float]):
        """设置1小时收盘价数据"""
        with self._lock:
            self._state.open_interest.closeprice_1h = prices
            logger.debug(f"设置1小时收盘价数据，长度: {len(prices)}")
    
    def get_closeprice_1h(self) -> List[float]:
        """获取1小时收盘价数据"""
        with self._lock:
            return self._state.open_interest.closeprice_1h.copy()
    
    # EMA趋势状态管理方法
    def set_ematrend_flag(self, value: bool):
        """设置EMA趋势标志"""
        with self._lock:
            self._state.ema_trend.ematrend_flag = value
            logger.debug(f"设置 ematrend_flag: {value}")
    
    def get_ematrend_flag(self) -> bool:
        """获取EMA趋势标志"""
        with self._lock:
            return self._state.ema_trend.ematrend_flag
    
    def set_if_traceema(self, value: bool):
        """设置是否跟踪EMA"""
        with self._lock:
            self._state.ema_trend.if_traceema = value
            logger.debug(f"设置 if_traceema: {value}")
    
    def get_if_traceema(self) -> bool:
        """获取是否跟踪EMA"""
        with self._lock:
            return self._state.ema_trend.if_traceema
    
    def add_trackema_token(self, token: str):
        """添加EMA跟踪代币"""
        with self._lock:
            if token not in self._state.ema_trend.trackema_tokens:
                self._state.ema_trend.trackema_tokens.append(token)
                logger.debug(f"添加EMA跟踪代币: {token}")
    
    def get_trackema_tokens(self) -> List[str]:
        """获取EMA跟踪代币列表"""
        with self._lock:
            return self._state.ema_trend.trackema_tokens.copy()
    
    def set_ematrend_list(self, data: List[Any]):
        """设置EMA趋势列表"""
        with self._lock:
            self._state.ema_trend.ematrend_list = data
            logger.debug(f"设置EMA趋势列表，长度: {len(data)}")
    
    def get_ematrend_list(self) -> List[Any]:
        """获取EMA趋势列表"""
        with self._lock:
            return self._state.ema_trend.ematrend_list.copy()
    
    # 应用级别状态管理
    def add_to_trace_list(self, item: Any):
        """添加到跟踪列表"""
        with self._lock:
            self._state.trace_list.append(item)
            logger.debug(f"添加到跟踪列表: {item}")
    
    def get_trace_list(self) -> List[Any]:
        """获取跟踪列表"""
        with self._lock:
            return self._state.trace_list.copy()
    
    def clear_trace_list(self):
        """清空跟踪列表"""
        with self._lock:
            self._state.trace_list.clear()
            logger.debug("清空跟踪列表")
    
    # 状态重置方法
    def reset_trading_state(self):
        """重置交易状态"""
        with self._lock:
            self._state.trading = TradingState()
            logger.info("重置交易状态")
    
    def reset_open_interest_state(self):
        """重置持仓量状态"""
        with self._lock:
            self._state.open_interest = OpenInterestState()
            logger.info("重置持仓量状态")
    
    def reset_ema_trend_state(self):
        """重置EMA趋势状态"""
        with self._lock:
            self._state.ema_trend = EMATrendState()
            logger.info("重置EMA趋势状态")
    
    def reset_all_state(self):
        """重置所有状态"""
        with self._lock:
            self._state = ApplicationState()
            logger.info("重置所有状态")
    
    # 状态报告
    def get_state_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        with self._lock:
            return {
                "timestamp": datetime.now().isoformat(),
                "trading": {
                    "mux_lock": self._state.trading.mux_lock,
                    "trace_flag": self._state.trading.trace_flag,
                    "finished_num": self._state.trading.finished_num,
                    "unfinished_num": self._state.trading.unfinished_num,
                    "abnormal_vol_tokens_count": len(self._state.trading.abnormal_vol_tokens),
                },
                "open_interest": {
                    "openinterest_count": len(self._state.open_interest.openinterest),
                    "abnormal_oi_count": len(self._state.open_interest.abnormal_oi),
                    "oilevel_flag": self._state.open_interest.oilevel_flag,
                },
                "ema_trend": {
                    "ematrend_flag": self._state.ema_trend.ematrend_flag,
                    "if_traceema": self._state.ema_trend.if_traceema,
                    "trackema_tokens_count": len(self._state.ema_trend.trackema_tokens),
                    "ematrend_list_count": len(self._state.ema_trend.ematrend_list),
                },
                "trace_list_count": len(self._state.trace_list),
            }


# 全局状态管理器实例
state_manager = StateManager() 