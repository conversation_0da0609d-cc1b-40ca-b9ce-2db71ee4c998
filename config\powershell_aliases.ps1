# Linux风格的PowerShell别名配置文件
# 使用方法：在PowerShell配置文件中添加 . ./config/powershell_aliases.ps1

# 文件操作
Set-Alias -Name ll -Value Get-ChildItem
Set-Alias -Name la -Value Get-ChildItem
function ls { Get-ChildItem @args }
function grep { Select-String @args }
function cat { Get-Content @args }
function head { Get-Content @args -Head 10 }
function tail { Get-Content @args -Tail 10 }
function touch { New-Item @args -ItemType File }
function mkdir { New-Item @args -ItemType Directory }

# 系统操作
function ps { Get-Process @args }
function kill { Stop-Process @args }
function which { Get-Command @args }
function pwd { Get-Location }
function clear { Clear-Host }

# Git操作（如果有Git）
function gst { git status }
function gco { git checkout @args }
function gad { git add @args }
function gcm { git commit -m @args }
function gps { git push }
function gpl { git pull }

# Python/uv相关
function activate { .\.venv\Scripts\Activate.ps1 }
function deactivate { deactivate }

Write-Host "Linux风格别名已加载！" -ForegroundColor Green
Write-Host "可用命令: ll, la, ls, grep, cat, head, tail, touch, mkdir, ps, kill, which, pwd, clear" -ForegroundColor Yellow
Write-Host "           gst, gco, gad, gcm, gps, gpl (Git), activate, deactivate (Python)" -ForegroundColor Yellow 