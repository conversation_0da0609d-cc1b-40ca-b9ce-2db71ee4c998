"""
表格组件模块
提供各种数据表格的Streamlit组件
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime

def create_data_table(data: pd.DataFrame, title: str = "数据表格", height: int = 400) -> None:
    """
    创建数据表格

    Args:
        data: 数据DataFrame
        title: 表格标题
        height: 表格高度
    """
    st.subheader(title)

    if data.empty:
        st.info("暂无数据")
        return

    # 显示表格
    st.dataframe(data, use_container_width=True, height=height)

def display_trades_table(trades_df: pd.DataFrame, title: str = "交易记录") -> None:
    """
    显示交易记录表格
    
    Args:
        trades_df: 交易记录DataFrame
        title: 表格标题
    """
    st.subheader(title)
    
    if trades_df.empty:
        st.info("暂无交易记录")
        return
    
    # 格式化数据
    display_df = trades_df.copy()
    
    # 格式化数值列
    if 'entry_price' in display_df.columns:
        display_df['entry_price'] = display_df['entry_price'].apply(lambda x: f"${x:,.2f}")
    if 'exit_price' in display_df.columns:
        display_df['exit_price'] = display_df['exit_price'].apply(lambda x: f"${x:,.2f}")
    if 'pnl' in display_df.columns:
        display_df['pnl'] = display_df['pnl'].apply(lambda x: f"¥{x:,.2f}")
    if 'return_pct' in display_df.columns:
        display_df['return_pct'] = display_df['return_pct'].apply(lambda x: f"{x:.2%}")
    
    # 添加颜色样式
    def highlight_pnl(val):
        if isinstance(val, str) and val.startswith('¥'):
            value = float(val.replace('¥', '').replace(',', ''))
            if value > 0:
                return 'color: green'
            elif value < 0:
                return 'color: red'
        return ''
    
    # 应用样式
    styled_df = display_df.style.applymap(highlight_pnl, subset=['pnl'] if 'pnl' in display_df.columns else [])
    
    # 显示表格
    st.dataframe(
        styled_df,
        use_container_width=True,
        height=400
    )
    
    # 显示统计信息
    if not trades_df.empty:
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_trades = len(trades_df)
            st.metric("总交易数", total_trades)
        
        with col2:
            if 'pnl' in trades_df.columns:
                winning_trades = len(trades_df[trades_df['pnl'] > 0])
                win_rate = winning_trades / total_trades if total_trades > 0 else 0
                st.metric("胜率", f"{win_rate:.1%}")
        
        with col3:
            if 'pnl' in trades_df.columns:
                total_pnl = trades_df['pnl'].sum()
                st.metric("总盈亏", f"¥{total_pnl:,.2f}")
        
        with col4:
            if 'return_pct' in trades_df.columns:
                avg_return = trades_df['return_pct'].mean()
                st.metric("平均收益率", f"{avg_return:.2%}")

def display_positions_table(positions_df: pd.DataFrame, title: str = "当前持仓") -> None:
    """
    显示持仓表格
    
    Args:
        positions_df: 持仓DataFrame
        title: 表格标题
    """
    st.subheader(title)
    
    if positions_df.empty:
        st.info("当前无持仓")
        return
    
    # 格式化数据
    display_df = positions_df.copy()
    
    # 格式化数值列
    numeric_columns = ['entry_price', 'current_price', 'quantity', 'market_value', 'unrealized_pnl']
    for col in numeric_columns:
        if col in display_df.columns:
            if col in ['entry_price', 'current_price']:
                display_df[col] = display_df[col].apply(lambda x: f"${x:,.4f}")
            elif col in ['market_value', 'unrealized_pnl']:
                display_df[col] = display_df[col].apply(lambda x: f"¥{x:,.2f}")
            else:
                display_df[col] = display_df[col].apply(lambda x: f"{x:,.4f}")
    
    # 添加收益率列
    if 'unrealized_pnl' in positions_df.columns and 'market_value' in positions_df.columns:
        display_df['return_pct'] = positions_df.apply(
            lambda row: f"{(row['unrealized_pnl'] / (row['market_value'] - row['unrealized_pnl'])):.2%}",
            axis=1
        )
    
    # 颜色样式
    def highlight_pnl(val):
        if isinstance(val, str):
            if val.startswith('¥'):
                value = float(val.replace('¥', '').replace(',', ''))
                return 'color: green' if value > 0 else 'color: red' if value < 0 else ''
            elif val.endswith('%'):
                value = float(val.replace('%', ''))
                return 'color: green' if value > 0 else 'color: red' if value < 0 else ''
        return ''
    
    # 应用样式
    style_columns = []
    if 'unrealized_pnl' in display_df.columns:
        style_columns.append('unrealized_pnl')
    if 'return_pct' in display_df.columns:
        style_columns.append('return_pct')
    
    if style_columns:
        styled_df = display_df.style.applymap(highlight_pnl, subset=style_columns)
    else:
        styled_df = display_df
    
    # 显示表格
    st.dataframe(
        styled_df,
        use_container_width=True,
        height=300
    )

def display_performance_summary(performance_data: Dict, title: str = "绩效摘要") -> None:
    """
    显示绩效摘要表格
    
    Args:
        performance_data: 绩效数据字典
        title: 表格标题
    """
    st.subheader(title)
    
    # 创建绩效数据DataFrame
    metrics = []
    for key, value in performance_data.items():
        if isinstance(value, (int, float)):
            if key in ['总收益率', '年化收益率', '最大回撤', '胜率']:
                formatted_value = f"{value:.2%}"
            elif key in ['夏普比率', '索提诺比率', 'Calmar比率']:
                formatted_value = f"{value:.3f}"
            elif key in ['总盈亏', '最大单笔盈利', '最大单笔亏损']:
                formatted_value = f"¥{value:,.2f}"
            else:
                formatted_value = f"{value:,.0f}" if value >= 1 else f"{value:.4f}"
        else:
            formatted_value = str(value)
        
        metrics.append({"指标": key, "数值": formatted_value})
    
    metrics_df = pd.DataFrame(metrics)
    
    # 显示表格
    st.dataframe(
        metrics_df,
        use_container_width=True,
        hide_index=True,
        height=400
    )

def display_symbol_performance_table(symbol_data: Dict, title: str = "币种表现") -> None:
    """
    显示币种表现表格
    
    Args:
        symbol_data: 币种数据字典
        title: 表格标题
    """
    st.subheader(title)
    
    if not symbol_data:
        st.info("暂无币种数据")
        return
    
    # 转换为DataFrame
    rows = []
    for symbol, data in symbol_data.items():
        row = {"币种": symbol}
        row.update(data)
        rows.append(row)
    
    df = pd.DataFrame(rows)
    
    # 格式化数值列
    if '收益率' in df.columns:
        df['收益率'] = df['收益率'].apply(lambda x: f"{x:.2%}")
    if '交易次数' in df.columns:
        df['交易次数'] = df['交易次数'].astype(int)
    if '胜率' in df.columns:
        df['胜率'] = df['胜率'].apply(lambda x: f"{x:.1%}")
    if '总盈亏' in df.columns:
        df['总盈亏'] = df['总盈亏'].apply(lambda x: f"¥{x:,.2f}")
    
    # 颜色样式
    def highlight_performance(val):
        if isinstance(val, str):
            if val.endswith('%') and val != '0.0%':
                value = float(val.replace('%', ''))
                return 'color: green' if value > 0 else 'color: red'
            elif val.startswith('¥'):
                value = float(val.replace('¥', '').replace(',', ''))
                return 'color: green' if value > 0 else 'color: red' if value < 0 else ''
        return ''
    
    # 应用样式
    style_columns = []
    for col in ['收益率', '总盈亏']:
        if col in df.columns:
            style_columns.append(col)
    
    if style_columns:
        styled_df = df.style.applymap(highlight_performance, subset=style_columns)
    else:
        styled_df = df
    
    # 显示表格
    st.dataframe(
        styled_df,
        use_container_width=True,
        hide_index=True,
        height=300
    )

def create_data_table_with_filters(df: pd.DataFrame, 
                                 title: str = "数据表格",
                                 filterable_columns: Optional[List[str]] = None) -> pd.DataFrame:
    """
    创建带过滤功能的数据表格
    
    Args:
        df: 数据DataFrame
        title: 表格标题
        filterable_columns: 可过滤的列名列表
    
    Returns:
        过滤后的DataFrame
    """
    st.subheader(title)
    
    if df.empty:
        st.info("暂无数据")
        return df
    
    # 创建过滤器
    if filterable_columns:
        with st.expander("🔍 数据过滤器", expanded=False):
            filters = {}
            
            for col in filterable_columns:
                if col in df.columns:
                    if df[col].dtype in ['object', 'string']:
                        # 字符串列使用多选框
                        unique_values = df[col].unique().tolist()
                        selected_values = st.multiselect(
                            f"选择 {col}",
                            options=unique_values,
                            default=unique_values,
                            key=f"filter_{col}"
                        )
                        if selected_values:
                            filters[col] = selected_values
                    
                    elif df[col].dtype in ['int64', 'float64']:
                        # 数值列使用滑块
                        min_val = float(df[col].min())
                        max_val = float(df[col].max())
                        if min_val != max_val:
                            selected_range = st.slider(
                                f"{col} 范围",
                                min_value=min_val,
                                max_value=max_val,
                                value=(min_val, max_val),
                                key=f"filter_{col}"
                            )
                            filters[col] = selected_range
            
            # 应用过滤器
            filtered_df = df.copy()
            for col, filter_value in filters.items():
                if isinstance(filter_value, list):
                    filtered_df = filtered_df[filtered_df[col].isin(filter_value)]
                elif isinstance(filter_value, tuple):
                    filtered_df = filtered_df[
                        (filtered_df[col] >= filter_value[0]) & 
                        (filtered_df[col] <= filter_value[1])
                    ]
    else:
        filtered_df = df
    
    # 显示过滤后的数据
    st.dataframe(
        filtered_df,
        use_container_width=True,
        height=400
    )
    
    # 显示数据统计
    st.caption(f"显示 {len(filtered_df)} / {len(df)} 条记录")
    
    return filtered_df
