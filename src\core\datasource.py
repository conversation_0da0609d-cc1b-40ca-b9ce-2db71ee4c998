from abc import ABC, abstractmethod
from typing import Dict, List
import pandas as pd

class BaseDataSource(ABC):
    """数据源抽象基类"""
    
    @abstractmethod
    def __init__(self, config: Dict):
        """初始化数据源"""
        pass
    
    @abstractmethod
    def fetch_klines(self, 
                    symbol: str, 
                    interval: str, 
                    start_time: int = None,
                    end_time: int = None) -> pd.DataFrame:
        """
        获取K线数据
        :param symbol: 交易对符号 (e.g. BTCUSDT)
        :param interval: K线间隔 (e.g. 1m, 5m, 1h)
        :param start_time: 起始时间戳（毫秒）
        :param end_time: 结束时间戳（毫秒）
        :return: 标准化DataFrame格式的K线数据
        """
        pass
    
    @classmethod
    @abstractmethod
    def get_supported_intervals(cls) -> List[str]:
        """获取支持的K线间隔列表"""
        pass
    
    @staticmethod
    def standardize_klines(df: pd.DataFrame) -> pd.DataFrame:
        """标准化K线数据格式"""
        required_columns = [
            'timestamp', 'open', 'high', 'low', 
            'close', 'volume', 'close_time',
            'quote_volume', 'count', 'taker_buy_volume',
            'taker_buy_quote_volume', 'ignore'
        ]
        # 数据标准化处理逻辑...
        return df[required_columns]