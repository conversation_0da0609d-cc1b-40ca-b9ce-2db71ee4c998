[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "tradeapi-alert"
version = "0.1.0"
description = "交易API告警系统 - 监控交易量异常、价格差异和技术指标"
readme = "README.md"
requires-python = ">=3.8"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

dependencies = [
    "requests>=2.28.0",
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "python-binance>=1.0.16",
    "discord.py>=2.0.0",
    "flask>=2.0.0",
    "python-dotenv>=0.19.0",
    "schedule>=1.1.0",
    "websocket-client>=1.3.0",
    "ccxt>=2.0.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=0.991",
]

[project.urls]
Homepage = "https://github.com/yourusername/tradeapi-alert"
Repository = "https://github.com/yourusername/tradeapi-alert.git"
Issues = "https://github.com/yourusername/tradeapi-alert/issues"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.hatch.build.targets.wheel]
packages = ["src"]

[project.scripts]
tradeapi-alert = "src.main:main" 