# src/app.py

"""
交易机器人主应用程序
Trading Bot Main Application

这是重构后的主应用程序入口，使用模块化设计替代原来的单体架构。
"""

import multiprocessing
import json
import time
from datetime import datetime

import src.config as config
from src.strategies.volume_strategy import VolumeAnomalyDetector
from src.strategies.oi_strategy import OIAnomalyDetector
from src.strategies.ema_strategy import EMATracker, EMATrendProcessor
from src.services.notification_service import NotificationService
from src.getCoinPrice import get_binance_all_coin


class TradingBot:
    """交易机器人主类"""
    
    def __init__(self):
        """初始化交易机器人"""
        self.notification_service = NotificationService()
        self.volume_detector = VolumeAnomalyDetector()
        self.oi_detector = OIAnomalyDetector()
        
        # 数据持久化路径
        self.trace_list_file = "./trade_data/tracelist.json"
        self.oi_level_file = "./trade_data/oilevel.json"
        self.ema_trend_file = "./trade_data/ematrend_list.json"
    
    def load_persistent_data(self):
        """加载持久化数据"""
        try:
            # 加载交易量检测的追踪列表
            with open(self.trace_list_file, "r") as file:
                self.volume_detector.trace_list = json.load(file)
        except (FileNotFoundError, json.JSONDecodeError):
            self.volume_detector.trace_list = []
        
        try:
            # 加载持仓量级别数据
            with open(self.oi_level_file, "r") as file:
                self.oi_detector.oi_level = json.load(file)
        except (FileNotFoundError, json.JSONDecodeError):
            self.oi_detector.oi_level = []
    
    def save_persistent_data(self):
        """保存持久化数据"""
        try:
            # 保存交易量检测的追踪列表
            with open(self.trace_list_file, "w") as file:
                json.dump(self.volume_detector.trace_list, file, indent=4)
        except Exception as e:
            print(f"保存追踪列表失败: {e}")
        
        try:
            # 保存持仓量级别数据（如果有更新）
            if self.oi_detector.oi_level_flag:
                with open(self.oi_level_file, "w") as file:
                    json.dump(self.oi_detector.oi_level, file, indent=4)
                self.oi_detector.oi_level_flag = False
        except Exception as e:
            print(f"保存持仓量级别数据失败: {e}")
    
    def is_current_to_get_klines(self, time_gap, is_test=False):
        """
        检查当前时间是否适合获取K线数据
        
        Args:
            time_gap (str): 时间间隔 ("1h", "4h", etc.)
            is_test (bool): 是否为测试模式
            
        Returns:
            tuple: (是否准备就绪, 当前分钟)
        """
        if is_test:
            return True, 0
        
        current_time = datetime.now()
        current_minute = current_time.minute
        
        if time_gap == "1h":
            # 每小时的第1-3分钟执行
            return 1 <= current_minute <= 3, current_minute
        elif time_gap == "4h":
            # 每4小时的第1-3分钟执行（0, 4, 8, 12, 16, 20点）
            current_hour = current_time.hour
            return (current_hour % 4 == 0) and (1 <= current_minute <= 3), current_minute
        
        return False, current_minute
    
    def run_volume_oi_detection(self, time_gap, window_size, threshold, alert_limit):
        """运行交易量和持仓量异常检测"""
        while True:
            try:
                print("开始执行交易量和持仓量异常检测...")
                
                # 检查是否到了执行时间
                is_ready, current_minute = self.is_current_to_get_klines(time_gap)
                if not is_ready:
                    time.sleep(20)
                    continue
                
                start_time = time.time()
                
                # 加载持久化数据
                self.load_persistent_data()
                
                # 重置检测状态
                self.volume_detector.reset_state()
                self.oi_detector.reset_state()
                
                # 获取所有币种
                all_coins = get_binance_all_coin()
                print(f"获取到 {len(all_coins)} 个币种")
                
                # 1. 执行交易量异常检测
                print("开始交易量异常检测...")
                self.volume_detector.multi_thread_detect(
                    all_coins, time_gap, window_size, threshold, None
                )
                print(f"交易量检测完成，处理了 {self.volume_detector.finished_count} 个币种")
                
                # 重试失败的币种
                if self.volume_detector.unfinished_tokens:
                    print(f"重试 {len(self.volume_detector.unfinished_tokens)} 个失败的币种...")
                    self.volume_detector.multi_thread_detect(
                        self.volume_detector.unfinished_tokens, time_gap, window_size, threshold, None
                    )
                
                # 2. 执行持仓量异常检测
                print("开始持仓量异常检测...")
                self.oi_detector.multi_thread_trace_abnormal_oi(all_coins)
                
                # 重试失败的币种
                if self.oi_detector.unfinished_tokens:
                    print(f"重试 {len(self.oi_detector.unfinished_tokens)} 个失败的币种...")
                    self.oi_detector.multi_thread_trace_abnormal_oi(
                        self.oi_detector.unfinished_tokens
                    )
                
                # 3. 处理和发送结果
                result_messages = []
                
                # 处理交易量异常结果
                if self.volume_detector.abnormal_tokens:
                    volume_msg = self.volume_detector.filter_abnormal_volume(
                        self.volume_detector.abnormal_tokens, alert_limit
                    )
                    result_messages.append(volume_msg)
                    print(f"发现 {len(self.volume_detector.abnormal_tokens)} 个交易量异常币种")
                
                # 处理持仓量异常结果
                if self.oi_detector.abnormal_oi:
                    oi_msg = self.oi_detector.filter_abnormal_openinterest(
                        self.oi_detector.abnormal_oi, alert_limit
                    )
                    result_messages.append(oi_msg)
                    print(f"发现 {len(self.oi_detector.abnormal_oi)} 个持仓量异常币种")
                
                # 处理持仓量级别结果
                if self.oi_detector.oi_level_list:
                    oi_level_msg = self.oi_detector.filter_oi_level(
                        self.oi_detector.oi_level_list, alert_limit
                    )
                    result_messages.append(oi_level_msg)
                    print(f"发现 {len(self.oi_detector.oi_level_list)} 个持仓量级别异常币种")
                
                # 发送通知
                if result_messages:
                    final_message = "\n".join(result_messages)
                    self.notification_service.send_alert(
                        "交易异常检测警报",
                        final_message,
                        send_email=True,
                        send_discord=True
                    )
                    print("警报通知已发送")
                
                # 保存持久化数据
                self.save_persistent_data()
                
                end_time = time.time()
                print(f"本轮检测完成，用时 {end_time - start_time:.2f} 秒")
                
            except Exception as e:
                error_msg = f"交易量和持仓量检测过程出错: {e}"
                print(error_msg)
                self.notification_service.send_error_alert(error_msg, "交易检测模块")
                time.sleep(60)  # 出错后等待1分钟再重试
    
    def run_ema_trend_detection(self):
        """运行EMA趋势检测"""
        while True:
            try:
                print("开始执行EMA趋势检测...")
                
                time_gap = "4h"
                is_ready, _ = self.is_current_to_get_klines(time_gap)
                
                if is_ready:
                    tracker = EMATracker(self.ema_trend_file)
                    processor = EMATrendProcessor(tracker)
                    all_tokens = get_binance_all_coin()
                    
                    print(f"开始处理 {len(all_tokens)} 个币种的EMA趋势...")
                    processor.process(all_tokens, time_gap)
                    
                    # 保存EMA追踪数据
                    tracker.save_to_file()
                    print("EMA趋势检测完成")
                else:
                    time.sleep(60)  # 如果不是执行时间，等待1分钟
                    
            except Exception as e:
                error_msg = f"EMA趋势检测过程出错: {e}"
                print(error_msg)
                self.notification_service.send_error_alert(error_msg, "EMA趋势模块")
                time.sleep(60)


def main():
    """主函数 - 启动多进程交易机器人"""
    print("交易机器人启动中...")
    
    # 创建交易机器人实例
    bot = TradingBot()
    
    # 创建进程列表
    processes = []
    
    # 启动交易量和持仓量检测进程
    volume_oi_process = multiprocessing.Process(
        target=bot.run_volume_oi_detection,
        args=(
            config.DEFAULT_TIMEFRAME,
            config.DEFAULT_WINDOW_SIZE,
            config.DEFAULT_VOLUME_THRESHOLD,
            config.DEFAULT_ALERTS_LIMIT,
        )
    )
    processes.append(volume_oi_process)
    volume_oi_process.start()
    print("交易量和持仓量检测进程已启动")
    
    # 启动EMA趋势检测进程
    ema_process = multiprocessing.Process(target=bot.run_ema_trend_detection)
    processes.append(ema_process)
    ema_process.start()
    print("EMA趋势检测进程已启动")
    
    try:
        # 等待所有进程完成
        for process in processes:
            process.join()
    except KeyboardInterrupt:
        print("接收到中断信号，正在关闭所有进程...")
        for process in processes:
            process.terminate()
            process.join()
        print("所有进程已关闭")


if __name__ == "__main__":
    main() 