# 4小时K线自动监控系统使用说明

## 📋 系统概述

`auto_4h_monitor.py` 是一个智能的4小时K线自动监控系统，具有以下特点：

- **复用现有功能**：直接使用 `historical_data_fetcher.py` 的所有功能，避免重复代码
- **配置文件驱动**：通过 `config/data_fetcher_config.json` 配置所有参数
- **双模式运行**：支持实时监控和历史数据验证
- **智能交易对管理**：根据数据库现有数据和交易量自动选择监控交易对

## 🎯 核心功能

### 1. 实时监控模式
- 在每个4小时K线切换时自动更新数据
- 运行策略验证并生成交易信号
- 智能选择监控的交易对

### 2. 历史验证模式
- 基于指定时间段的历史数据进行策略验证
- 支持回测和策略优化
- 生成详细的验证报告

## ⚙️ 配置文件说明

### 默认配置 (`config/data_fetcher_config.json`)

```json
{
  "time_range": {
    "days": 365,           // 获取1年历史数据
    "time_label": "1year"
  },
  "symbol_selection": {
    "mode": "top_n",       // 获取交易量前N个交易对
    "top_n": 100,          // 前100个交易对
    "min_volume": 0        // 无最小交易量限制
  },
  "kline_settings": {
    "interval": "4h"       // 4小时K线
  },
  "fetcher_settings": {
    "use_proxy": true,     // 使用代理
    "proxy_port": 6754,    // 代理端口
    "output_dir": "./data"
  }
}
```

### 预设配置

#### 实时更新预设 (`real_time_update`)
- 时间范围：最近1天
- 交易对：前100个
- 用途：4小时K线切换时的增量更新

#### 历史验证预设 (`historical_validation`)
- 时间范围：1年
- 交易对：前100个
- 用途：历史数据验证和回测

## 🚀 使用方法

### 1. 启动实时监控

```bash
python auto_4h_monitor.py
```

系统将：
- 自动检测4小时K线切换时间
- 在每个K线结束后更新数据
- 运行策略验证
- 生成交易信号

### 2. 历史数据验证

```python
from auto_4h_monitor import Auto4HMonitor

async def run_validation():
    monitor = Auto4HMonitor()
    await monitor.initialize()
    
    # 验证指定日期范围
    success = await monitor.run_historical_validation("2025-06-01", "2025-06-28")
    
    # 或使用预设配置
    success = await monitor.update_data_using_fetcher("historical_validation")
```

### 3. 测试配置

```bash
python test_config.py        # 测试配置加载
python test_auto_monitor.py  # 完整功能测试
```

## 📊 系统特性

### 智能交易对选择
1. **优先使用数据库现有交易对**：从本地数据库读取已有的交易对
2. **智能补充**：如果数据库交易对不足，自动获取交易量前100的USDT交易对
3. **排除稳定币**：自动排除 USDCUSDT、BUSDUSDT 等稳定币交易对
4. **优先级配置**：支持配置优先监控的交易对

### 数据更新机制
1. **增量更新**：只更新需要的数据，避免重复获取
2. **缺口分析**：自动分析数据缺口并补充
3. **批次处理**：分批获取数据，避免API限制
4. **错误恢复**：网络错误时自动重试

### 策略验证
1. **多策略支持**：支持 CRSI、EMA、Bollinger 等多种策略
2. **置信度计算**：为每个信号计算置信度
3. **风险控制**：模拟交易模式，无实际交易风险
4. **详细报告**：生成包含统计信息的验证报告

## 📈 监控流程

```
启动系统 → 初始化组件 → 获取监控交易对 → 设置策略
    ↓
等待4小时K线切换 → 检测新K线 → 更新数据 → 验证策略 → 生成信号
    ↓
生成报告 → 继续监控循环
```

## 🔧 技术实现

### 模块复用
```python
from src.data_layer.historical_data_fetcher import (
    USDTHistoricalFetcher,    # 数据获取器
    save_klines,              # 数据保存
    scan_local_databases,     # 数据库扫描
    analyze_data_gaps         # 缺口分析
)
```

### 配置驱动
- 所有参数通过配置文件控制
- 支持多种预设配置
- 运行时动态加载配置

### 时间精确控制
- 精确计算4小时K线时间（0, 4, 8, 12, 16, 20点）
- 在K线切换后立即触发更新
- 支持手动触发和定时触发

## 📋 日志和监控

系统提供详细的日志信息：
- 系统启动和初始化状态
- 数据更新进度和结果
- 策略验证过程和信号生成
- 错误信息和恢复状态

## ⚠️ 注意事项

1. **代理设置**：确保代理服务正常运行（如使用代理）
2. **数据库路径**：确保数据库文件存在且可访问
3. **API限制**：系统已内置API限制处理，但仍需注意频率
4. **磁盘空间**：长期运行需要足够的磁盘空间存储数据

## 🎉 总结

该系统完全满足您的需求：
- ✅ 复用 `historical_data_fetcher.py` 功能，避免重复代码
- ✅ 配置文件驱动，与终端执行效果一致
- ✅ 支持实时监控和历史验证双模式
- ✅ 智能交易对管理和数据更新
- ✅ 完整的策略验证和信号生成系统
