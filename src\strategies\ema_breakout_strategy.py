"""
EMA突破策略
EMA Breakout Strategy

策略逻辑：
1. 入场条件：EMA21 > EMA200 且 EMA21 > EMA55 且 距离上次信号>60天
2. 退出条件（按优先级排序）：
   - 固定止损：亏损大于15%（最高优先级）
   - 移动止损：最高价盈利>15%时，止损移动到成本价
   - 分批止盈：盈利大于50%时，止盈一半仓位
   - 技术退出：EMA21斜率连续3个时间窗口为负
3. 日线以早8点收盘计算
4. 使用4小时数据重采样为日线
5. EMA21斜率使用3个周期计算
6. 时间冷却期：两次入场信号间隔必须大于60天
"""

from typing import Dict, Any, List
import pandas as pd
import numpy as np
from .base import BaseStrategy, SignalType



class EMABreakoutStrategy(BaseStrategy):
    """
    EMA突破策略

    基于EMA21、EMA55、EMA200的多重确认突破策略
    """
    
    @classmethod
    def get_default_parameters(cls) -> Dict[str, Any]:
        """
        获取策略默认参数

        Returns:
            Dict[str, Any]: 默认参数字典
        """
        return {
            'ema_short': 21,              # 短期EMA周期
            'ema_medium': 55,             # 中期EMA周期
            'ema_long': 200,              # 长期EMA周期
            'slope_periods': 3,           # EMA21斜率计算周期（改为3）
            'slope_confirm_periods': 3,   # 斜率确认需要的连续周期数
            'min_ema200_periods': 200,    # EMA200最少需要的数据点
            'daily_close_hour': 8,        # 日线收盘时间（早8点）
            'stop_loss_pct': 0.15,        # 固定止损百分比（15%）
            'partial_profit_pct': 0.50,   # 分批止盈触发点（50%）
            'trailing_trigger_pct': 0.15, # 移动止损触发点（盈利15%时启动）
            'signal_cooldown_days': 30    # 信号冷却期天数
        }
    
    @classmethod
    def get_required_columns(cls) -> List[str]:
        """
        获取策略需要的数据列
        
        Returns:
            List[str]: 必需的数据列名列表
        """
        return ['open', 'high', 'low', 'close', 'volume']
    
    def _validate_parameters(self):
        """
        验证策略参数有效性
        """
        if not 5 <= self.params['ema_short'] <= 50:
            raise ValueError("ema_short 应在5-50之间")
        
        if not 20 <= self.params['ema_medium'] <= 100:
            raise ValueError("ema_medium 应在20-100之间")
            
        if not 100 <= self.params['ema_long'] <= 300:
            raise ValueError("ema_long 应在100-300之间")

        if self.params['ema_short'] >= self.params['ema_medium']:
            raise ValueError("ema_short 必须小于 ema_medium")

        if self.params['ema_medium'] >= self.params['ema_long']:
            raise ValueError("ema_medium 必须小于 ema_long")

        if not 3 <= self.params['slope_periods'] <= 10:
            raise ValueError("slope_periods 应在3-10之间")

        if not 1 <= self.params['slope_confirm_periods'] <= 5:
            raise ValueError("slope_confirm_periods 应在1-5之间")



    def _resample_to_daily_8am(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        将4小时数据重采样为日线数据（以早8点为收盘时间）

        Args:
            data: 4小时K线数据

        Returns:
            pd.DataFrame: 日线数据
        """
        # 确保数据有时间索引
        df = data.copy()

        if 'datetime' not in df.columns:
            # 如果没有datetime列，使用现有的索引（应该是datetime）
            if isinstance(df.index, pd.DatetimeIndex):
                df['datetime'] = df.index
            else:
                # 如果索引也不是datetime，创建一个时间索引
                df['datetime'] = pd.date_range(start='2023-01-01', periods=len(df), freq='4H')

        # 确保datetime列是datetime类型
        df['datetime'] = pd.to_datetime(df['datetime'])

        # 调整时间：减去8小时，使得早8点变成0点
        df['adjusted_time'] = df['datetime'] - pd.Timedelta(hours=self.params['daily_close_hour'])
        df.set_index('adjusted_time', inplace=True)

        # 按调整后的日期重采样
        daily_data = df.resample('1D').agg({
            'open': 'first',      # 第一个4小时K线的开盘价
            'high': 'max',        # 最高价
            'low': 'min',         # 最低价
            'close': 'last',      # 最后一个4小时K线的收盘价
            'volume': 'sum'       # 成交量总和
        }).dropna()

        # 恢复正确的时间（加回8小时）
        daily_data.index = daily_data.index + pd.Timedelta(hours=self.params['daily_close_hour'])

        # 重置索引，保持datetime列
        daily_data.reset_index(inplace=True)
        daily_data.rename(columns={'adjusted_time': 'datetime'}, inplace=True)

        return daily_data

    def _calculate_ema_traditional(self, prices: pd.Series, period: int) -> pd.Series:
        """
        使用传统方法计算EMA，更接近交易软件

        Args:
            prices: 价格序列
            period: EMA周期

        Returns:
            pd.Series: EMA序列
        """
        if len(prices) < period:
            return pd.Series([np.nan] * len(prices), index=prices.index)

        multiplier = 2 / (period + 1)
        ema_values = []

        # 使用前period个数据的SMA作为第一个EMA值
        sma = prices.iloc[:period].mean()
        ema_values.append(sma)

        # 从第period+1个数据开始计算EMA
        for i in range(period, len(prices)):
            ema_value = (prices.iloc[i] * multiplier) + (ema_values[-1] * (1 - multiplier))
            ema_values.append(ema_value)

        # 前面的值用NaN填充
        result = [np.nan] * (period - 1) + ema_values
        return pd.Series(result, index=prices.index)

    def _check_ema200_validity(self, daily_data: pd.DataFrame) -> tuple:
        """
        检查EMA200是否有足够数据计算

        Args:
            daily_data: 日线数据

        Returns:
            tuple: (是否有效, EMA200序列)
        """
        min_periods = self.params['min_ema200_periods']

        # 严格检查：必须有足够的数据点才能计算准确的EMA200
        if len(daily_data) < min_periods:
            return False, None

        # 使用传统方法计算EMA200
        ema200 = self._calculate_ema_traditional(daily_data['close'], self.params['ema_long'])

        # 检查最新的EMA200是否有效（不是NaN）
        if pd.isna(ema200.iloc[-1]):
            return False, None

        return True, ema200

    def _calculate_ema21_slope(self, ema21_series: pd.Series) -> pd.Series:
        """
        计算EMA21的斜率

        Args:
            ema21_series: EMA21序列

        Returns:
            pd.Series: 斜率序列
        """
        periods = self.params['slope_periods']
        slopes = []

        for i in range(len(ema21_series)):
            if i < periods:
                slopes.append(np.nan)
            else:
                # 计算线性回归斜率
                y = ema21_series.iloc[i-periods+1:i+1].values
                x = np.arange(len(y))

                # 检查数据有效性
                if len(y) >= 2 and not np.any(np.isnan(y)):
                    try:
                        slope = np.polyfit(x, y, 1)[0]  # 一次多项式的斜率
                        slopes.append(slope)
                    except (np.linalg.LinAlgError, ValueError):
                        # 如果线性回归失败，使用简单的差分方法
                        slope = (y[-1] - y[0]) / (len(y) - 1)
                        slopes.append(slope)
                else:
                    slopes.append(np.nan)

        return pd.Series(slopes, index=ema21_series.index)

    def _calculate_ema55_slope(self, ema55_series: pd.Series) -> pd.Series:
        """
        计算EMA55的斜率（3周期）

        Args:
            ema55_series: EMA55序列

        Returns:
            pd.Series: 斜率序列
        """
        periods = 3  # 调整为3个周期计算EMA55斜率，提高敏感度
        slopes = []

        for i in range(len(ema55_series)):
            if i < periods:
                slopes.append(np.nan)
            else:
                # 计算线性回归斜率
                y = ema55_series.iloc[i-periods+1:i+1].values
                x = np.arange(len(y))

                # 检查数据有效性
                if len(y) >= 2 and not np.any(np.isnan(y)):
                    try:
                        slope = np.polyfit(x, y, 1)[0]  # 一次多项式的斜率
                        slopes.append(slope)
                    except (np.linalg.LinAlgError, ValueError):
                        # 如果线性回归失败，使用简单的差分方法
                        slope = (y[-1] - y[0]) / (len(y) - 1)
                        slopes.append(slope)
                else:
                    slopes.append(np.nan)

        return pd.Series(slopes, index=ema55_series.index)

    def _convert_slope_to_degrees(self, slope: float) -> float:
        """
        将斜率转换为角度（度数）

        Args:
            slope: 斜率值

        Returns:
            float: 角度（度数）
        """
        if pd.isna(slope):
            return np.nan
        return np.degrees(np.arctan(slope))

    def _calculate_trading_days(self, entry_time, current_time, data):
        """
        计算交易日天数（基于4小时K线数据）

        Args:
            entry_time: 入场时间
            current_time: 当前时间
            data: 4小时K线数据

        Returns:
            int: 交易日天数
        """
        try:
            # 找到入场和当前时间对应的索引
            entry_mask = data['datetime'] <= entry_time
            current_mask = data['datetime'] <= current_time

            if not entry_mask.any() or not current_mask.any():
                return 0

            entry_idx = data[entry_mask].index[-1]
            current_idx = data[current_mask].index[-1]

            # 每天6个4小时K线，所以交易日 = K线数差 / 6
            kline_diff = current_idx - entry_idx
            trading_days = kline_diff // 6

            return max(0, trading_days)
        except Exception as e:
            return 0

    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标

        Args:
            data: 输入的K线数据

        Returns:
            pd.DataFrame: 包含技术指标的数据框
        """
        # 转换为日线数据（以早8点收盘）
        daily_data = self._resample_to_daily_8am(data)

        if len(daily_data) < 50:  # 至少需要50个日线数据点
            # 如果日线数据不足，返回原始数据结构但标记为无效
            df = data.copy()
            df['ema21'] = np.nan
            df['ema55'] = np.nan
            df['ema200'] = np.nan
            df['ema21_slope'] = np.nan
            df['ema200_valid'] = False
            df['valid_signal'] = False
            return df

        # 使用传统方法计算基础EMA指标
        daily_data['ema21'] = self._calculate_ema_traditional(daily_data['close'], self.params['ema_short'])
        daily_data['ema55'] = self._calculate_ema_traditional(daily_data['close'], self.params['ema_medium'])

        # 检查EMA200有效性
        ema200_valid, ema200 = self._check_ema200_validity(daily_data)
        if ema200_valid:
            daily_data['ema200'] = ema200
        else:
            daily_data['ema200'] = np.nan

        # 计算EMA21和EMA55斜率
        daily_data['ema21_slope'] = self._calculate_ema21_slope(daily_data['ema21'])
        daily_data['ema55_slope'] = self._calculate_ema55_slope(daily_data['ema55'])

        # 计算入场条件（EMA21 > EMA200 且 EMA21 > EMA55）
        if ema200_valid:
            # 入场条件：EMA21 > EMA200 且 EMA21 > EMA55
            daily_data['entry_condition'] = (daily_data['ema21'] > daily_data['ema200']) & (daily_data['ema21'] > daily_data['ema55'])
        else:
            # EMA200数据不足时，不开仓
            daily_data['entry_condition'] = False

        # 信号触发（条件刚满足时）
        daily_data['entry_trigger'] = daily_data['entry_condition'] & ~daily_data['entry_condition'].shift(1).fillna(False)

        # 计算EMA21斜率连续为负的确认
        daily_data['ema21_slope_negative'] = daily_data['ema21_slope'] < 0

        # 计算连续负斜率的计数
        daily_data['negative_slope_count'] = 0
        for i in range(len(daily_data)):
            if daily_data['ema21_slope_negative'].iloc[i]:
                if i > 0 and daily_data['negative_slope_count'].iloc[i-1] > 0:
                    daily_data.iloc[i, daily_data.columns.get_loc('negative_slope_count')] = daily_data['negative_slope_count'].iloc[i-1] + 1
                else:
                    daily_data.iloc[i, daily_data.columns.get_loc('negative_slope_count')] = 1
            else:
                daily_data.iloc[i, daily_data.columns.get_loc('negative_slope_count')] = 0

        # 退出条件：连续3个周期EMA21斜率为负
        daily_data['exit_condition'] = daily_data['negative_slope_count'] >= self.params['slope_confirm_periods']

        # 标记有效信号区域
        daily_data['valid_signal'] = (
            ~daily_data['ema21'].isna() &
            ~daily_data['ema55'].isna() &
            ~daily_data['ema200'].isna()
        )

        # 添加EMA200有效性标记
        daily_data['ema200_valid'] = ema200_valid

        # 将日线指标映射回4小时数据
        df = self._map_daily_to_4h(data, daily_data)

        return df

    def _map_daily_to_4h(self, data_4h: pd.DataFrame, daily_data: pd.DataFrame) -> pd.DataFrame:
        """
        将日线指标映射回4小时数据

        Args:
            data_4h: 4小时数据
            daily_data: 日线数据

        Returns:
            pd.DataFrame: 包含指标的4小时数据
        """
        df = data_4h.copy()

        # 确保有datetime列
        if 'datetime' not in df.columns:
            # 使用现有的索引（应该是datetime）
            if isinstance(df.index, pd.DatetimeIndex):
                df['datetime'] = df.index
            else:
                # 如果索引也不是datetime，这是一个错误情况
                raise ValueError("数据必须有datetime索引或datetime列")

        df['datetime'] = pd.to_datetime(df['datetime'])

        # 为4小时数据计算对应的日线日期（基于早8点收盘）
        # 早8点之前的4小时K线属于前一个交易日
        df['trading_date'] = df['datetime'].apply(lambda x:
            x.date() if x.hour >= self.params['daily_close_hour']
            else (x - pd.Timedelta(days=1)).date()
        )

        # 为日线数据准备日期
        if 'datetime' in daily_data.columns:
            daily_data['trading_date'] = pd.to_datetime(daily_data['datetime']).dt.date
        else:
            # 如果没有datetime列，使用索引
            daily_data['trading_date'] = daily_data.index.date

        # 映射日线指标到4小时数据
        merge_columns = ['trading_date', 'ema21', 'ema55', 'ema200', 'ema21_slope', 'ema55_slope',
                        'ema21_slope_negative', 'negative_slope_count', 'exit_condition',
                        'entry_condition', 'entry_trigger', 'valid_signal', 'ema200_valid']

        df = df.merge(
            daily_data[merge_columns],
            on='trading_date',
            how='left'
        )

        # 前向填充缺失值
        numeric_cols = ['ema21', 'ema55', 'ema200', 'ema21_slope', 'ema55_slope', 'negative_slope_count']
        for col in numeric_cols:
            df[col] = df[col].ffill()

        boolean_cols = ['ema21_slope_negative', 'exit_condition',
                       'entry_condition', 'entry_trigger', 'valid_signal', 'ema200_valid']
        for col in boolean_cols:
            df[col] = df[col].fillna(False)

        # 恢复原始的datetime索引
        if 'datetime' in df.columns:
            df.set_index('datetime', inplace=True)

        return df



    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号

        入场条件：EMA21 > EMA200 且 EMA21 > EMA55 且 距离上次信号>60天
        退出条件（按优先级排序）：
        1. 固定止损：亏损大于15%（最高优先级）
        2. 移动止损：最高价盈利>15%时，止损移动到成本价
        3. 分批止盈：盈利大于50%时，止盈一半仓位
        4. 技术退出：EMA21斜率连续3周期为负

        Args:
            data: 包含价格和指标数据的数据框

        Returns:
            pd.DataFrame: 包含信号的数据框
        """
        df = data.copy()

        # 初始化信号列
        df['signal'] = SignalType.NEUTRAL.value
        df['confidence'] = 0.0
        df['reason'] = ''
        df['exit_type'] = ''

        # 交易状态管理
        current_position = None
        last_entry_time = None  # 记录上次入场时间

        for i in range(len(df)):
            if not df.iloc[i]['valid_signal']:
                continue

            current_row = df.iloc[i]
            current_time = df.index[i]  # 使用索引而不是列
            current_price = current_row['close']

            # 检查入场信号
            if (current_row['entry_trigger'] and current_position is None):

                # 检查时间冷却期：距离上次入场是否超过60天
                cooldown_ok = True
                if last_entry_time is not None:
                    days_since_last = (current_time - last_entry_time).days
                    cooldown_ok = days_since_last > self.params['signal_cooldown_days']

                if cooldown_ok:
                    # 开仓
                    current_position = {
                        'entry_time': current_time,
                        'entry_price': current_price,
                        'entry_idx': i,
                        'highest_price': current_price,  # 记录最高价
                        'position_size': 1.0,  # 仓位大小（1.0=满仓，0.5=半仓）
                        'trailing_stop_active': False,  # 移动止损是否激活
                        'stop_loss_price': current_price * (1 - self.params['stop_loss_pct']),  # 固定止损价
                        'partial_profit_taken': False  # 是否已经分批止盈
                    }
                    last_entry_time = current_time

                    # 计算置信度
                    confidence = 0.7  # 基础置信度

                    # EMA21相对EMA200的优势
                    if not pd.isna(current_row['ema21']) and not pd.isna(current_row['ema200']):
                        ema_advantage = (current_row['ema21'] - current_row['ema200']) / current_row['ema200']
                        confidence += min(ema_advantage * 5, 0.2)  # 最多加20%

                    # EMA21斜率加分
                    if not pd.isna(current_row['ema21_slope']) and current_row['ema21_slope'] > 0:
                        confidence += 0.1

                    # 限制置信度范围
                    confidence = min(max(confidence, 0.1), 1.0)

                    # 记录买入信号
                    df.iloc[i, df.columns.get_loc('signal')] = SignalType.BUY.value
                    df.iloc[i, df.columns.get_loc('confidence')] = confidence
                    df.iloc[i, df.columns.get_loc('reason')] = f"EMA21突破EMA200买入 (EMA21: {current_row['ema21']:.2f}, EMA200: {current_row['ema200']:.2f})"
                    df.iloc[i, df.columns.get_loc('exit_type')] = 'entry'

            # 检查退出信号
            elif current_position is not None:
                # 计算当前盈亏比例
                entry_price = current_position['entry_price']
                current_gain_pct = (current_price - entry_price) / entry_price
                current_loss_pct = (entry_price - current_price) / entry_price

                # 更新最高价
                if current_price > current_position['highest_price']:
                    current_position['highest_price'] = current_price

                # # 检查是否激活移动止损：最高价大于成本15%时
                highest_gain_pct = (current_position['highest_price'] - entry_price) / entry_price
                # if highest_gain_pct > self.params['trailing_trigger_pct'] and not current_position['trailing_stop_active']:
                #     current_position['trailing_stop_active'] = True
                #     current_position['stop_loss_price'] = entry_price  # 移动止损到成本价

                # 检查退出条件（按优先级排序）
                should_exit = False
                partial_exit = False
                exit_reason = ""
                exit_type = ""
                confidence = 0.8

                # 1. 固定止损/移动止损：价格跌破止损价（最高优先级）
                if current_price < current_position['stop_loss_price']:
                    should_exit = True
                    if current_position['trailing_stop_active']:
                        exit_reason = f"移动止损退出，跌破成本价（最高盈利{highest_gain_pct*100:.1f}%）"
                        exit_type = 'trailing_stop'
                    else:
                        exit_reason = f"固定止损退出，亏损{current_loss_pct*100:.1f}%"
                        exit_type = 'stop_loss'
                    confidence = 0.9

                # # 2. 分批止盈：盈利大于50%且未分批止盈
                # elif current_gain_pct > self.params['partial_profit_pct'] and not current_position['partial_profit_taken']:
                #     partial_exit = True
                #     current_position['partial_profit_taken'] = True
                #     current_position['position_size'] = 0.5  # 减仓到一半
                #     exit_reason = f"分批止盈，盈利{current_gain_pct*100:.1f}%，减仓50%"
                #     exit_type = 'partial_profit'
                #     confidence = 0.9

                # 3. 技术退出：EMA21斜率连续3个周期为负
                elif current_row['exit_condition']:
                    should_exit = True
                    exit_reason = f"EMA21斜率连续{self.params['slope_confirm_periods']}周期为负，技术退出"
                    exit_type = 'slope_negative'
                    confidence = 0.8

                if should_exit or partial_exit:
                    df.iloc[i, df.columns.get_loc('signal')] = SignalType.SELL.value
                    df.iloc[i, df.columns.get_loc('confidence')] = confidence
                    df.iloc[i, df.columns.get_loc('reason')] = exit_reason
                    df.iloc[i, df.columns.get_loc('exit_type')] = exit_type

                    if should_exit:
                        current_position = None  # 完全平仓
                    # 如果是分批止盈，保持仓位但更新仓位大小

        return df

    def generate_signal(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        生成单个交易信号（用于实时交易）

        Args:
            data: 最新的K线数据

        Returns:
            Dict[str, Any]: 信号字典
        """
        if len(data) < 50:  # 至少需要50个4小时数据点
            return {
                'action': 'HOLD',
                'confidence': 0,
                'reason': f'数据不足，需要至少50个4小时数据点',
                'timestamp': pd.Timestamp.now()
            }

        # 计算指标
        df_with_indicators = self.calculate_indicators(data)

        # 生成信号
        df_with_signals = self.generate_signals(df_with_indicators)

        # 获取最新信号
        latest_signal = df_with_signals.iloc[-1]

        # 转换信号类型
        signal_map = {
            SignalType.BUY.value: 'BUY',
            SignalType.SELL.value: 'SELL',
            SignalType.NEUTRAL.value: 'HOLD'
        }

        # 构建返回信息
        result = {
            'action': signal_map.get(latest_signal['signal'], 'HOLD'),
            'confidence': latest_signal['confidence'] * 100,  # 转换为百分比
            'reason': latest_signal['reason'],
            'timestamp': pd.Timestamp.now(),
            'price': latest_signal['close'],
            'ema21': latest_signal.get('ema21', np.nan),
            'ema55': latest_signal.get('ema55', np.nan),
            'ema200': latest_signal.get('ema200', np.nan),
            'ema21_slope': latest_signal.get('ema21_slope', np.nan),
            'ema200_valid': latest_signal.get('ema200_valid', False)
        }

        # 添加条件状态信息
        result['market_env'] = latest_signal.get('market_env', 'UNKNOWN')
        result['price_above_ema200'] = latest_signal.get('price_above_ema200', False)
        result['ema21_above_ema200'] = latest_signal.get('ema21_above_ema200', False)
        result['within_distance_threshold'] = latest_signal.get('within_distance_threshold', False)

        # 添加移动止损信息
        result['stop_loss_price'] = latest_signal.get('stop_loss_price', np.nan)
        result['exit_type'] = latest_signal.get('exit_type', '')

        # 添加距离信息
        if not pd.isna(latest_signal.get('ema21')) and not pd.isna(latest_signal.get('ema200')):
            distance_pct = abs(latest_signal['ema21'] - latest_signal['ema200']) / latest_signal['ema200']
            result['distance_to_ema200_pct'] = distance_pct * 100

        return result

# 注册策略
from . import register_strategy
register_strategy('EMABreakout', EMABreakoutStrategy)
