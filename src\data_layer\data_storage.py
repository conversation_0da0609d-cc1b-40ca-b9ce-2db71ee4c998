#!/usr/bin/env python3
"""
数据存储系统
Data Storage System

基于SQLite实现的K线数据存储系统，支持增量更新和高效查询
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json
from contextlib import contextmanager
from dataclasses import dataclass, asdict

from ..logger_config import get_logger

logger = get_logger(__name__)


@dataclass
class KlineRecord:
    """K线记录数据结构"""
    symbol: str
    interval: str
    timestamp: int
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    quote_volume: float
    count: int
    taker_buy_volume: float
    taker_buy_quote_volume: float
    close_time: int
    fetch_time: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 将datetime转换为timestamp
        data['fetch_time'] = int(self.fetch_time.timestamp())
        return data


class DataStorage:
    """数据存储管理器"""
    
    def __init__(self, db_path: str = "./data/klines.db"):
        """
        初始化数据存储
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        logger.info(f"数据存储系统初始化完成，数据库路径: {self.db_path}")
    
    def _init_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            # 创建K线数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS klines (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    interval TEXT NOT NULL,
                    timestamp INTEGER NOT NULL,
                    open_price REAL NOT NULL,
                    high_price REAL NOT NULL,
                    low_price REAL NOT NULL,
                    close_price REAL NOT NULL,
                    volume REAL NOT NULL,
                    quote_volume REAL NOT NULL,
                    count INTEGER NOT NULL,
                    taker_buy_volume REAL NOT NULL,
                    taker_buy_quote_volume REAL NOT NULL,
                    close_time INTEGER NOT NULL,
                    fetch_time INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, interval, timestamp)
                )
            """)
            
            # 创建索引以提高查询性能
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_symbol_interval_timestamp 
                ON klines(symbol, interval, timestamp)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_symbol_interval_datetime
                ON klines(symbol, interval, timestamp DESC)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_fetch_time
                ON klines(fetch_time)
            """)
            
            # 创建元数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS metadata (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    interval TEXT NOT NULL,
                    first_timestamp INTEGER,
                    last_timestamp INTEGER,
                    total_records INTEGER DEFAULT 0,
                    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, interval)
                )
            """)
            
            # 创建数据质量表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS data_quality (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    interval TEXT NOT NULL,
                    check_date DATE NOT NULL,
                    total_records INTEGER,
                    missing_periods INTEGER,
                    duplicate_records INTEGER,
                    anomaly_records INTEGER,
                    quality_score REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            logger.info("数据库表结构初始化完成")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(str(self.db_path))
        try:
            # 设置WAL模式以提高并发性能
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=memory")
            yield conn
        except Exception as e:
            conn.rollback()
            logger.error(f"数据库操作错误: {e}")
            raise
        finally:
            conn.close()
    
    def save_klines_dataframe(self, df: pd.DataFrame, symbol: str, interval: str) -> int:
        """
        保存K线DataFrame到数据库
        
        Args:
            df: K线数据DataFrame
            symbol: 交易对符号
            interval: 时间间隔
            
        Returns:
            int: 成功插入的记录数
        """
        if df.empty:
            logger.warning("DataFrame为空，跳过保存")
            return 0
        
        try:
            # 准备数据
            records = []
            for idx, row in df.iterrows():
                record = KlineRecord(
                    symbol=symbol,
                    interval=interval,
                    timestamp=int(row['timestamp']),
                    open_price=float(row['open']),
                    high_price=float(row['high']),
                    low_price=float(row['low']),
                    close_price=float(row['close']),
                    volume=float(row['volume']),
                    quote_volume=float(row['quote_volume']),
                    count=int(row['count']),
                    taker_buy_volume=float(row['taker_buy_volume']),
                    taker_buy_quote_volume=float(row['taker_buy_quote_volume']),
                    close_time=int(row['close_time']),
                    fetch_time=row.get('fetch_time', datetime.now())
                )
                records.append(record.to_dict())
            
            # 插入数据
            inserted_count = self._insert_records(records)
            
            # 更新元数据
            self._update_metadata(symbol, interval)
            
            logger.info(f"✅ {symbol} ({interval}): 成功保存 {inserted_count} 条K线记录")
            return inserted_count
            
        except Exception as e:
            logger.error(f"❌ {symbol} ({interval}): 保存K线数据失败 - {e}")
            return 0
    
    def _insert_records(self, records: List[Dict[str, Any]]) -> int:
        """插入记录到数据库"""
        if not records:
            return 0
        
        insert_sql = """
            INSERT OR IGNORE INTO klines (
                symbol, interval, timestamp, open_price, high_price, low_price, 
                close_price, volume, quote_volume, count, taker_buy_volume, 
                taker_buy_quote_volume, close_time, fetch_time
            ) VALUES (
                :symbol, :interval, :timestamp, :open_price, :high_price, :low_price,
                :close_price, :volume, :quote_volume, :count, :taker_buy_volume,
                :taker_buy_quote_volume, :close_time, :fetch_time
            )
        """
        
        with self.get_connection() as conn:
            cursor = conn.executemany(insert_sql, records)
            conn.commit()
            return cursor.rowcount
    
    def _update_metadata(self, symbol: str, interval: str):
        """更新元数据"""
        with self.get_connection() as conn:
            # 计算统计信息
            stats_sql = """
                SELECT 
                    MIN(timestamp) as first_timestamp,
                    MAX(timestamp) as last_timestamp,
                    COUNT(*) as total_records
                FROM klines 
                WHERE symbol = ? AND interval = ?
            """
            
            cursor = conn.execute(stats_sql, (symbol, interval))
            stats = cursor.fetchone()
            
            if stats and stats[2] > 0:  # total_records > 0
                # 更新或插入元数据
                upsert_sql = """
                    INSERT OR REPLACE INTO metadata 
                    (symbol, interval, first_timestamp, last_timestamp, total_records, last_update)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                
                conn.execute(upsert_sql, (
                    symbol, interval, stats[0], stats[1], stats[2], datetime.now()
                ))
                conn.commit()
    
    def get_klines(self, symbol: str, interval: str, start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None, limit: Optional[int] = None) -> pd.DataFrame:
        """
        查询K线数据
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            start_time: 开始时间
            end_time: 结束时间
            limit: 限制返回条数
            
        Returns:
            pd.DataFrame: K线数据
        """
        try:
            # 构建查询条件
            where_conditions = ["symbol = ? AND interval = ?"]
            params = [symbol, interval]
            
            if start_time:
                where_conditions.append("timestamp >= ?")
                params.append(int(start_time.timestamp() * 1000))
            
            if end_time:
                where_conditions.append("timestamp <= ?")
                params.append(int(end_time.timestamp() * 1000))
            
            where_clause = " AND ".join(where_conditions)
            
            # 构建完整查询
            query = f"""
                SELECT 
                    timestamp, open_price as open, high_price as high, low_price as low,
                    close_price as close, volume, quote_volume, count,
                    taker_buy_volume, taker_buy_quote_volume, close_time
                FROM klines 
                WHERE {where_clause}
                ORDER BY timestamp ASC
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            with self.get_connection() as conn:
                df = pd.read_sql_query(query, conn, params=params)
            
            if not df.empty:
                # 转换时间戳
                df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('datetime', inplace=True)
                
                # 添加元数据
                df['symbol'] = symbol
                df['interval'] = interval
            
            logger.debug(f"查询 {symbol} ({interval}) K线数据: {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"查询 {symbol} ({interval}) K线数据失败: {e}")
            return pd.DataFrame()
    
    def get_latest_timestamp(self, symbol: str, interval: str) -> Optional[datetime]:
        """
        获取最新的时间戳
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            
        Returns:
            Optional[datetime]: 最新时间戳
        """
        try:
            query = """
                SELECT MAX(timestamp) as latest_timestamp
                FROM klines 
                WHERE symbol = ? AND interval = ?
            """
            
            with self.get_connection() as conn:
                cursor = conn.execute(query, (symbol, interval))
                result = cursor.fetchone()
            
            if result and result[0]:
                return datetime.fromtimestamp(result[0] / 1000)
            
            return None
            
        except Exception as e:
            logger.error(f"获取 {symbol} ({interval}) 最新时间戳失败: {e}")
            return None
    
    def get_data_range(self, symbol: str, interval: str) -> Optional[Tuple[datetime, datetime]]:
        """
        获取数据时间范围
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            
        Returns:
            Optional[Tuple[datetime, datetime]]: (最早时间, 最晚时间)
        """
        try:
            query = """
                SELECT MIN(timestamp) as min_time, MAX(timestamp) as max_time
                FROM klines 
                WHERE symbol = ? AND interval = ?
            """
            
            with self.get_connection() as conn:
                cursor = conn.execute(query, (symbol, interval))
                result = cursor.fetchone()
            
            if result and result[0] and result[1]:
                min_time = datetime.fromtimestamp(result[0] / 1000)
                max_time = datetime.fromtimestamp(result[1] / 1000)
                return (min_time, max_time)
            
            return None
            
        except Exception as e:
            logger.error(f"获取 {symbol} ({interval}) 数据范围失败: {e}")
            return None
    
    def get_missing_periods(self, symbol: str, interval: str, 
                          start_time: datetime, end_time: datetime) -> List[Tuple[datetime, datetime]]:
        """
        检测缺失的时间段
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List[Tuple[datetime, datetime]]: 缺失时间段列表
        """
        try:
            # 获取现有数据
            df = self.get_klines(symbol, interval, start_time, end_time)
            
            if df.empty:
                return [(start_time, end_time)]
            
            # 计算期望的时间间隔
            interval_map = {
                '1m': timedelta(minutes=1),
                '5m': timedelta(minutes=5),
                '15m': timedelta(minutes=15),
                '30m': timedelta(minutes=30),
                '1h': timedelta(hours=1),
                '4h': timedelta(hours=4),
                '1d': timedelta(days=1)
            }
            
            if interval not in interval_map:
                logger.warning(f"不支持的时间间隔: {interval}")
                return []
            
            time_delta = interval_map[interval]
            
            # 生成期望的时间序列
            expected_times = []
            current_time = start_time
            while current_time <= end_time:
                expected_times.append(current_time)
                current_time += time_delta
            
            # 查找缺失时间段
            existing_times = set(df.index)
            missing_periods = []
            
            for expected_time in expected_times:
                if expected_time not in existing_times:
                    # 找到缺失的开始和结束时间
                    gap_start = expected_time
                    gap_end = expected_time
                    
                    # 扩展缺失区间
                    while gap_end + time_delta in expected_times and gap_end + time_delta not in existing_times:
                        gap_end += time_delta
                    
                    missing_periods.append((gap_start, gap_end))
            
            # 合并相邻的缺失区间
            merged_periods = self._merge_periods(missing_periods)
            
            logger.debug(f"{symbol} ({interval}): 发现 {len(merged_periods)} 个缺失时间段")
            return merged_periods
            
        except Exception as e:
            logger.error(f"检测 {symbol} ({interval}) 缺失时间段失败: {e}")
            return []
    
    def _merge_periods(self, periods: List[Tuple[datetime, datetime]]) -> List[Tuple[datetime, datetime]]:
        """合并相邻的时间段"""
        if not periods:
            return []
        
        # 按开始时间排序
        sorted_periods = sorted(periods, key=lambda x: x[0])
        merged = [sorted_periods[0]]
        
        for current_start, current_end in sorted_periods[1:]:
            last_start, last_end = merged[-1]
            
            # 如果当前区间与上一个区间相邻或重叠
            if current_start <= last_end:
                # 合并区间
                merged[-1] = (last_start, max(last_end, current_end))
            else:
                # 添加新区间
                merged.append((current_start, current_end))
        
        return merged
    
    def get_metadata(self, symbol: str = None, interval: str = None) -> pd.DataFrame:
        """
        获取元数据信息
        
        Args:
            symbol: 交易对符号（可选）
            interval: 时间间隔（可选）
            
        Returns:
            pd.DataFrame: 元数据信息
        """
        try:
            where_conditions = []
            params = []
            
            if symbol:
                where_conditions.append("symbol = ?")
                params.append(symbol)
            
            if interval:
                where_conditions.append("interval = ?")
                params.append(interval)
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            query = f"""
                SELECT 
                    symbol, interval, 
                    datetime(first_timestamp/1000, 'unixepoch') as first_time,
                    datetime(last_timestamp/1000, 'unixepoch') as last_time,
                    total_records, last_update
                FROM metadata 
                {where_clause}
                ORDER BY symbol, interval
            """
            
            with self.get_connection() as conn:
                df = pd.read_sql_query(query, conn, params=params)
            
            return df
            
        except Exception as e:
            logger.error(f"获取元数据失败: {e}")
            return pd.DataFrame()
    
    def cleanup_old_data(self, days_to_keep: int = 365):
        """
        清理旧数据
        
        Args:
            days_to_keep: 保留的天数
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days_to_keep)
            cutoff_timestamp = int(cutoff_time.timestamp() * 1000)
            
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "DELETE FROM klines WHERE timestamp < ?", 
                    (cutoff_timestamp,)
                )
                deleted_count = cursor.rowcount
                conn.commit()
            
            logger.info(f"清理旧数据完成: 删除 {deleted_count} 条记录")
            
            # 更新所有元数据
            self._refresh_all_metadata()
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
    
    def _refresh_all_metadata(self):
        """刷新所有元数据"""
        try:
            with self.get_connection() as conn:
                # 获取所有唯一的symbol和interval组合
                cursor = conn.execute(
                    "SELECT DISTINCT symbol, interval FROM klines"
                )
                combinations = cursor.fetchall()
                
                for symbol, interval in combinations:
                    self._update_metadata(symbol, interval)
            
            logger.info("元数据刷新完成")
            
        except Exception as e:
            logger.error(f"刷新元数据失败: {e}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            with self.get_connection() as conn:
                # 基本统计
                stats = {}
                
                # 总记录数
                cursor = conn.execute("SELECT COUNT(*) FROM klines")
                stats['total_records'] = cursor.fetchone()[0]
                
                # 交易对数量
                cursor = conn.execute("SELECT COUNT(DISTINCT symbol) FROM klines")
                stats['total_symbols'] = cursor.fetchone()[0]
                
                # 时间间隔数量
                cursor = conn.execute("SELECT COUNT(DISTINCT interval) FROM klines")
                stats['total_intervals'] = cursor.fetchone()[0]
                
                # 数据库大小
                cursor = conn.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
                stats['database_size_bytes'] = cursor.fetchone()[0]
                stats['database_size_mb'] = stats['database_size_bytes'] / (1024 * 1024)
                
                # 最早和最新数据时间
                cursor = conn.execute("SELECT MIN(timestamp), MAX(timestamp) FROM klines")
                min_ts, max_ts = cursor.fetchone()
                if min_ts and max_ts:
                    stats['earliest_data'] = datetime.fromtimestamp(min_ts / 1000)
                    stats['latest_data'] = datetime.fromtimestamp(max_ts / 1000)
                
                # 各交易对统计
                cursor = conn.execute("""
                    SELECT symbol, COUNT(*) as records 
                    FROM klines 
                    GROUP BY symbol 
                    ORDER BY records DESC 
                    LIMIT 10
                """)
                stats['top_symbols'] = cursor.fetchall()
                
                return stats
                
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {e}")
            return {}
    
    def vacuum_database(self):
        """优化数据库"""
        try:
            with self.get_connection() as conn:
                conn.execute("VACUUM")
                conn.execute("ANALYZE")
            
            logger.info("数据库优化完成")
            
        except Exception as e:
            logger.error(f"数据库优化失败: {e}")


# 便利函数
def get_default_storage() -> DataStorage:
    """获取默认的数据存储实例"""
    return DataStorage()


def save_dataframe_to_storage(df: pd.DataFrame, symbol: str, interval: str, 
                             storage: Optional[DataStorage] = None) -> int:
    """
    便利函数：保存DataFrame到存储
    
    Args:
        df: K线数据DataFrame
        symbol: 交易对符号
        interval: 时间间隔
        storage: 存储实例（可选）
        
    Returns:
        int: 成功保存的记录数
    """
    if storage is None:
        storage = get_default_storage()
    
    return storage.save_klines_dataframe(df, symbol, interval)


def load_dataframe_from_storage(symbol: str, interval: str, days: int = 30,
                               storage: Optional[DataStorage] = None) -> pd.DataFrame:
    """
    便利函数：从存储加载DataFrame
    
    Args:
        symbol: 交易对符号
        interval: 时间间隔
        days: 加载最近的天数
        storage: 存储实例（可选）
        
    Returns:
        pd.DataFrame: K线数据
    """
    if storage is None:
        storage = get_default_storage()
    
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    
    return storage.get_klines(symbol, interval, start_time, end_time)