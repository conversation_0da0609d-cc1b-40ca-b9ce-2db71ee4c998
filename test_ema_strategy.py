#!/usr/bin/env python3
"""
测试EMA突破策略
Test EMA Breakout Strategy
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.strategies import list_strategies, get_strategy
# 导入策略以触发注册
from src.strategies.ema_breakout_strategy import EMABreakoutStrategy

def create_test_data(periods=300):
    """创建测试数据"""
    np.random.seed(42)
    
    # 创建基础价格数据
    base_price = 100
    price_changes = np.random.normal(0, 0.02, periods)
    prices = [base_price]
    
    for change in price_changes:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    prices = prices[1:]  # 移除初始价格
    
    # 创建OHLCV数据
    data = []
    for i, close in enumerate(prices):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = close + np.random.normal(0, 0.005) * close
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'open': open_price,
            'high': max(open_price, high, close),
            'low': min(open_price, low, close),
            'close': close,
            'volume': volume,
            'datetime': pd.Timestamp('2023-01-01') + pd.Timedelta(hours=4*i)
        })
    
    return pd.DataFrame(data)

def test_ema_strategy():
    """测试EMA突破策略"""
    print("🧪 测试EMA突破策略")
    print("=" * 50)
    
    # 1. 检查策略注册
    print("\n1. 检查策略注册:")
    strategies = list_strategies()
    print(f"  可用策略: {strategies}")
    
    if 'EMABreakout' not in strategies:
        print("  ❌ EMABreakout策略未注册")
        return
    
    # 2. 创建策略实例
    print("\n2. 创建策略实例:")
    try:
        strategy = get_strategy('EMABreakout')
        print(f"  ✅ 策略实例创建成功: {type(strategy)}")
        print(f"  默认参数: {strategy.params}")
    except Exception as e:
        print(f"  ❌ 创建策略失败: {e}")
        return
    
    # 3. 创建测试数据
    print("\n3. 创建测试数据:")
    test_data = create_test_data(300)  # 300个4小时K线
    print(f"  测试数据: {len(test_data)} 条记录")
    print(f"  时间范围: {test_data['datetime'].min()} 到 {test_data['datetime'].max()}")
    print(f"  价格范围: {test_data['close'].min():.2f} - {test_data['close'].max():.2f}")
    
    # 4. 计算指标
    print("\n4. 计算技术指标:")
    try:
        df_with_indicators = strategy.calculate_indicators(test_data)
        print(f"  ✅ 指标计算成功")
        print(f"  添加的列: {[col for col in df_with_indicators.columns if col not in test_data.columns]}")
        
        # 检查EMA值
        latest_row = df_with_indicators.iloc[-1]
        if not pd.isna(latest_row.get('ema21')):
            print(f"  EMA21: {latest_row['ema21']:.2f}")
            print(f"  EMA50: {latest_row['ema50']:.2f}")
            print(f"  EMA200: {latest_row['ema200']:.2f}")
            # 计算距离EMA200（如果EMA200有效）
            if not pd.isna(latest_row['ema200']):
                distance_pct = abs(latest_row['ema21'] - latest_row['ema200']) / latest_row['ema200'] * 100
                print(f"  距离EMA200: {distance_pct:.1f}%")
            else:
                print(f"  距离EMA200: N/A (EMA200数据不足)")
        else:
            print("  ⚠️ EMA指标为空，可能数据不足")
            
    except Exception as e:
        import traceback
        print(f"  ❌ 指标计算失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return
    
    # 5. 生成信号
    print("\n5. 生成交易信号:")
    try:
        df_with_signals = strategy.generate_signals(df_with_indicators)
        
        # 统计信号
        buy_signals = (df_with_signals['signal'] == 1).sum()
        sell_signals = (df_with_signals['signal'] == -1).sum()
        neutral_signals = (df_with_signals['signal'] == 0).sum()
        
        print(f"  ✅ 信号生成成功")
        print(f"  买入信号: {buy_signals} 个")
        print(f"  卖出信号: {sell_signals} 个")
        print(f"  中性信号: {neutral_signals} 个")
        
        # 显示最近的信号
        recent_signals = df_with_signals[df_with_signals['signal'] != 0].tail(3)
        if not recent_signals.empty:
            print(f"\n  最近的信号:")
            for idx, row in recent_signals.iterrows():
                signal_type = "买入" if row['signal'] == 1 else "卖出"
                print(f"    {signal_type}: 置信度{row['confidence']*100:.1f}% - {row['reason']}")
        
    except Exception as e:
        print(f"  ❌ 信号生成失败: {e}")
        return
    
    # 6. 测试实时信号
    print("\n6. 测试实时信号:")
    try:
        latest_signal = strategy.generate_signal(test_data)
        print(f"  ✅ 实时信号生成成功")
        print(f"  动作: {latest_signal['action']}")
        print(f"  置信度: {latest_signal['confidence']:.1f}%")
        print(f"  原因: {latest_signal['reason']}")
        print(f"  当前价格: {latest_signal['price']:.2f}")
        
        if 'ema21' in latest_signal:
            print(f"  EMA21: {latest_signal['ema21']:.2f}")
            print(f"  EMA50: {latest_signal['ema50']:.2f}")
            print(f"  EMA200: {latest_signal['ema200']:.2f}")
            # 计算距离EMA200（如果EMA200有效）
            if not pd.isna(latest_signal['ema200']):
                distance_pct = abs(latest_signal['ema21'] - latest_signal['ema200']) / latest_signal['ema200'] * 100
                print(f"  距离EMA200: {distance_pct:.1f}%")
            else:
                print(f"  距离EMA200: N/A (EMA200数据不足)")
        
    except Exception as e:
        print(f"  ❌ 实时信号生成失败: {e}")
        return
    
    print("\n✅ EMA突破策略测试完成")

if __name__ == "__main__":
    test_ema_strategy()
