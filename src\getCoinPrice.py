import requests
import time
from datetime import datetime, timedelta
from src.config_manager import config_manager
from src.logger_config import get_logger

logger = get_logger(__name__)

# 排除的代币列表
g_exclude_token_list = ['BTC', 'ETH', 'BNB', 'BUSD', 'USDC']

def _get_request_config():
    """获取请求配置，包括代理设置"""
    proxies = config_manager.get_proxies()
    
    # 如果代理配置存在但无法连接，则不使用代理
    if proxies:
        try:
            # 测试代理连接
            test_response = requests.get(
                "https://httpbin.org/ip", 
                proxies=proxies, 
                timeout=3
            )
            if test_response.status_code == 200:
                logger.debug("代理连接正常")
                return proxies
        except Exception as e:
            logger.warning(f"代理连接失败，将使用直连: {e}")
    
    return None
    
def get_binance_coin_price(coin):
    """获取币种现货价格"""
    try:
        time_start = time.time()
        
        # 使用新的配置系统
        proxies = _get_request_config()
        base_url = config_manager.get('binance.price_url')
        
        response = requests.get(base_url, params={"symbol": coin}, proxies=proxies, timeout=10)
        time_end = time.time()
        
        if response.status_code == 200:
            response_time = time_end - time_start
            logger.debug(f"请求 {coin} 价格成功，响应时间：{response_time:.5f}秒")
        else:
            logger.error(f"请求 {coin} 价格失败，状态码: {response.status_code}")

        data = response.json()
        coin_price = float(data["price"])

        server_time = response.headers["Date"]
        server_time = datetime.strptime(server_time, "%a, %d %b %Y %H:%M:%S %Z")

        # 将币安服务器时间转换为GMT+8时区
        server_time_gmt8 = server_time + timedelta(hours=8)
        server_time_str = server_time_gmt8.strftime("%Y-%m-%d %H:%M:%S")

        return coin_price, server_time_str
    except Exception as e:
        logger.error(f"get_binance_coin_price Error: {e}")
        return None, None
    
def get_binance_all_coin():
    """获取所有token名，用于后续索引"""
    global g_exclude_token_list
    try:
        time_start = time.time()
        
        # 使用新的配置系统
        proxies = _get_request_config()
        baseperp_url = config_manager.get('binance.perp_price_url')
        
        response = requests.get(baseperp_url, proxies=proxies, timeout=10)
        time_end = time.time()
        
        if response.status_code == 200:
            response_time = time_end - time_start
            logger.info(f"请求所有币种成功，响应时间：{response_time:.5f}秒")
        else:
            logger.error(f"请求所有币种失败，状态码: {response.status_code}")
            return None

        all_info = response.json()
        all_coin = []

        for info in all_info:
            coin = info["symbol"]
            # 检查是否在排除列表中
            should_exclude = False
            for notinclude in g_exclude_token_list:
                if notinclude in coin:
                    should_exclude = True
                    logger.debug(f'排除币种: {coin}')
                    break
            
            if not should_exclude:
                all_coin.append(coin)

        logger.info(f"获取到币种数量: {len(all_coin)}")
        return all_coin
    
    except Exception as e:
        logger.error(f"get_binance_all_coin error: {e}")
        return None

def monitor_coin_price(coin, interval_seconds):
    """监控币种价格变化"""
    while True:
        coin_price, server_time = get_binance_coin_price(coin)

        if coin_price is not None and server_time is not None:
            logger.info(f"交易对: {coin} | 时间: {server_time} | 价格: ${coin_price}")
        else:
            logger.error(f"无法获取 {coin} 价格")

        time.sleep(interval_seconds)

if __name__ == "__main__":
    # interval_seconds = 10
    # coin = "COMPUSDT"
    # monitor_coin_price(coin, interval_seconds)
    get_binance_all_coin()