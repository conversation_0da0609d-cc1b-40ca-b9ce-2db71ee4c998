"""
1.交易量异常
2.现货合约价格差别大
3.利用开盘价，最高价，最低价，收盘价这些信息组合出K柱的大概形态（十字星，（倒）T bar，上影线，下引线， sot）
4.持仓变化量
"""
import requests
import time
from datetime import datetime, timedelta, timezone
import concurrent.futures
import csv
import os
import multiprocessing
import json
import numpy as np

from src.getCoinPrice import get_binance_all_coin
from src.noticeMesg import func_notice_with_send_email,func_notice_with_send_email_beta
from src.discord import func_send_message_to_discord,func_send_message_to_discord_beta
from src.trade import func_trade_UM, func_trace_positions
from src.EMATrend import EMATracker,EMATrendProcessor
import src.config as config
from src.data_layer import binance_api

# Global variables for state management
# TODO: Refactor these into a shared state management class
g_mux_lock = False
g_trace_flag = False

g_unfinished_token = []
g_finished_num = 0
g_unfinised_num = 0
g_abnormal_vol_token = []
g_getklines_flag = False

g_openinterest = []
g_all_exist = []
g_trace_list = []

closeprice_1h = []
g_abnormal_oi = []

g_oi_level_list = []
g_oi_level_500 = []
g_oi_level_1000 = []
g_oi_level_2000 = []
g_oi_level_5000 = []
g_oi_level_10000 = []
g_oi_level_nolimit = []
g_oi_level = []
g_oilevel_flag = False

g_ematrend_flag = False
g_if_traceema = False
g_trackema_token = []
g_ematrend_list = []


def pretty_print_json(json_context):
    try:
        # 将JSON字符串解析为Python对象
        if type(json_context) is dict or type(json_context) is list:
            json_obj = json_context
        elif type(json_context) is str:
            json_obj = json.loads(json_context)

        # 将Python对象转换为格式化的JSON字符串
        pretty_json_str = json.dumps(json_obj, indent=4)

        print(pretty_json_str)

    except json.JSONDecodeError as e:
        print("Failed to parse JSON:", e)


def gmt_to_utc(gmt_time):
    """Converts GMT time to UTC time."""
    # Assuming gmt_time is a datetime object
    return gmt_time - timedelta(hours=8)


"""
[
    [
        1499040000000,      // k线开盘时间
        "0.01634790",       // 开盘价
        "0.80000000",       // 最高价
        "0.01575800",       // 最低价
        "0.01577100",       // 收盘价(当前K线未结束的即为最新价)
        "148976.11427815",  // 成交量
        1499644799999,      // k线收盘时间
        "2434.19055334",    // 成交额
        308,                // 成交笔数
        "1756.87402397",    // 主动买入成交量
        "28.46694368",      // 主动买入成交额
        "17928899.62484339" // 请忽略该参数
    ]
]
"""


def func_detect_token_abnormal_signal(symbol, base_size, window_size, threshold, end_time):
    global g_finished_num
    global g_abnormal_vol_token
    global g_unfinished_token
    try:
        if end_time is None:  # 传值了就用传入的，没有则获取现在的时间
            end_time = datetime.now()
        start_time = end_time - timedelta(hours=window_size)

        all_kinfo = binance_api.get_klines_info(symbol, start_time, end_time, base_size)
        
        # if all_kinfo is None:
        #     all_kinfo = get_klines_info(symbol, start_time, end_time, base_size)
        #     if all_kinfo is None:
        #         return
        #     else:
        #         g_unfinished_token.remove(symbol)
        if all_kinfo is None:
            return
        
        g_finished_num += 1

        timestamp = all_kinfo[-1][0]/1000
        cur_time = datetime.fromtimestamp(timestamp)

        cur_openprice = 0.0
        cur_closeprice = 0.0
        cur_quotevolume = 0.0

        cur_openprice = float(all_kinfo[-1][1])
        cur_closeprice = float(all_kinfo[-1][4])
        cur_quotevolume = float(all_kinfo[-1][7])
        
        closeprice_1h = []
        ema_25 = []
        ema_30 = []
        ema_45 = []
        ema_50 = []
        
        avg_quotevolume = 0.0
        for index in range(0, len(all_kinfo)):
            avg_quotevolume += (float(all_kinfo[index][7]))
            closeprice_1h.append(float(all_kinfo[index][4]))
        avg_quotevolume /= len(all_kinfo)
        volume_change_percentage = 0.0

        if avg_quotevolume != 0:
            volume_change_percentage = (cur_quotevolume - avg_quotevolume) / avg_quotevolume * 100
        # 1.判断交易量是否异常
        if volume_change_percentage < threshold:
            return
        
        # 2.过滤器：1.涨幅是否满足要求；2.ema是否看涨；3.交易时间间隔是否满足
        increase = (float)((cur_closeprice-cur_openprice)/cur_openprice)
        if increase < 0.02:
            return
        
        # 计算EMA组合：短期25周期和长期50周期
        ema_25 = calculate_ema(closeprice_1h, 25)
        ema_50 = calculate_ema(closeprice_1h, 50)
        
        # 添加EMA数组长度校验
        if len(ema_25) < 2 or len(ema_50) < 2:
            return
            
        # 判断EMA金叉：当前EMA25上穿EMA50 且 前一根EMA25在EMA50下方
        if not (ema_25[-2] < ema_50[-2] and ema_25[-1] > ema_50[-1]):
            return
        
        if func_filter_with_4hema(symbol, cur_time) == False:
            return
        
        # 继续下去需要该symbol满足时间间隔
        if update_trace_list(symbol, timestamp) != 0: 
            return
        print('symbol:', symbol, 'cur_quotevolume:', cur_quotevolume)
        g_abnormal_vol_token.append(
            {"token": symbol, "volume_chg_percent": volume_change_percentage, "current_volume": cur_quotevolume, "increase":increase*100})
                    
    except Exception as e:
        g_unfinished_token.append(symbol)
        print("get ", symbol, "Error:", e)


def func_trace_abnormal_alert(symbol, base_size, start_time, end_time):
    try:
        quotevolume, openprice, closeprice, highprice, lowprice = binance_api.get_klines_info(
            symbol, start_time, end_time, base_size)
        if len(highprice) > 0:
            # print(highprice,lowprice)
            first_k = [openprice[0], closeprice[0]]
            print("symbol:", symbol,
                "openprice[0]:", openprice[0], "closeprice[0]:", closeprice[0])
            increase = (float)((closeprice[0]-openprice[0])/openprice[0])
            tmp_increase = str(round(increase*100, 2))+"%"
            if increase > 0.03:
                increase = 0.03
            size = len(highprice)
            write_data = []
            print("increase:", increase, "size:", size)

            for i in range(1, size):
                # print("i:", i)
                if lowprice[i] < first_k[0]:
                    print("track run fail.", i, lowprice[i])
                #         field_names = ['token', 'start_time',
                # 'end_time', 'is_success']
                    # write_data = {'token':symbol, 'start_time':start_time, 'end_time':end_time, 'is_success':False}
                    write_data.append({'token': symbol, 'start_time': start_time, 'end_time': start_time +
                                    timedelta(hours=i), 'increase': tmp_increase, 'is_success': "fail"})
                    write_data_to_csv(write_data, "./alert_data/record.csv")
                    return
                if highprice[i] >= (1+2*increase)*first_k[1]:
                    print("trace run success.", i, highprice[i])
                    # write_data = {'token':symbol, 'start_time':start_time, 'end_time':end_time, 'is_success':True}
                    # write_data = [symbol, start_time, end_time, True]
                    write_data.append({'token': symbol, 'start_time': start_time, 'end_time': start_time +
                                    timedelta(hours=i), 'increase': tmp_increase, 'is_success': "success"})
                    write_data_to_csv(write_data, "./alert_data/record.csv")
                    return
            print("trace not end.")
            # write_data = {'token':symbol, 'start_time':start_time, 'end_time':end_time, 'is_success':False}
            # write_data = [symbol, start_time, end_time, False]
            write_data.append({'token': symbol, 'start_time': start_time, 'end_time': start_time +
                            timedelta(hours=i), 'increase': tmp_increase, 'is_success': "not end"})
            write_data_to_csv(write_data, "./alert_data/record.csv")

    except Exception as e:
        g_unfinished_token.append(symbol)
        print("get ", symbol, "Error:", e)


def func_multi_thread_detect_token_abnormal_signal(*args):
    # 每次处理的币种交易对数量
    batch_size = 8

    # 将币种交易对分批处理，每次最多处理batch_size个
    batches = [args[0][i:i+batch_size]
            for i in range(0, len(args[0]), batch_size)]

    # 创建线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=batch_size) as executor:
        # 提交任务到线程池
        if len(args) == 5:
            futures = [executor.submit(func_detect_token_abnormal_signal, coin, args[1],
                                    args[2], args[3], args[4]) for batch in batches for coin in batch]
        else:
            print("error args num", args, "need 5")

        # 获取结果
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"获取成交量时出错: {e}")


def func_multi_thread_trace_abnormal_alert(*args):
    # 每次处理的币种交易对数量
    batch_size = 8

    # 将币种交易对分批处理，每次最多处理batch_size个
    batches = [args[0][i:i+batch_size]
            for i in range(0, len(args[0]), batch_size)]

    # 创建线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=batch_size) as executor:
        # 提交任务到线程池
        if len(args) == 4:
            futures = [executor.submit(func_trace_abnormal_alert, coin, args[1],
                                    args[2], args[3]) for batch in batches for coin in batch]
        else:
            print("error args num", args, "need 4")

        # 获取结果
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"获取成交量时出错: {e}")


"""
{
    "symbol": "BTCUSDT",
  "priceChange": "-94.99999800",    //24小时价格变动
  "priceChangePercent": "-95.960",  //24小时价格变动百分比
  "weightedAvgPrice": "0.29628482", //加权平均价
  "lastPrice": "4.00000200",        //最近一次成交价
  "lastQty": "200.00000000",        //最近一次成交额
  "openPrice": "99.00000000",       //24小时内第一次成交的价格
  "highPrice": "100.00000000",      //24小时最高价
  "lowPrice": "0.10000000",         //24小时最低价
  "volume": "8913.30000000",        //24小时成交量
  "quoteVolume": "15.30000000",     //24小时成交额
  "openTime": 1499783499040,        //24小时内，第一笔交易的发生时间
  "closeTime": 1499869899040,       //24小时内，最后一笔交易的发生时间
  "firstId": 28385,   // 首笔成交id
  "lastId": 28460,    // 末笔成交id
  "count": 76         // 成交笔数
}
"""


def get_binance_market_capital():
    try:
        response = requests.get(config.HR_URL, proxies=config.PROXIES, timeout=5)
        data = response.json()
        print("len(data):", len(data))
        if isinstance(data, list):
            # 对数据按照成交量进行排序
            sorted_data = sorted(data, key=lambda x: float(
                x['quoteVolume']), reverse=True)

            # 打印市值排名前50的币种
            for i in range(50):
                symbol = sorted_data[i]['symbol']
                quote_volume = float(sorted_data[i]['quoteVolume'])
                # print(f"Rank {i+1}: {symbol}, 24h Quote Volume: {quote_volume:.2f}")

                # 将quote_volume转换为亿、千万等单位的字符串说明
                volume_str = ''
                if quote_volume >= 1e8:
                    volume_str = f"{quote_volume / 1e8:.2f} 亿"
                elif quote_volume >= 1e7:
                    volume_str = f"{quote_volume / 1e7:.2f} 千万"
                else:
                    volume_str = f"{quote_volume:.2f}"

                print(f"Rank {i+1}: {symbol}, 24h 交易量: {volume_str} 美元")
        else:
            print("Failed to fetch data.")
    except requests.exceptions.RequestException as e:
        print("Request exception:", e)


def write_data_to_csv(data_list, file_name):
    # 指定CSV文件的字段名称
    field_names = ['token', 'start_time',
                'end_time', 'increase', 'profit', 'is_success']

    file_exists = os.path.exists(file_name)
    with open(file_name, mode='a', newline='') as csv_file:
        writer = csv.DictWriter(csv_file, fieldnames=field_names)
        if not file_exists:
            writer.writeheader()
        writer.writerows(data_list)


def get_whattime_now():
    try:
        response = requests.get("http://worldtimeapi.org/api/ip")
        response.raise_for_status()
        data = response.json()
        network_time = data["datetime"]
        return datetime.fromisoformat(network_time.replace("Z", "+00:00"))
    except requests.exceptions.RequestException as e:
        print("获取网络时间失败:", e)
        print("获取本地时间...")
        network_time = datetime.now()
        return network_time


def is_current_to_get_klines(time_gap, is_test=False):
    while True:
        global g_getklines_flag
        # return True #test
        # if g_getklines_flag is False:
        #     # 间隔10秒进行下一次判断
        #     time.sleep(10)
        if is_test:
            return True

        time.sleep(20)
        # else:
        #     # 在执行过一次获取后延长休眠的时间，也同时避免掉一分钟内做多次获取的操作
        #     time.sleep(30)
        #     g_getklines_flag = False

        # 首先尝试获取网络时间
        # network_time = get_whattime_now()
        network_time = datetime.now()
        # if network_time is None:
        #     network_time = datetime.now()

        if (time_gap == "1h"):
            if (network_time.minute == 14 or network_time.minute == 29 or
                    network_time.minute == 44 or network_time.minute == 59) and network_time.second <= 30:
                g_getklines_flag = True
                print("is_current_to_get_klines network_time:", network_time)
                return True

        elif (time_gap == "4h"):
            if (network_time.hour+1) % 4 == 0 and network_time.minute == 58:
                g_getklines_flag = True
                print("network_time:", network_time)
                return True

        elif (time_gap == "1d"):
            if (network_time.hour+1) == 8 and network_time.minute == 57:
                g_getklines_flag = True
                print("network_time:", network_time)
                return True

        # # 获取网络时间失败或不可用，回退到本地时间 Etc/GMT+8 sudo timedatectl set-timezone Asia/Hong_Kong
        # now = datetime.now()
        # if (now.minute == 14 or now.minute == 29 or now.minute == 44 or now.minute == 59):
        #     g_getklines_flag = True
        #     print("now:", now)
        #     return True

def is_current_to_trace():
    while True:
        time.sleep(18)
        network_time = datetime.now()
        
        if (network_time.minute == 14 or network_time.minute == 29 or
                network_time.minute == 44 or network_time.minute == 59) and network_time.second <= 20:
            print("is_current_to_trace network_time:", network_time)
            return True


def get_open_interest(symbol, period):
    try:
        response = requests.get(config.OI_URL, params={
                                "symbol": symbol,  # 合约交易对，例如："BTCUSDT"
                                "period": period   # 查询的时间区间，例如："1d"表示一天的数据，"1h"表示一小时的数据
                                }, proxies=config.PROXIES, timeout=5)
        data = response.json()
        print("data:", data)
        # if isinstance(data, list):
        #     for item in data:
        #         print(f"Timestamp: {item['timestamp']}, Open Interest: {item['openInterest']}")
        # else:
        #     print("Failed to fetch data.")
    except requests.exceptions.RequestException as e:
        print("Request exception:", e)

# #判断K线形态，并可以灵活设置阈值（实体与影线的比例）
# def k_line_pattern(open_price, close_price, low_price, high_price, thresholds, tolerance_val):
#     # 计算涨跌情况
#     is_rising = close_price > open_price
#     is_falling = close_price < open_price

#     # 实体长度
#     body_length = abs(close_price - open_price)

#     # 上影线
#     upper_shadow = high_price - max(open_price, close_price)

#     # 下影线
#     lower_shadow = min(open_price, close_price) - low_price

#     if upper_shadow <= tolerance_val and lower_shadow <= tolerance_val:#sot
#         pass
#     else:
#         # if
#         pass
#     # 判断十字星、T Bar和倒T Bar
#     if body_length/(upper_shadow+lower_shadow) <= thresholds["crosstar"] and upper_shadow > 0 and lower_shadow > 0:
#         return "十字星"
#     elif body_length == 0 and upper_shadow > 0 and lower_shadow == 0:
#         return "T Bar"
#     elif body_length == 0 and upper_shadow == 0 and lower_shadow > 0:
#         return "倒T Bar"

#     # 判断上影线、下影线和实体的方向
#     shadow_direction = "无影线"
#     body_direction = "无实体"
#     if is_rising:
#         if upper_shadow > 0 and lower_shadow == 0:
#             shadow_direction = "上影线"
#         elif upper_shadow == 0 and lower_shadow > 0:
#             shadow_direction = "下影线"
#         body_direction = "上涨实体"
#     elif is_falling:
#         if upper_shadow == 0 and lower_shadow > 0:
#             shadow_direction = "上影线"
#         elif upper_shadow > 0 and lower_shadow == 0:
#             shadow_direction = "下影线"
#         body_direction = "下跌实体"

#     # 判断SOT形态
#     sot_pattern = "无SOT"
#     if shadow_direction != "无影线" and body_direction != "无实体":
#         sot_pattern = "SOT"

#     # 判断形态是否符合阈值条件
#     for pattern, threshold in thresholds.items():
#         if pattern == "十字星" and body_length != 0:
#             continue  # 十字星没有实体，所以忽略实体阈值
#         if body_length / (upper_shadow + lower_shadow) < threshold:
#             return f"{pattern}（不符合阈值要求）"

#     return f"涨跌：{'涨' if is_rising else '跌'}，实体长度：{body_length:.2f}，{shadow_direction}，{body_direction}，SOT形态：{sot_pattern}"

# # 示例数据
# open_price = 100.0
# close_price = 110.0
# low_price = 95.0
# high_price = 115.0

# # 形态阈值字典，每种形态的阈值是实体与影线的比值范围
# thresholds = {
#     "十字星": (0.1, 0.2),
#     "T Bar": (0.0, 0.0),  # T Bar没有实体，所以实体与影线的比值为0
#     "倒T Bar": (0.0, 0.0),  # 倒T Bar没有实体，所以实体与影线的比值为0
#     "上涨实体": (0.2, float('inf')),  # 上涨实体，实体与影线的比值至少为0.2
#     "下跌实体": (0.2, float('inf')),  # 下跌实体，实体与影线的比值至少为0.2
#     "VSB": (0.2, float('inf')),  # VSB，实体与影线的比值至少为0.2
#     "VDB": (0.2, float('inf')),  # VDB，实体与影线的比值至少为0.2
#     "SOT": (0.0, 0.1)  # SOT，实体与影线的比值最大为0.1
# }

# result = k_line_pattern(open_price, close_price, low_price, high_price, thresholds)
# print("K线图形态:", result)


def func_filter_abnormal_volume(tokens, num_limit):
    result_str = ""
    global g_all_exist
    flag_once_warning = False
    # global g_trace_list
    global g_abnormal_vol_token
    
    if len(tokens) > 0:
        # 对g_abnormal_vol_token按volume_change_percentage进行排序
        sorted_tokens = sorted(tokens,
                            key=lambda x: x['volume_chg_percent'], reverse=True)
        # 限制结果大小为10个
        sorted_tokens = sorted_tokens[:num_limit]
        # 遍历list中的每个信息
        for ab_vol_token in sorted_tokens[:]:
            # print("ab_vol_token:", ab_vol_token)

            if ab_vol_token["current_volume"] < 1e6:  # 交易额太小可信度不高所以过滤掉
                sorted_tokens.remove(ab_vol_token)
                continue
            # 异常波动时有这几个，说明整体行情在有比较大的波动
            if (ab_vol_token["token"].find("BTC") >= 0) or (ab_vol_token["token"].find("ETH") >= 0) or (ab_vol_token["token"].find("BNB") >= 0):
                if flag_once_warning is False:
                    result_str += "当前行情波动较大，请注意风险！！！\n"
                flag_once_warning = True
                sorted_tokens.remove(ab_vol_token)
                continue

            # g_trace_list.append(ab_vol_token["token"])
        #     # 遍历每个信息中的键值对
        #     info_str = '\n'.join(f"{key}: {value}" for key, value in ab_vol_token.items())

            # 遍历每个信息中的键值对
            info_list = []
            for key, value in ab_vol_token.items():
                if key == "current_volume":
                    quote_volume = value
                    if quote_volume >= 1e8:
                        volume_str = f"{quote_volume / 1e8:.2f} 亿"
                    elif quote_volume >= 1e7:
                        volume_str = f"{quote_volume / 1e7:.2f} 千万"
                    elif quote_volume >= 1e6:
                        volume_str = f"{quote_volume / 1e6:.2f} 百万"

                    volume_str += "美元"
                    value = f"({volume_str})"
                if key == "volume_chg_percent" or key == "increase":
                    value = str(round(value, 2))+"%"
                if key == "token":
                    g_all_exist.append(value)
                info_list.append(f"{key}: {value}")
                # print("type(info_list):", type(info_list))
            info_str = '\n'.join(info_list)
            result_str += f"{info_str}\n"
            result_str += '--------------------\n'
            
        g_abnormal_vol_token = sorted_tokens
    return result_str


def func_filter_abnormal_openiterest(tokens, num_limit):
    result_str = ""
    # global g_all_exist
    # tmp_all_exist = []
            # g_abnormal_oi.append({"token":symbol, "cur_oi":cur_oi, "chg_oi":chg_oi, "chg_percent":chg_percent})

    if len(tokens) > 0:
        # 对g_abnormal_vol_token按volume_change_percentage进行排序
        sorted_tokens = sorted(tokens,
                            key=lambda x: x['chg_percent'], reverse=True)
        # 限制结果大小为10个
        sorted_tokens = sorted_tokens[:num_limit]
        # 遍历list中的每个信息
        for ab_openinterest_token in sorted_tokens:
            # print("ab_openinterest_token:", ab_openinterest_token)

            if ab_openinterest_token["cur_oi"] < 1e6:  # 交易额太小可信度不高所以过滤掉
                continue

            # 遍历每个信息中的键值对
            info_list = []
            for key, value in ab_openinterest_token.items():
                if key == "cur_oi": #or key == "chg_oi":
                    quote_volume = value
                    if quote_volume >= 1e8:
                        volume_str = f"{quote_volume / 1e8:.2f} 亿"
                    elif quote_volume >= 1e7:
                        volume_str = f"{quote_volume / 1e7:.2f} 千万"
                    elif quote_volume >= 1e6:
                        volume_str = f"{quote_volume / 1e6:.2f} 百万"

                    volume_str += "美元"
                    value = f"({volume_str})"
                if key == "chg_percent":
                    value = str(round(value, 2))+"%"
                # if key == "token":
                #     for exist in g_all_exist:
                #         if value == exist:
                #             tmp_all_exist.append(value)
                info_list.append(f"{key}: {value}")

            info_str = '\n'.join(info_list)
            result_str += f"{info_str}\n"
            result_str += '--------------------\n'

        # g_all_exist = tmp_all_exist
    return result_str

def func_filter_oi_level(tokens, num_limit):
    result_str = ""
    # global g_all_exist
    # tmp_all_exist = []
    # g_trace_list.append({'symbol': symbol, 'start_time': start_time, 'oi_level':oi_level, 'oi_value':oi_value})
    
    if len(tokens) > 0:
        # 对g_abnormal_vol_token按volume_change_percentage进行排序
        sorted_tokens = sorted(tokens,
                            key=lambda x: x['oi_value'], reverse=True)
        # 限制结果大小为10个
        sorted_tokens = sorted_tokens[:num_limit]
        # 遍历list中的每个信息
        for ab_openinterest_token in sorted_tokens:
            # print("ab_openinterest_token:", ab_openinterest_token)

            if ab_openinterest_token["oi_value"] < 1e6:  # 交易额太小可信度不高所以过滤掉
                continue

            # 遍历每个信息中的键值对
            info_list = []
            for key, value in ab_openinterest_token.items():
                if key == "oi_value":
                    quote_volume = value
                    if quote_volume >= 1e8:
                        volume_str = f"{quote_volume / 1e8:.2f} 亿"
                    elif quote_volume >= 1e7:
                        volume_str = f"{quote_volume / 1e7:.2f} 千万"
                    elif quote_volume >= 1e6:
                        volume_str = f"{quote_volume / 1e6:.2f} 百万"

                    volume_str += "美元"
                    value = f"({volume_str})"
                if key == "4h_chg_per":
                    value = str(round(value, 2))+"%"

                info_list.append(f"{key}: {value}")

            info_str = '\n'.join(info_list)

            result_str += f"{info_str}\n"
            result_str += '--------------------\n'

        # g_all_exist = tmp_all_exist
    return result_str

""" 
time_gap:k线检测间隔
average:前面多少k线的平均值
threshold:波动率异常触发的阈值
require_limit:警示最大发送的条目数
"""


def func_find_alpha_process(lock, time_gap, average, threshold, require_limit):
    while True:
        try:
            if is_current_to_get_klines(time_gap):
                lock.acquire()
                global g_abnormal_vol_token
                global g_unfinished_token
                global g_openinterest
                global g_all_exist
                global g_finished_num

                time_start = time.time()
                all_coin = get_binance_all_coin()
                g_abnormal_vol_token = []
                g_unfinished_token = []
                g_openinterest = []
                g_all_exist = []
                if all_coin is None:
                    continue

                g_finished_num = 0
                print("func_get_" + time_gap + "_alpha_process running...")
                func_multi_thread_detect_token_abnormal_signal(
                    all_coin, time_gap, average, threshold, None)
                print("g_finished_num:", g_finished_num)
                print("len(g_unfinished_token):", len(g_unfinished_token),
                    "g_unfinished_token:", g_unfinished_token)
                # 获取失败的再获取一次
                if len(g_unfinished_token) > 0:
                    func_multi_thread_detect_token_abnormal_signal(
                        g_unfinished_token, time_gap, average, threshold, None)
                result_str = ""
                result_str = func_filter_abnormal_volume(
                    g_abnormal_vol_token, require_limit)
                if result_str != "":
                    result_str += "-------------------split-----------------\n"
                result_str += func_filter_abnormal_openiterest(
                    g_openinterest, require_limit)
                if result_str != "":
                    if g_all_exist is not None:
                        tmp_str = ""
                        for all_exist in g_all_exist:
                            print("all_exist:", all_exist)
                            tmp_str += str(all_exist)
                            tmp_str += " "
                        tmp_str += "\n-------------------split-----------------\n"
                        result_str = tmp_str + result_str

                    func_notice_with_send_email(
                        time_gap+"_abnormal_token", result_str, "")

                lock.release()
                time_end = time.time()
                time_consume = time_end-time_start
                print("time_consume:", time_consume)

        except Exception as e:
            func_notice_with_send_email(
                "func_get_" + time_gap + "_alpha_process running error", " ", "")
            print("Error:", e)


def get_binance_market_cap():
    try:
        url = "https://api.binance.com/api/v3/exchangeInfo"
        response = requests.get(
            url, params={"symbol": "BTCUSDT"}, proxies=proxies, timeout=5)
        data = response.json()
        # print("data:", data, "len(data)", len(data))
        # pretty_print_json(data)
        if 'symbols' in data:
            symbols = data['symbols']
            token_info = []

            for symbol in symbols:
                base_asset = symbol['baseAsset']
                quote_asset = symbol['quoteAsset']

                # 只处理币对中的交易对，忽略合约等其他类型
                if quote_asset == 'USDT':
                    print("base_asset:", base_asset)
                    url = "https://api.binance.com/api/v1/tokenInfo?symbol=BTC"  # 流通量获取不到作废
                    response = requests.get(url, proxies=proxies, timeout=5)
                    print("---test 1---")

                    data = response.json()
                    print("---test 2---")

                    circulating_supply = 0.0
                    if 'circulatingSupply' in data:
                        print("---test 3---")
                        circulating_supply = float(data['circulatingSupply'])

                    # 获取币种实时价格
                    ticker_url = f"https://api.binance.com/api/v3/ticker/price?symbol={base_asset}{quote_asset}"
                    ticker_response = requests.get(
                        ticker_url, proxies=proxies, timeout=5)
                    ticker_data = ticker_response.json()
                    # pretty_print_json(ticker_data)
                    if 'price' in ticker_data:
                        price = float(ticker_data['price'])
                        market_cap = circulating_supply * price
                        token_info.append((base_asset, market_cap))

            # 对市值进行排序，取前10名
            sorted_tokens = sorted(
                token_info, key=lambda x: x[1], reverse=True)
            for i, (symbol, market_cap) in enumerate(sorted_tokens[:10]):
                print(
                    f"Rank {i+1}: {symbol}, Market Cap: {market_cap:.2f} USDT")

        else:
            print("Failed to fetch data.")

    except requests.exceptions.RequestException as e:
        print("Request exception:", e)
    except ValueError as e:
        print("Failed to parse JSON response:", e)


"""
1.持仓量异常可能存在潜在机会 
2.持仓量与价格背离可能存在潜在反转
3.持仓多空比与价格背离（如：价格升高，多空比降低；价格降低，多空比升高）
"""


def get_openinterest(symbol, start_time, end_time, period):
    try:
        # end_time = datetime.now()
        # start_time = end_time - timedelta(hours=20)
        avg_sumopeninterest = 0.0
        current_sumopeninterest = 0.0
        open_interests = []
        start_time = int(start_time.timestamp() * 1000)
        end_time = int(end_time.timestamp() * 1000)
        response = requests.get(openinterest_url,
                                params={
                                    "symbol": symbol,
                                    "period": period, "limit": 500, "startTime": start_time, "endTime": end_time
                                }, proxies=proxies, timeout=5)
        data = response.json()
        # pretty_print_json(data)
        # print(len(data))
        for info in data:
            symbol = info['symbol']
            timestamp = info['timestamp']
            sumOpenInterest = info['sumOpenInterest']
            current_sumopeninterest = float(info['sumOpenInterestValue'])
            open_interests.append(current_sumopeninterest)
        #     avg_sumopeninterest += float(current_sumopeninterest)
        # if avg_sumopeninterest != 0:
        #     avg_sumopeninterest /= len(data)
        return open_interests  # current_sumopeninterest, avg_sumopeninterest

    except requests.exceptions.RequestException as e:
        print("Request exception:", e)
    except ValueError as e:
        print("Failed to parse JSON response:", e)


def find_change_percent(datas):
    avg_datas = sum(datas)/len(datas)
    current_datas = datas[-1]
    return current_datas, avg_datas


def find_oi_prices_divergence(open_interests, prices, window_size, threshold):
    divergences = []
    acc_pos_val = 0
    acc_neg_val = 0
    acc_val = 0
    print("open_interests:", open_interests)
    print("prices:", prices)

    for i in range(len(open_interests) - window_size):
        # 获取当前时间窗口内的未平仓数和价格列表
        oi_window = open_interests[i:i+window_size]
        prices_window = prices[i:i+window_size]

        # 计算未平仓数和价格的平均值
        avg_oi = round(sum(oi_window) / len(oi_window), 2)
        avg_price = round(sum(prices_window) / len(prices_window), 2)

        # 检查背离现象
        oi_chg_percent = round((float)((oi_window[-1] - avg_oi)/avg_oi)*100, 2)
        price_chg_percent = round(
            (float)((prices_window[-1] - avg_price)/avg_price)*100, 2)
        # print(f"{symbol}openinterest_change_percentage:{openinterest_change_percentage:.2f}%" )

        print(f"avg_oi:{avg_oi}, oi_window[-1]: {oi_window[-1]:.2f}, avg_price: {avg_price}, oi_chg_percent: {oi_chg_percent:.2f}%, price_chg_percent: {price_chg_percent:.2f}%, ratio:{oi_chg_percent/price_chg_percent:.2f}")
        # print("avg_oi:", avg_oi, "oi_window[-1]:", oi_window[-1] ,"avg_price:", avg_price, "oi_chg_percent:", oi_chg_percent, "price_chg_percent:", price_chg_percent)
        if abs(oi_chg_percent) > threshold:
            if oi_chg_percent/price_chg_percent > 2:
                acc_val += 1
            if oi_chg_percent > 0 and price_chg_percent < 0:
                acc_pos_val += 1
            if oi_chg_percent < 0 and price_chg_percent > 0:
                acc_neg_val += 1

        # if abs(acc_pos_val-acc_neg_val)/(acc_pos_val+acc_neg_val)
    print("acc_val", acc_val, "acc_pos_val:",
        acc_pos_val, "acc_neg_val:", acc_neg_val)
    # if(acc_pos_val >= (len(open_interests) - window_size)*0.3) or (acc_neg_val >= (len(open_interests) - window_size)*0.3):
    #     return True
    if (acc_pos_val >= (len(open_interests) - window_size)*0.3):
        return True
    return False


def trace_abnormal_alert(trace_list, current_date):
    print("trace_list:", trace_list)
    start_time = current_date
    end_time = current_date + timedelta(hours=24)
    func_multi_thread_trace_abnormal_alert(
        trace_list, "1h", start_time, end_time)


def func_backtest_alpha_alert(time_gap, average, threshold):
    year = 2023
    month = 7

# specified_datetime = datetime(2023, 7, 25, 17, 00, 00)
    start_date = datetime(year, month, 1, 00, 00)  # 7月1日 00:00
    end_date = datetime(year, month, 31, 23, 45)  # 7月31日 23:45
    time_interval = timedelta(minutes=15)

    current_date = start_date
    day = current_date.day
    tmp_day = 0
    tmp_used_list = []
    all_coin = get_binance_all_coin()

    while current_date <= end_date:

        global g_abnormal_vol_token
        global g_unfinished_token
        global g_openinterest
        global g_all_exist
        global g_finished_num
        global g_trace_list

        time_start = time.time()
        g_abnormal_vol_token = []
        g_unfinished_token = []
        g_openinterest = []
        g_all_exist = []
        g_trace_list = []

        if tmp_day != day:
            tmp_used_list = []
            tmp_day = day

        if all_coin is None:
            time.sleep(1)
            all_coin = get_binance_all_coin()
            continue

        g_finished_num = 0
        func_multi_thread_detect_token_abnormal_signal(
            all_coin, time_gap, average, threshold, current_date)
        print("g_finished_num:", g_finished_num)
        print("len(g_unfinished_token):", len(g_unfinished_token),
            "g_unfinished_token:", g_unfinished_token)
        # 获取失败的再获取一次
        if len(g_unfinished_token) > 0:
            func_multi_thread_detect_token_abnormal_signal(
                g_unfinished_token, time_gap, average, threshold, current_date)

        result_str = ""
        result_str = func_filter_abnormal_volume(g_abnormal_vol_token, 10)
        print("result_str:", result_str)

        # 进行后续追踪
        if g_trace_list is not None:
            trace_list = []
            for val in g_trace_list:
                if val not in tmp_used_list:
                    tmp_used_list.append(val)
                    trace_list.append(val)
            if trace_list is not None:
                trace_abnormal_alert(trace_list, current_date)

        print(current_date)
        current_date += time_interval
        time_end = time.time()
        time_consume = time_end-time_start

        print("time_consume:", time_consume)
        # time.sleep(20)

""""
用于辅助追踪ema状态，当满足条件ema20>ema30>ema50时，且是第一次满足，如果不是第一次满足，则需要记录的状态是false，且上一次触发的间隔时间大于3天
1.新的symbol符合追踪条件时进行追踪
2.已有的symbol：
    2.1正在追踪的，当追踪条件不满足时退出追踪
    2.2已结束追踪的，用来判断新的追踪开始时，追踪的间隔时间是否符合要求
"""
def track_ema_trend(symbol, start_time, ema20, ema30, ema50):
    global g_ematrend_list #ema追踪信息
    global g_ematrend_flag #本次查询后是否有改变的ema信息
    global g_if_traceema #本symbol是否需要追踪
    
    if symbol is None:
        return -3
    
    g_if_traceema = False
    if ema20 > ema30 > ema50:
        g_if_traceema = True
    # else:
    #     g_if_traceema = False
    
    # 有新增symbol，或者更新时间则需要同步更新本地json文件
    for key in g_ematrend_list:
        if key['symbol'] == symbol:
            if key['track_flag'] is True:
                if g_if_traceema is False:
                    if ema20 >= ema50:
                        g_if_traceema = True
                    else:                        
                        key['track_flag'] = g_if_traceema
                        g_ematrend_flag = True
                return -1
            else:
                if g_if_traceema is True:
                    if abs(key['start_time']-start_time) >= 86400*3:  # 3天的差值
                            key['start_time'] = start_time
                            key['track_flag'] = g_if_traceema
                            g_ematrend_flag = True
                            return 0
                    return -2
                else:
                    return -2
            
    if g_if_traceema is True:
        g_ematrend_list.append({'symbol': symbol, 'start_time': start_time, "track_flag":g_if_traceema})
        g_ematrend_flag = True
        return 0
    else:
        return -3
        
        
# class EMATracker:

#     def __init__(self, filepath=None):
#         self.ematrend_list = []  # EMA追踪信息
#         self._ematrend_flag = False  # 本次查询后是否有改变的EMA信息
#         self.filepath = filepath  # JSON文件路径
#         if filepath:
#             self.load_from_file()
    
#     def check_ema_condition(self, ema20, ema30, ema50):
#         return ema20 > ema30 > ema50

#     def find_symbol(self, symbol):
#         for ema_info in self.ematrend_list:
#             if ema_info['symbol'] == symbol:
#                 return ema_info
#         return None

#     def track_ema_trend(self, symbol, start_time, ema20, ema30, ema50):
#         if symbol is None:
#             raise ValueError("Symbol cannot be None")
        
#         ema_info = self.find_symbol(symbol)

#         if ema_info is None:  # 新的symbol符合追踪条件时进行追踪
#             if self.check_ema_condition(ema20, ema30, ema50):
#                 self.ematrend_list.append({
#                     'symbol': symbol, 
#                     'start_time': start_time, 
#                     'track_flag': True
#                 })
#                 self.set_ematrend_flag(True)

#         else:  # 已有的symbol
#             if ema_info['track_flag']:  # 正在追踪的
#                 if ema20 < ema50:  # 退出追踪的条件
#                     ema_info['track_flag'] = False
#                     # self.set_ematrend_flag(True)
#             else:  # 已结束追踪的
#                 if self.check_ema_condition(ema20, ema30, ema50):
#                     last_start_time = datetime.datetime.strptime(ema_info['start_time'], "%Y-%m-%d %H:%M:%S")
#                     current_start_time = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
#                     if (current_start_time - last_start_time).days >= 3:
#                         ema_info['start_time'] = start_time
#                         ema_info['track_flag'] = True
#                         self.set_ematrend_flag(True)

#     def load_from_file(self):
#         try:
#             with open(self.filepath, 'r') as file:
#                 self.ematrend_list = json.load(file)
#         except FileNotFoundError:
#             print(f"Warning: File {self.filepath} not found. Starting with an empty list.")
#         except json.JSONDecodeError:
#             print(f"Warning: Could not decode JSON from {self.filepath}. Starting with an empty list.")
#         except Exception as e:
#             print(f"An unexpected error occurred: {e}")

#     @property
#     def ematrend_flag(self):
#         """返回 ematrend_flag 的值."""
#         return self._ematrend_flag

#     def set_ematrend_flag(self, value):
#         """设置 ematrend_flag 的值."""
#         self._ematrend_flag = value

#     def save_to_file(self):
#         if self._ematrend_flag:  # 根据标志位判断是否需要写入文件
#             try:
#                 with open(self.filepath, 'w') as file:
#                     json.dump(self.ematrend_list, file, indent=4)
#             except Exception as e:
#                 print(f"An error occurred while saving to {self.filepath}: {e}")
#             else:
#                 self._ematrend_flag = False  # 重置标志位
                
            
# tracker = EMATracker("./trade_data/ematrend_list.json")
            
def update_trace_list(symbol, start_time):
    global g_trace_list
    global g_trace_flag
    
    if symbol is None:
        return -3
    # 有新增symbol，或者更新时间则需要同步更新本地json文件
    for key in g_trace_list:  # 使用切片 [:] 来创建原始列表的副本，以避免在迭代过程中修改列表大小导致的问题。
        if key['symbol'] == symbol:
            if abs(key['start_time']-start_time) >= 86400:  # 一天的差值
                key['start_time'] = start_time
                g_trace_flag = True

                return 0
            else:
                return -1
    g_trace_list.append({'symbol': symbol, 'start_time': start_time})
    g_trace_flag = True
    return 0

def update_oi_level(symbol, start_time, oi_value, chg_per):

    global g_oi_level
    global g_oilevel_flag
    
    if symbol is None:
        return -3
    # 有新增symbol，或者更新时间则需要同步更新本地json文件
    for key in g_oi_level:  # 使用切片 [:] 来创建原始列表的副本，以避免在迭代过程中修改列表大小导致的问题。
        if key['symbol'] == symbol:
            if abs(key['start_time']-start_time) >= 86400:  # 1d的差值
                key['start_time'] = start_time
                key['oi_value'] = oi_value
                
                g_oilevel_flag = True

                return 0
            else:
                return -1
    g_oi_level.append({'symbol': symbol, 'start_time': start_time, 'oi_value':oi_value, '4h_chg_per':chg_per})
    g_oilevel_flag = True
    return 0

def func_get_klinesinfo(symbol):
    global g_unfinished_token
    try:
        year = 2021
        month = 12
        start_time = datetime(year, month, 1, 00, 00)  # 7月1日 00:00
        start_time = start_time-timedelta(hours=60)
        end_time = datetime(year, month, 31, 23, 45)  # 7月31日 23:45
        # 最后符合的,也要追踪后续24h.所以时间范围是[60h+start_time, end_time+24h]
        end_time = end_time + timedelta(hours=24)
        time_interval = timedelta(minutes=15*500)  # 最大获取条数为500
        interval = "15m"
        all_kinfo = []
        current_time = start_time
        global g_abnormal_vol_token
        g_abnormal_vol_token = []

        while current_time <= end_time:

            start_time = current_time
            tmp_end_time = end_time
            start_time = int(start_time.timestamp() * 1000)
            tmp_end_time = int(tmp_end_time.timestamp() * 1000)
            time_start = time.time()
            response = requests.get(perpkline_url, params={
                "symbol": symbol,
                "interval": interval, "startTime": start_time, "endTime": tmp_end_time
                # ,"apikey": API_KEY
            }, proxies=proxies, timeout=5)
            time_end = time.time()
            data = response.json()
            # print(len(data))
            all_kinfo += data

            current_time += time_interval
            # time.sleep(1)
        print("symbol:", symbol, "len(all_kinfo):", len(all_kinfo))

        # with open("./alert_data/2021_11_01-2021_11_30/"+symbol+".json", "w") as json_file: 
        with open("./alert_data/2021_12_01-2021_12_31/"+symbol+".json", "w") as json_file: 

            json.dump(all_kinfo, json_file, indent=4)
        # return all_kinfo

    except Exception as e:
        g_unfinished_token.append(symbol)
        print("get_"+symbol+"_kinfo Error:", e)
        return None


def func_multi_thread_trace_abnormal_signal(*args):
    # 每次处理的币种交易对数量
    batch_size = 3

    # 将币种交易对分批处理，每次最多处理batch_size个
    batches = [args[0][i:i+batch_size]
            for i in range(0, len(args[0]), batch_size)]

    # 创建线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=batch_size) as executor:
        # 提交任务到线程池
        if len(args) == 1:
            futures = [executor.submit(func_get_klinesinfo, coin)
                    for batch in batches for coin in batch]
        else:
            print("error args num", args, "need 1")

        # 获取结果
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"func_multi_thread_trace_abnormal_signal: {e}")


def calculate_ema(data, n):
    ema_values = []
    if len(data) >= n:
        sma_initial = sum(data[:n]) / n  # 使用SMA作为初始EMA值

        ema_values.append(sma_initial)

        multiplier = 2 / (n + 1)

        for i in range(n, len(data)):
            ema = (data[i] - ema_values[-1]) * multiplier + ema_values[-1]
            ema_values.append(ema)
            
    return ema_values

def func_filter_with_4hema(symbol, end_time):
    try:
        if end_time is None:
            end_time = datetime.now()
        start_time = end_time-timedelta(hours=50*4)
        kinfos = binance_api.get_klines_info(symbol, start_time, end_time, "4h")
        if kinfos is None:
            kinfos = binance_api.get_klines_info(symbol, start_time, end_time, "4h")

        # print(len(kinfos))
            
        if len(kinfos) > 0:
            closeprice_4h = []
            for kinfo in kinfos:
                closeprice_4h.append(float(kinfo[4]))
            
            ema_20 = calculate_ema(closeprice_4h, 20) 
            ema_30 = calculate_ema(closeprice_4h, 30)
            if len(ema_20) == 0 or len(ema_30) == 0:
                return True
            
            # print("len(ema_20):", len(ema_20), "len(ema_30):", len(ema_30))
            # print('ema_20[-1]:', ema_20[-1], 'ema_30[-1]:', ema_30[-1])
            if ema_20[-1] > ema_30[-1]:
                return True
            else:
                return False
        else:
            return False
        
    except Exception as e:
        print("func_filter_with_4hema ", symbol, "Error:", e)
        return False


def func_backtest_abnormal_signal():
    global g_unfinished_token
    global closeprice_1h

    ema_25 = []
    ema_30 = []
    ema_45 = []
    ema_50 = []

    try:
        # 获取K线数据
        all_token = get_binance_all_coin()
        func_multi_thread_trace_abnormal_signal(all_token)
        if len(g_unfinished_token) > 0:
            print("len(g_unfinished_token):", len(g_unfinished_token))
            print("g_unfinished_token:", g_unfinished_token)
        func_multi_thread_trace_abnormal_signal(g_unfinished_token)

        testlist = [] 
        for filename in os.listdir("./alert_data/2021_12_01-2021_12_31"):
            # for filename in os.listdir("./alert_data/2023_03_01-2023_03_31/"):
            # for filename in os.listdir("./alert_data/test/"):

            json_data = []
            symbol = ""
            if filename.endswith(".json"):
                file_path = os.path.join(
                    "./alert_data/2021_12_01-2021_12_31/", filename)
                # file_path = os.path.join("./alert_data/2023_03_01-2023_03_31/", filename)
                # file_path = os.path.join("./alert_data/test/", filename)

                symbol = filename.split(".json")[0]
                with open(file_path, "r") as file:
                    try:
                        json_data = json.load(file)
                    except json.JSONDecodeError:
                        print(f"Error reading JSON from file: {file_path}")
            # print(json_data)
            all_kinfo = json_data
            
        #     if len(all_kinfo) < 3216 and len(all_kinfo) != 0:
        #         testlist.append(symbol)
        # print(len(testlist))
        # print(testlist)
        
            print("symbol:", symbol, len(all_kinfo))
            closeprice_1h = []

            for i in range(0, len(all_kinfo)-24*4):
                if (i+1) % 4 == 0:
                    closeprice_1h.append(float(all_kinfo[i][4]))

            # print("---test1---")

            ema_25 = calculate_ema(closeprice_1h, 25)
            ema_30 = calculate_ema(closeprice_1h, 30)
            ema_45 = calculate_ema(closeprice_1h, 45)
            ema_50 = calculate_ema(closeprice_1h, 50)
            # print("---test2---")
            # if(len(all_kinfo) != 3312):
            #     g_unfinished_token.append(symbol)
            window = 60*4
            if len(all_kinfo) < window:
                continue

            for i in range(window, len(all_kinfo)-24*4):

                timestamp = all_kinfo[i][0]/1000
                # print("all_kinfo[i][0]:", all_kinfo[i][0])
                # 将时间戳转换为 datetime 对象
                # datetime_object
                cur_time = datetime.fromtimestamp(timestamp)
                # 格式化成可阅读的时间字符串
                # cur_time = datetime_object.strftime('%Y-%m-%d %H:%M:%S')
                # 以此1h线形成的K线作为判断依据
                cur_openprice = 0.0
                cur_closeprice = 0.0
                cur_quotevolume = 0.0

                if (i+1) % 4 == 1:
                    cur_openprice = float(all_kinfo[i][1])
                    cur_closeprice = float(all_kinfo[i][4])
                    cur_quotevolume = float(all_kinfo[i][5])
                elif (i+1) % 4 == 2:
                    cur_openprice = float(all_kinfo[i-1][1])
                    cur_closeprice = float(all_kinfo[i][4])
                    cur_quotevolume = float(
                    all_kinfo[i][5]) + float(all_kinfo[i-1][5])
                elif (i+1) % 4 == 3:
                    cur_openprice = float(all_kinfo[i-2][1])
                    cur_closeprice = float(all_kinfo[i][4])
                    cur_quotevolume = float(
                        all_kinfo[i][5]) + float(all_kinfo[i-1][5]) + float(all_kinfo[i-2][5])
                elif (i+1) % 4 == 0:
                    cur_openprice = float(all_kinfo[i-3][1])
                    cur_closeprice = float(all_kinfo[i][4])
                    cur_quotevolume = float(all_kinfo[i][5]) + float(
                        all_kinfo[i-1][5]) + float(all_kinfo[i-2][5]) + float(all_kinfo[i-3][5])

                # print("cur_time:", cur_time)

                avg_quotevolume = 0.0
                for index in range(i-window+1, i+1):
                    avg_quotevolume += (float(all_kinfo[index][5]))
                avg_quotevolume = 4*avg_quotevolume/window

                volume_change_percentage = 0.0

                if avg_quotevolume != 0:
                    volume_change_percentage = (
                        cur_quotevolume - avg_quotevolume) / avg_quotevolume * 100

                threshold = 300  # 70
                if volume_change_percentage >= threshold:
                    increase = (float)(
                        (cur_closeprice-cur_openprice)/cur_openprice)
                    if increase >= 0.02:
                        # print("---test3---")
                        pos_ema25 = int((i-window)/4)+int(window/4)-25
                        pos_ema30 = int((i-window)/4)+int(window/4)-30
                        pos_ema45 = int((i-window)/4)+int(window/4)-45
                        pos_ema50 = int((i-window)/4)+int(window/4)-50

                        # print("len(ema_25):", len(ema_25))
                        # print("len(ema_30):", len(ema_30))
                        # print("len(ema_45):", len(ema_45))
                        # print("len(ema_50):", len(ema_50))
                        # print("pos_ema25:", pos_ema25)
                        # print("pos_ema30:", pos_ema30)
                        # print("pos_ema45:", pos_ema45)
                        # print("pos_ema50:", pos_ema50)

                        # 在本根k还未走完时,是判断前一根k是否已经满足要求
                        if ema_25[pos_ema25] < ema_45[pos_ema45] or ema_30[pos_ema30] < ema_50[pos_ema50]:
                            continue

                        # print("2 ", cur_time, ema_25[pos_ema25], ema_30[pos_ema30], ema_45[pos_ema45], ema_50[pos_ema50])

                        # 如果该symbol还在追踪中则不进行后续处理
                        if update_trace_list(symbol, timestamp) != 0:
                            continue
                        first_k = [cur_openprice, cur_closeprice]

                        if increase > 0.03:
                            increase = 0.03

                        tmp_increase = str(round(increase*100, 2))+"%"

                        write_data = []
                        tmp_max_val = 0.0
                        tmp_j = 0
                        flag_trace = True
                        for j in range(1, 24*4):
                            if i+j+1 < len(all_kinfo):
                                if float(all_kinfo[i+j][3]) < first_k[0]:
                                    # print("track run fail.", i, float(all_kinfo[i+j][3]))
                                    write_data.append({'token': symbol, 'start_time': cur_time, 'end_time': cur_time+timedelta(
                                        minutes=j*15), 'increase': "-"+tmp_increase, 'profit': -increase, 'is_success': "fail"})
                                    # print("1 write_data:", write_data)

                                    write_data_to_csv(
                                        write_data, "./alert_data/2021_12_01-2021_12_31/202112_record"+".csv")
                                    # write_data_to_csv(write_data, "./alert_data/2023_06_01-2023_06_30/202306_record"+".csv")
                                    j = 24*4
                                    flag_trace = False
                                    break
                                if float(all_kinfo[i+j][2]) >= (1+2*increase)*first_k[1]:
                                    # print("trace run success.", i, float(all_kinfo[i+j][2]))
                                    max_val = 0.0
                                    tmp_k = 0
                                    for k in range(j, 24*4):
                                        if i+k+1 < len(all_kinfo):
                                            if float(all_kinfo[i+k][4]) > max_val:
                                                # print("i+k:", i+k)
                                                max_val = float(
                                                    all_kinfo[i+k][4])
                                                tmp_k = k
                                    up_per = str(
                                        round((max_val-first_k[1])/first_k[1]*100, 2))+"%"

                                    write_data.append({'token': symbol, 'start_time': cur_time, 'end_time': cur_time+timedelta(
                                        minutes=tmp_k*15), 'increase': up_per, 'profit': (max_val-first_k[1])/first_k[1], 'is_success': "success"})
                                    # print("2 write_data:", write_data)

                                    write_data_to_csv(
                                        write_data, "./alert_data/2021_12_01-2021_12_31/202112_record"+".csv")

                                    j = 24*4
                                    flag_trace = False
                                    break
                                if float(all_kinfo[i+j][4]) > tmp_max_val:
                                    tmp_max_val = float(all_kinfo[i+j][4])
                                    tmp_j = j
                        if flag_trace is True:
                            # print("trace not end.")
                            up_per = str(
                                round((tmp_max_val-first_k[1])/first_k[1]*100, 2))+"%"
                            write_data.append({'token': symbol, 'start_time': cur_time, 'end_time': cur_time+timedelta(
                                minutes=tmp_j*15), 'increase': up_per, 'profit': (tmp_max_val-first_k[1])/first_k[1], 'is_success': "not end"})
                            # print("3 write_data:", write_data)

                            write_data_to_csv(
                                write_data, "./alert_data/2021_12_01-2021_12_31/202112_record"+".csv")

                                    # write_data_to_csv(write_data, "./alert_data/2023_06_01-2023_06_30/202306_record"+".csv")

    except Exception as e:
        print("func_backtest_abnormal_signal Error:", e)
        return None

def func_get_all_token():
    bak_allcoin = []
    all_coin = []
    all_coin = get_binance_all_coin()
    
    if all_coin is not None:
        file_path = './trade_data/alltoken.json'
        with open(file_path, "r") as file:
            try:
                bak_allcoin = json.load(file)
            except json.JSONDecodeError:
                print(f"Error reading JSON from file: {file_path}")
                
        if bak_allcoin != all_coin:
            bak_allcoin = all_coin
            try:
                with open(file_path, "w") as json_file:
                    json.dump(bak_allcoin, json_file, indent=4)
            except json.JSONDecodeError:
                print(f"Error writing JSON to file: {file_path}")
        
    if all_coin is None:
        # TODO:弄一个本地的列表，如果网络获取失败，则读取本地列表
        file_path = './trade_data/alltoken.json'
        with open(file_path, "r") as file:
            try:
                all_coin = json.load(file)
            except json.JSONDecodeError:
                print(f"Error reading JSON from file: {file_path}")
    
    return all_coin

def func_trade_bot_use_alpha_signal(time_gap, average, threshold, require_limit):
    while True:
        try:
            if is_current_to_get_klines(time_gap):
                global g_abnormal_vol_token
                global g_unfinished_token
                global g_finished_num
                global g_trace_list
                global g_trace_flag
                global g_abnormal_oi
                global g_oi_level
                global g_oilevel_flag
                global g_oi_level_list
                
                time_start = time.time()
                g_abnormal_vol_token = []
                g_unfinished_token = []
                g_abnormal_oi = []
                g_oi_level_list = []
                
                all_coin = func_get_all_token()

                g_finished_num = 0
                print("func_trade_bot_use_alpha_signal running...")
                
                if len(g_trace_list) == 0:
                    file_path = "./trade_data/tracelist.json"
                    with open(file_path, "r") as file:
                        try:
                            g_trace_list = json.load(file)
                        except json.JSONDecodeError:
                            print(f"Error reading JSON from file: {file_path}")     
                            
                if len(g_oi_level) == 0:
                    file_path = "./trade_data/oilevel.json"
                    with open(file_path, "r") as file:
                        try:
                            g_oi_level = json.load(file)
                        except json.JSONDecodeError:
                            print(f"Error reading JSON from file: {file_path}")     
                
                func_multi_thread_detect_token_abnormal_signal(
                    all_coin, time_gap, average, threshold, None)
                print("g_finished_num:", g_finished_num)
                print("len(g_unfinished_token):", len(g_unfinished_token),
                    "g_unfinished_token:", g_unfinished_token)
                # 获取失败的再获取一次
                if len(g_unfinished_token) > 0:
                    func_multi_thread_detect_token_abnormal_signal(
                        g_unfinished_token, time_gap, average, threshold, None)
                
                g_unfinished_token = []
                func_multi_thread_trace_abnormal_oi(all_coin)
                if len(g_unfinished_token) > 0:
                    func_multi_thread_trace_abnormal_oi(g_unfinished_token)

                    
                result_str = ""
                result_str = func_filter_abnormal_volume(
                    g_abnormal_vol_token, require_limit)
                if len(g_abnormal_vol_token) > 0:
                    print("g_abnormal_vol_token:", g_abnormal_vol_token)
                    # for token in g_abnormal_vol_token:
                    #     result_str = func_trade_UM(token['token'], token['cur_openprice'], token['cur_closeprice'])+result_str
                
                if len(g_abnormal_oi) > 0:
                    print("g_abnormal_oi:", g_abnormal_oi)    
                    result_str += func_filter_abnormal_openiterest(g_abnormal_oi, require_limit)

                if len(g_oi_level_list) > 0:
                    print("g_oi_level_list:", g_oi_level_list)
                    result_str += func_filter_oi_level(g_oi_level_list, require_limit)
                
                if g_trace_flag is True:
                    try:
                        file_path = "./trade_data/tracelist.json"
                        with open(file_path, "w") as json_file:
                            json.dump(g_trace_list, json_file, indent=4)
                            g_trace_flag = False
                    except json.JSONDecodeError:
                        print(f"Error writing JSON to file: {file_path}")    

                if g_oilevel_flag is True:
                    try:
                        file_path = "./trade_data/oilevel.json"
                        with open(file_path, "w") as json_file:
                            json.dump(g_oi_level, json_file, indent=4)
                            g_oilevel_flag = False
                    except json.JSONDecodeError:
                        print(f"Error writing JSON to file: {file_path}")    
                        
                if result_str != "":
                    func_notice_with_send_email(
                        "abnormal_token_alert", result_str, "")
                    func_send_message_to_discord(result_str)
                    print("result_str:", result_str)

                time_end = time.time()
                time_consume = time_end-time_start
                print("time_consume:", time_consume)
            
        except Exception as e:
            func_notice_with_send_email(
                "func_trade_bot_use_alpha_signal running error", " ", "")
            print("Error:", e)

def func_trace_positions_process():
    while True:
        ret_str = ""
        try:
            if is_current_to_trace():
                ret_str,is_success = func_trace_positions()
            
                if ret_str != "":
                    func_notice_with_send_email("func_trace_positions_process", ret_str, "")
        except Exception as e:
            func_notice_with_send_email(
                "func_trace_positions_process running error", ret_str, "")
            print("Error:", e)
            
def func_trace_abnormal_oi(symbol):
                # symbol = 'SPELLUSDT'
    global g_abnormal_oi
    global g_unfinished_token
    global g_oi_level_500
    global g_oi_level_1000
    global g_oi_level_2000
    global g_oi_level_5000
    global g_oi_level_10000
    global g_oi_level_nolimit
    global g_oi_level_list
    
    try:
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=30)
        oi_data = binance_api.get_open_interest_hist(symbol, '15m', start_time, end_time)
        # oi_data = oi_data[:2]
        
        if oi_data is None:
            return
        if len(oi_data) != 2:
            return
        
        # print(len(oi_data), oi_data)
        
        last_oi = float(oi_data[-2]['sumOpenInterestValue'])
        cur_oi = float(oi_data[-1]['sumOpenInterestValue'])
        cur_timestamp = float(oi_data[-1]['timestamp'])

        if float(cur_oi/last_oi) > 1.2: 
            # true_time = datetime.fromtimestamp(cur_timestamp/1000)
            chg_oi = cur_oi - last_oi
            chg_percent = round(float(chg_oi/last_oi)*100,2)
            g_abnormal_oi.append({"token":symbol, "cur_oi":cur_oi, "chg_oi":chg_oi, "chg_percent":chg_percent})


        end_time = datetime.now()
        start_time = end_time - timedelta(hours=4)
        oi_data = binance_api.get_open_interest_hist(symbol, '1h', start_time, end_time)
        
        if oi_data is None:
            return
        if len(oi_data) != 4:
            return
        
        # print(len(oi_data), oi_data)
        last_4h_oi = float(oi_data[0]['sumOpenInterestValue'])
        cur_oi = float(oi_data[-1]['sumOpenInterestValue'])
        cur_timestamp = float(oi_data[-1]['timestamp'])
        
        if float(cur_oi/last_4h_oi) > 1.2: 
            # true_time = datetime.fromtimestamp(cur_timestamp/1000)
            chg_oi = cur_oi - last_4h_oi
            chg_percent = round(float(chg_oi/last_4h_oi)*100,2)
            if update_oi_level(symbol, cur_timestamp/1000, cur_oi, chg_percent) == 0:
                g_oi_level_list.append({'symbol':symbol, 'oi_value':cur_oi, '4h_chg_per':chg_percent})
            
    except Exception as e:
        g_unfinished_token.append(symbol)
        print("func_trace_abnormal_oi ", symbol, "Error:", e)

def func_multi_thread_trace_abnormal_oi(*args):
    # 每次处理的币种交易对数量
    batch_size = 8

    # 将币种交易对分批处理，每次最多处理batch_size个
    batches = [args[0][i:i+batch_size]
            for i in range(0, len(args[0]), batch_size)]

    # 创建线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=batch_size) as executor:
        # 提交任务到线程池
        if len(args) == 1:
            futures = [executor.submit(func_trace_abnormal_oi, coin) for batch in batches for coin in batch]
        else:
            print("error args num", args, "need 1")

        # 获取结果
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"func_multi_thread_trace_abnormal_signal: {e}")

def func_find_ematrend_process():
    while True:
        try:
            time_gap = "4h"
            if is_current_to_get_klines(time_gap):
                tracker = EMATracker("./trade_data/ematrend_list.json")
                processor = EMATrendProcessor(tracker)
                all_tokens = func_get_all_token()
                processor.process(all_tokens, time_gap)
                print("g_finished_num:", g_finished_num)
                
        except Exception as e:
            print("Error:", e)
            pass


if __name__ == "__main__":
    # for running
    processes = []

    p_find = multiprocessing.Process(target=func_trade_bot_use_alpha_signal, args=(config.TIME_GAP, config.AVERAGE, config.THRESHOLD, config.REQUIRE_LIMIT))
    processes.append(p_find)
    p_find.start()
    
    find_ematrend = multiprocessing.Process(target=func_find_ematrend_process)
    processes.append(find_ematrend)
    find_ematrend.start()
    
    for p in processes:
        p.join()

