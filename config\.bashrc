# Git Bash 配置文件
# 将此文件内容添加到 ~/.bashrc 或在每次启动Git Bash时source此文件

# 添加Node.js到PATH
export PATH="$PATH:/c/Program Files/nodejs"

# 添加Python uv到PATH（如果需要）
export PATH="$PATH:/c/Users/<USER>/.local/bin"

# 实用的别名
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias ..='cd ..'
alias ...='cd ../..'
alias grep='grep --color=auto'
alias fgrep='fgrep --color=auto'
alias egrep='egrep --color=auto'

# Git别名
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git pull'
alias gco='git checkout'
alias gb='git branch'
alias gd='git diff'
alias glog='git log --oneline --graph --decorate'

# Python/uv别名
alias activate='source .venv/Scripts/activate'
alias py='python'
alias pip='python -m pip'

# 实用函数
mkcd() {
    mkdir -p "$1" && cd "$1"
}

# 显示当前Git分支
parse_git_branch() {
    git branch 2> /dev/null | sed -e '/^[^*]/d' -e 's/* \(.*\)/(\1)/'
}

# 自定义提示符
export PS1='\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[01;31m\]$(parse_git_branch)\[\033[00m\]\$ '

# 设置默认编辑器
export EDITOR=nano

# 历史记录设置
export HISTSIZE=1000
export HISTFILESIZE=2000

echo "Git Bash环境配置已加载！"
echo "可用工具: node ($(node --version)), npm ($(npm --version)), uv ($(uv --version | cut -d' ' -f2))"
echo "可用别名: ll, la, gs, ga, gc, gp, gl, activate, py, pip" 