# 策略构建指南

## 📋 概述

本指南将帮助您构建新的交易策略。策略系统已经清理完毕，现在您可以从零开始构建自己的策略。

## 🏗️ 策略架构

### 保留的核心组件

1. **BaseStrategy** (`src/strategies/base.py`)
   - 所有策略的抽象基类
   - 定义了标准接口和通用功能
   - 包含回测、信号生成等核心方法

2. **BacktestEngine** (`src/strategies/backtest_engine.py`)
   - 策略回测引擎
   - 支持单策略和多策略回测
   - 提供详细的性能分析

3. **策略注册机制** (`src/strategies/__init__.py`)
   - 动态策略注册和管理
   - `register_strategy()` 函数用于注册新策略
   - `get_strategy()` 函数用于获取策略实例

## 🚀 创建新策略

### 步骤1：复制模板

```bash
cp src/strategies/strategy_template.py src/strategies/my_strategy.py
```

### 步骤2：修改策略类

```python
class MyStrategy(BaseStrategy):
    """我的自定义策略"""
    
    @classmethod
    def get_default_parameters(cls) -> Dict[str, Any]:
        return {
            'my_param1': 20,
            'my_param2': 0.02
        }
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        # 实现您的技术指标计算
        pass
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        # 实现您的信号生成逻辑
        pass
```

### 步骤3：注册策略

在策略文件末尾添加：

```python
from . import register_strategy
register_strategy('MyStrategy', MyStrategy)
```

### 步骤4：配置策略

在 `config/strategy.json` 中添加：

```json
{
  "enabled_strategies": ["MyStrategy"],
  "strategy_weights": {
    "MyStrategy": 1.0
  },
  "custom_params": {
    "MyStrategy": {
      "my_param1": 25,
      "my_param2": 0.03
    }
  }
}
```

## 📊 必须实现的方法

### 1. get_default_parameters()
```python
@classmethod
def get_default_parameters(cls) -> Dict[str, Any]:
    """返回策略的默认参数"""
    return {
        'period': 20,
        'threshold': 0.02
    }
```

### 2. get_required_columns()
```python
@classmethod
def get_required_columns(cls) -> List[str]:
    """返回策略需要的数据列"""
    return ['open', 'high', 'low', 'close', 'volume']
```

### 3. _validate_parameters()
```python
def _validate_parameters(self):
    """验证参数有效性"""
    if not 5 <= self.params['period'] <= 100:
        raise ValueError("period 应在5-100之间")
```

### 4. calculate_indicators()
```python
def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
    """计算技术指标"""
    df = data.copy()
    df['my_indicator'] = df['close'].rolling(self.params['period']).mean()
    return df
```

### 5. generate_signals()
```python
def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
    """生成交易信号"""
    df = data.copy()
    df['signal'] = SignalType.NEUTRAL.value
    df['confidence'] = 0.0
    df['reason'] = ''
    
    # 实现信号逻辑
    # ...
    
    return df
```

## 🎯 信号类型

使用 `SignalType` 枚举：

```python
from .base import SignalType

# 可用信号类型
SignalType.NEUTRAL      # 0  - 中性
SignalType.BUY          # 1  - 买入
SignalType.SELL         # -1 - 卖出
SignalType.STRONG_BUY   # 2  - 强买入
SignalType.STRONG_SELL  # -2 - 强卖出
```

## 🧪 测试策略

### 1. 单元测试
```python
# 创建测试文件
def test_my_strategy():
    strategy = MyStrategy({}, {})
    # 测试逻辑
```

### 2. 回测测试
```python
from src.strategies import BacktestEngine

engine = BacktestEngine()
strategy = MyStrategy({}, {})
results = engine.run_single_backtest(strategy, data, "BTCUSDT")
```

### 3. 实时测试
```python
# 在 auto_4h_monitor.py 中测试
python auto_4h_monitor.py
```

## 📈 策略开发最佳实践

### 1. 参数化设计
- 所有关键数值都应该是可配置参数
- 提供合理的默认值
- 添加参数验证

### 2. 错误处理
- 处理数据不足的情况
- 处理NaN值和异常数据
- 提供有意义的错误信息

### 3. 性能优化
- 使用向量化操作而不是循环
- 缓存计算结果
- 避免重复计算

### 4. 可读性
- 添加详细的注释
- 使用有意义的变量名
- 分解复杂的逻辑

## 🔧 可用的技术指标

您可以在 `src/core/indicators.py` 中找到预定义的技术指标：

- `calculate_sma()` - 简单移动平均
- `calculate_ema()` - 指数移动平均
- `calculate_rsi()` - 相对强弱指标
- `calculate_bollinger_bands()` - 布林带
- `calculate_macd()` - MACD
- 等等...

## 📋 示例策略

查看 `strategy_template.py` 获取完整的示例实现。

## 🚀 部署策略

1. 确保策略通过所有测试
2. 在配置文件中启用策略
3. 运行 `auto_4h_monitor.py` 开始监控
4. 监控日志和性能指标

## ⚠️ 注意事项

1. **风险管理**：始终包含止损和风险控制
2. **数据质量**：验证输入数据的完整性
3. **过拟合**：避免过度优化历史数据
4. **实盘测试**：先在模拟环境中充分测试

祝您构建出优秀的交易策略！
