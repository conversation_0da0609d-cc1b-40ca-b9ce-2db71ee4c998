# TBTrade Web可视化界面 (Streamlit版本)

## 项目结构

```
web/
├── streamlit_app.py         # 主应用入口
├── requirements.txt         # Python依赖
├── config.py               # 配置管理
├── pages/                  # 多页面应用
│   ├── 01_📊_回测分析.py
│   ├── 02_💹_实时交易.py
│   ├── 03_📈_系统监控.py
│   └── 04_💾_数据管理.py
├── components/             # 可复用组件
│   ├── __init__.py
│   ├── charts.py          # 图表组件
│   ├── tables.py          # 表格组件
│   ├── indicators.py      # 指标组件
│   └── forms.py           # 表单组件
├── utils/                  # 工具函数
│   ├── __init__.py
│   ├── data_loader.py     # 数据加载
│   ├── formatters.py      # 格式化工具
│   └── helpers.py         # 辅助函数
├── assets/                 # 静态资源
│   ├── styles.css         # 自定义样式
│   └── images/            # 图片资源
└── docs/                   # 文档
    └── USER_GUIDE.md       # 用户指南
```

## 技术栈

### 核心框架
- **Streamlit** - 主要Web框架
- **Plotly** - 交互式图表
- **Pandas** - 数据处理
- **NumPy** - 数值计算

### 可视化组件
- **Plotly Express** - 快速图表
- **Matplotlib** - 静态图表
- **Altair** - 声明式可视化
- **Streamlit-Echarts** - ECharts集成

### 数据处理
- **SQLite3** - 数据库连接
- **Requests** - HTTP请求
- **Threading** - 多线程处理

## 开发环境

### 安装依赖
```bash
cd web
pip install -r requirements.txt
```

### 启动应用
```bash
streamlit run streamlit_app.py
```

### 访问地址
- 本地访问: http://localhost:8501
- 网络访问: http://0.0.0.0:8501

## 功能模块

1. **📊 回测分析** - 交互式回测配置和结果可视化
2. **💹 实时交易** - 交易监控和手动操作界面
3. **📈 系统监控** - 系统状态和市场数据实时监控
4. **💾 数据管理** - 数据下载、更新和质量管理

## 特性优势

- **🚀 开发速度快** - 纯Python开发，无需前后端分离
- **🔗 完美集成** - 直接导入现有TBTrade系统模块
- **📱 响应式设计** - 自动适配桌面和移动设备
- **⚡ 实时更新** - 支持实时数据刷新和WebSocket
- **🎨 专业图表** - 内置丰富的金融数据可视化组件
