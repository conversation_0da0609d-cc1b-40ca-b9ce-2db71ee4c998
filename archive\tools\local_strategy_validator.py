#!/usr/bin/env python3
"""
基于本地数据的策略验证系统
Local Data Strategy Validator

基于本地历史数据进行策略验证，支持4小时K线数据的定时更新和策略测试
"""

import asyncio
import logging
import sqlite3
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

from src.core.config_manager import ConfigManager
from src.strategies import get_strategy, list_strategies
from src.services.signal_generator import StrategySignalGenerator
from src.services.execution_manager import ExecutionManager, LogNotifier
from src.data_layer.historical_data_fetcher import USDTHistoricalFetcher

class LocalStrategyValidator:
    """基于本地数据的策略验证系统"""
    
    def __init__(self, config_path: str = "./config", db_path: str = "./data/usdt_historical_data.db"):
        """
        初始化策略验证系统
        
        Args:
            config_path: 配置文件路径
            db_path: 本地数据库路径
        """
        # 加载配置
        self.config = ConfigManager(config_path)
        self.config.setup_logging()
        
        # 数据库路径
        self.db_path = Path(db_path)
        self.table_name = "usdt_klines_historical_4h"
        
        # 初始化组件
        self.signal_generator = None
        self.execution_manager = None
        self.data_fetcher = None
        
        # 系统状态
        self.is_running = False
        self.last_update_time = None
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
        # 监控的交易对
        self.symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT"]
        
    async def initialize(self):
        """初始化系统组件"""
        try:
            self.logger.info("🚀 初始化本地策略验证系统...")
            
            # 1. 检查数据库
            if not self.db_path.exists():
                self.logger.error(f"❌ 数据库文件不存在: {self.db_path}")
                raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
            
            # 2. 初始化信号生成器
            self.signal_generator = StrategySignalGenerator()
            
            # 3. 初始化执行管理器（仅日志模式）
            self.execution_manager = ExecutionManager(self.signal_generator)
            self.execution_manager.simulation_mode = True
            self.execution_manager.auto_execution = False
            
            # 添加日志通知器
            log_notifier = LogNotifier()
            self.execution_manager.add_notifier(log_notifier)
            
            # 4. 初始化数据获取器
            self.data_fetcher = USDTHistoricalFetcher(use_proxy=False)
            
            # 5. 设置策略
            await self._setup_strategies()
            
            # 6. 设置回调函数
            self._setup_callbacks()
            
            self.logger.info("✅ 系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 系统初始化失败: {e}")
            raise
    
    async def _setup_strategies(self):
        """设置策略"""
        strategy_config = self.config.get_strategy_config()
        
        # 为每个交易对添加策略
        for symbol in self.symbols:
            # 为每个启用的策略添加到信号生成器
            for strategy_name in strategy_config["enabled_strategies"]:
                # 获取自定义参数
                custom_params = strategy_config["custom_params"].get(strategy_name, {})
                
                # 添加策略
                self.signal_generator.add_strategy(symbol, strategy_name, custom_params)
                
                self.logger.info(f"🎯 为 {symbol} 添加策略: {strategy_name}")
        
        # 更新策略权重
        self.signal_generator.strategy_weights.update(strategy_config["strategy_weights"])
    
    def _setup_callbacks(self):
        """设置回调函数"""
        # 设置信号回调
        self.signal_generator.on_signal = self._on_signal_generated
    
    async def _on_signal_generated(self, signal: Dict[str, Any]):
        """信号生成回调"""
        self.logger.info(f"📈 生成信号: {signal['symbol']} - {signal['action']} (置信度: {signal['confidence']:.1f}%)")
        
        # 发送给执行管理器
        await self.execution_manager.process_signal(signal)
    
    def get_latest_data(self, symbol: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """从本地数据库获取最新数据"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            
            query = f"""
            SELECT symbol, open_time, open_price, high_price, low_price, close_price, volume, datetime_str
            FROM {self.table_name}
            WHERE symbol = ?
            ORDER BY open_time DESC
            LIMIT ?
            """
            
            df = pd.read_sql_query(query, conn, params=(symbol, limit))
            conn.close()
            
            if df.empty:
                return None
            
            # 转换数据格式
            df = df.rename(columns={
                'open_price': 'open',
                'high_price': 'high', 
                'low_price': 'low',
                'close_price': 'close'
            })
            
            # 转换时间
            df['datetime'] = pd.to_datetime(df['datetime_str'])
            df = df.sort_values('open_time').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 获取 {symbol} 数据失败: {e}")
            return None
    
    def check_data_freshness(self) -> Dict[str, Any]:
        """检查数据新鲜度"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            
            # 获取最新数据时间
            query = f"""
            SELECT symbol, MAX(open_time) as latest_time, datetime_str
            FROM {self.table_name}
            GROUP BY symbol
            ORDER BY symbol
            """
            
            cursor = conn.execute(query)
            results = cursor.fetchall()
            conn.close()
            
            data_status = {}
            current_time = datetime.now()
            
            for symbol, latest_timestamp, datetime_str in results:
                latest_time = datetime.fromtimestamp(latest_timestamp / 1000)
                time_diff = current_time - latest_time
                
                # 4小时数据，如果超过5小时就认为需要更新
                needs_update = time_diff > timedelta(hours=5)
                
                data_status[symbol] = {
                    'latest_time': latest_time,
                    'time_diff_hours': time_diff.total_seconds() / 3600,
                    'needs_update': needs_update,
                    'datetime_str': datetime_str
                }
            
            return data_status
            
        except Exception as e:
            self.logger.error(f"❌ 检查数据新鲜度失败: {e}")
            return {}
    
    async def update_data(self):
        """更新本地数据"""
        try:
            self.logger.info("🔄 开始更新本地数据...")
            
            # 检查数据新鲜度
            data_status = self.check_data_freshness()
            
            symbols_need_update = [
                symbol for symbol, status in data_status.items() 
                if status['needs_update']
            ]
            
            if not symbols_need_update:
                self.logger.info("✅ 本地数据已是最新，无需更新")
                return
            
            self.logger.info(f"📋 需要更新的交易对: {symbols_need_update}")
            
            # 计算更新时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(days=2)  # 获取最近2天的数据确保完整
            
            # 获取新数据
            all_klines = self.data_fetcher.batch_get_historical_data(
                symbols_need_update,
                interval='4h',
                start_time=start_time,
                end_time=end_time
            )
            
            if all_klines:
                # 保存到数据库
                from src.data_layer.historical_data_fetcher import save_klines_to_database
                
                conn = sqlite3.connect(str(self.db_path))
                save_klines_to_database(conn, self.table_name, all_klines)
                conn.close()
                
                self.logger.info(f"✅ 数据更新完成，新增 {len(all_klines)} 条记录")
                self.last_update_time = datetime.now()
            else:
                self.logger.warning("⚠️ 未获取到新数据")
                
        except Exception as e:
            self.logger.error(f"❌ 数据更新失败: {e}")
    
    async def validate_strategies(self):
        """验证策略"""
        try:
            self.logger.info("🧪 开始策略验证...")
            
            for symbol in self.symbols:
                # 获取最新数据
                df = self.get_latest_data(symbol, limit=200)
                
                if df is None or len(df) < 50:
                    self.logger.warning(f"⚠️ {symbol} 数据不足，跳过验证")
                    continue
                
                self.logger.info(f"📊 验证 {symbol} 策略 (数据量: {len(df)} 条)")
                
                # 生成信号
                await self.signal_generator.process_data(symbol, df)
            
            self.logger.info("✅ 策略验证完成")
            
        except Exception as e:
            self.logger.error(f"❌ 策略验证失败: {e}")
    
    def schedule_tasks(self):
        """设置定时任务"""
        # 每4小时更新一次数据（在整点后5分钟执行）
        schedule.every(4).hours.at(":05").do(lambda: asyncio.create_task(self.update_data()))
        
        # 每4小时验证一次策略（在整点后10分钟执行）
        schedule.every(4).hours.at(":10").do(lambda: asyncio.create_task(self.validate_strategies()))
        
        # 每天早上8点进行完整验证
        schedule.every().day.at("08:00").do(lambda: asyncio.create_task(self.full_validation()))
        
        self.logger.info("⏰ 定时任务已设置")
        self.logger.info("  - 数据更新: 每4小时 (整点后5分钟)")
        self.logger.info("  - 策略验证: 每4小时 (整点后10分钟)")
        self.logger.info("  - 完整验证: 每天早上8点")
    
    async def full_validation(self):
        """完整验证流程"""
        self.logger.info("🔄 开始完整验证流程...")
        
        # 1. 更新数据
        await self.update_data()
        
        # 2. 验证策略
        await self.validate_strategies()
        
        # 3. 生成报告
        await self.generate_report()
        
        self.logger.info("✅ 完整验证流程完成")
    
    async def generate_report(self):
        """生成验证报告"""
        try:
            self.logger.info("📊 生成验证报告...")
            
            # 获取数据状态
            data_status = self.check_data_freshness()
            
            # 获取执行统计
            stats = self.execution_manager.get_statistics()
            
            report = f"""
📊 **策略验证报告** - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**数据状态**:
"""
            
            for symbol, status in data_status.items():
                report += f"• {symbol}: {status['datetime_str']} ({status['time_diff_hours']:.1f}小时前)\n"
            
            report += f"""
**执行统计**:
• 总信号数: {stats.get('total_signals', 0)}
• 成功执行: {stats.get('successful_executions', 0)}
• 当前持仓: {len(stats.get('current_positions', []))}
• 总收益率: {stats.get('total_return', 0):.2f}%

**系统状态**:
• 运行状态: {'运行中' if self.is_running else '已停止'}
• 最后更新: {self.last_update_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_update_time else '未更新'}
• 监控交易对: {', '.join(self.symbols)}
"""
            
            self.logger.info(report)
            
        except Exception as e:
            self.logger.error(f"❌ 生成报告失败: {e}")
    
    async def start(self):
        """启动验证系统"""
        if self.is_running:
            self.logger.warning("⚠️ 系统已在运行中")
            return
        
        try:
            # 初始化系统
            await self.initialize()
            
            # 启动执行管理器
            self.execution_manager.start()
            
            # 设置定时任务
            self.schedule_tasks()
            
            self.is_running = True
            
            self.logger.info("🚀 本地策略验证系统已启动")
            
            # 执行初始验证
            await self.full_validation()
            
            # 开始定时任务循环
            await self._run_scheduler()
            
        except Exception as e:
            self.logger.error(f"❌ 启动失败: {e}")
            await self.stop()
            raise
    
    async def _run_scheduler(self):
        """运行定时任务调度器"""
        self.logger.info("⏰ 定时任务调度器已启动")
        
        try:
            while self.is_running:
                schedule.run_pending()
                await asyncio.sleep(60)  # 每分钟检查一次
                
        except Exception as e:
            self.logger.error(f"❌ 调度器运行错误: {e}")
    
    async def stop(self):
        """停止验证系统"""
        if not self.is_running:
            return
        
        self.logger.info("🛑 正在停止验证系统...")
        
        self.is_running = False
        
        if self.execution_manager:
            self.execution_manager.stop()
        
        self.logger.info("✅ 验证系统已停止")

async def main():
    """主函数"""
    print("🚀 启动本地策略验证系统")
    print("=" * 50)
    
    # 创建验证系统
    validator = LocalStrategyValidator()
    
    try:
        # 启动系统
        await validator.start()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在停止系统...")
    except Exception as e:
        print(f"❌ 系统运行出错: {e}")
    finally:
        await validator.stop()
        print("👋 系统已退出")

if __name__ == "__main__":
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 程序异常退出: {e}")
