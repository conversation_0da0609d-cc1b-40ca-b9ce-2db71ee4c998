# TBTrade Web API 文档

## 基础信息

- 基础URL: `http://localhost:5000/api`
- 内容类型: `application/json`
- WebSocket地址: `http://localhost:5000`

## API接口

### 回测相关

#### 运行回测
- **POST** `/api/backtest/run`
- **描述**: 启动新的回测任务
- **请求体**:
```json
{
  "symbols": ["BTCUSDT", "ETHUSDT"],
  "start_date": "2023-01-01",
  "end_date": "2024-01-01",
  "initial_capital": 10000,
  "strategy_params": {
    "ema_short": 21,
    "ema_medium": 55,
    "ema_long": 200
  }
}
```

#### 获取回测结果
- **GET** `/api/backtest/results/{task_id}`
- **描述**: 获取指定回测任务的结果

#### 获取回测历史
- **GET** `/api/backtest/history`
- **描述**: 获取历史回测记录列表

### 交易相关

#### 获取当前持仓
- **GET** `/api/trading/positions`
- **描述**: 获取当前所有持仓信息

#### 手动下单
- **POST** `/api/trading/order`
- **描述**: 执行手动交易订单

### 监控相关

#### 获取系统状态
- **GET** `/api/monitoring/status`
- **描述**: 获取系统运行状态

#### 获取市场数据
- **GET** `/api/monitoring/market/{symbol}`
- **描述**: 获取指定币种的市场数据

### 数据管理

#### 更新数据
- **POST** `/api/data/update`
- **描述**: 手动触发数据更新

#### 获取数据状态
- **GET** `/api/data/status`
- **描述**: 获取数据库状态信息

## WebSocket事件

### 回测进度
- **事件**: `backtest_progress`
- **数据**:
```json
{
  "task_id": "uuid",
  "progress": 75,
  "status": "processing",
  "current_date": "2023-06-15",
  "estimated_completion": "2023-12-01T10:30:00Z"
}
```

### 系统状态更新
- **事件**: `system_status`
- **数据**:
```json
{
  "cpu_usage": 45.2,
  "memory_usage": 68.5,
  "active_connections": 3
}
```

### 交易信号
- **事件**: `trading_signal`
- **数据**:
```json
{
  "symbol": "BTCUSDT",
  "signal_type": "BUY",
  "price": 45000,
  "timestamp": "2023-12-01T10:30:00Z"
}
```
