# 数据可视化工具使用指南

本项目提供了多种数据库可视化和分析工具，帮助您查看和分析交易数据。

## 🛠️ 可用工具

### 1. 简单数据库浏览器 (simple_db_browser.py)
**推荐使用** - 轻量级，无需额外依赖

```bash
# 查看数据库中的所有表
python simple_db_browser.py data/market_data.db --list

# 描述表结构和统计信息
python simple_db_browser.py data/market_data.db --describe klines

# 搜索包含BTC的交易对
python simple_db_browser.py data/market_data.db --search BTC

# 查询BTCUSDT最近7天的数据
python simple_db_browser.py data/market_data.db --query BTCUSDT --days 7

# 导出BTCUSDT数据到CSV文件
python simple_db_browser.py data/market_data.db --export BTCUSDT --format csv
```

### 2. 高级数据库可视化器 (db_visualizer.py)
**需要matplotlib** - 提供图表功能

```bash
# 安装依赖
pip install matplotlib seaborn

# 绘制BTCUSDT价格图表
python db_visualizer.py data/market_data.db --plot-symbol BTCUSDT --days 30

# 显示市场概览图表
python db_visualizer.py data/market_data.db --market-overview

# 导出数据到Excel
python db_visualizer.py data/market_data.db --export BTCUSDT --format excel
```

### 3. 现有分析脚本

#### 简单数据分析 (simple_data_analysis.py)
```bash
python simple_data_analysis.py
```

#### 高级市场分析器 (advanced_market_analyzer.py)
```bash
python advanced_market_analyzer.py
```

#### 已收集数据分析 (analyze_collected_data.py)
```bash
python analyze_collected_data.py
```

## 📊 数据库文件说明

项目中包含以下主要数据库文件：

### data/market_data.db
- **主要市场数据库**
- 包含实时和历史K线数据
- 表结构：klines (symbol, datetime, open, high, low, close, volume)

### data/usdt_1year_data.db
- **1年历史数据**
- USDT交易对的完整年度数据
- 适合长期趋势分析

### data/usdt_365days_4h_50pairs.db
- **365天4小时数据**
- 50个主要USDT交易对
- 4小时K线精度

### data/usdt_3months_data.db
- **3个月数据**
- 最近3个月的详细数据
- 适合短期分析

## 🔍 常用查询示例

### 1. 查看数据库概览
```bash
# 列出所有表
python simple_db_browser.py data/market_data.db --list

# 查看表结构
python simple_db_browser.py data/market_data.db --describe klines
```

### 2. 搜索和查询交易对
```bash
# 搜索所有BTC相关交易对
python simple_db_browser.py data/market_data.db --search BTC

# 搜索所有ETH相关交易对
python simple_db_browser.py data/market_data.db --search ETH

# 显示所有交易对（前20个）
python simple_db_browser.py data/market_data.db --search ""
```

### 3. 查看具体交易对数据
```bash
# 查看BTCUSDT最近7天数据
python simple_db_browser.py data/market_data.db --query BTCUSDT --days 7

# 查看ETHUSDT最近30天数据
python simple_db_browser.py data/market_data.db --query ETHUSDT --days 30
```

### 4. 数据导出
```bash
# 导出BTCUSDT数据到CSV
python simple_db_browser.py data/market_data.db --export BTCUSDT --format csv

# 导出前1000条记录到JSON
python simple_db_browser.py data/market_data.db --export all --format json --limit 1000
```

## 📈 图表功能 (需要matplotlib)

### 1. 价格走势图
```bash
# 绘制BTCUSDT 30天价格走势
python db_visualizer.py data/market_data.db --plot-symbol BTCUSDT --days 30

# 绘制ETHUSDT 7天价格走势
python db_visualizer.py data/market_data.db --plot-symbol ETHUSDT --days 7
```

### 2. 市场概览图
```bash
# 显示前10个交易对的市场概览
python db_visualizer.py data/market_data.db --market-overview
```

## 🛠️ 自定义查询

如果需要更复杂的查询，可以直接使用SQLite命令行工具：

```bash
# 安装SQLite (如果未安装)
# Windows: 下载sqlite3.exe
# Linux: sudo apt install sqlite3
# macOS: brew install sqlite3

# 连接数据库
sqlite3 data/market_data.db

# 示例查询
.tables                          # 显示所有表
.schema klines                   # 显示表结构
SELECT * FROM klines LIMIT 10;   # 查看前10条记录
SELECT DISTINCT symbol FROM klines; # 查看所有交易对
```

## 📝 输出文件说明

### CSV文件
- 标准逗号分隔格式
- 可用Excel或其他表格软件打开
- 适合进一步数据分析

### JSON文件
- 结构化数据格式
- 适合程序处理
- 包含完整的数据类型信息

### 图表文件 (PNG)
- 高分辨率图像 (300 DPI)
- 适合报告和演示
- 自动保存到当前目录

## ⚠️ 注意事项

1. **数据库文件路径**: 确保数据库文件存在且可访问
2. **Python依赖**: simple_db_browser.py 只需要pandas，db_visualizer.py 需要matplotlib
3. **内存使用**: 大量数据导出时注意内存使用，建议使用limit参数
4. **文件权限**: 确保有写入权限以保存导出文件和图表

## 🚀 快速开始

1. **查看数据库概览**:
   ```bash
   python simple_db_browser.py data/market_data.db --list
   ```

2. **搜索感兴趣的交易对**:
   ```bash
   python simple_db_browser.py data/market_data.db --search BTC
   ```

3. **查看具体数据**:
   ```bash
   python simple_db_browser.py data/market_data.db --query BTCUSDT --days 7
   ```

4. **导出数据进行分析**:
   ```bash
   python simple_db_browser.py data/market_data.db --export BTCUSDT --format csv
   ```

这些工具将帮助您深入了解交易数据，为策略开发和市场分析提供有力支持！
