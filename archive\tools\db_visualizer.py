#!/usr/bin/env python3
"""
数据库可视化工具
Database Visualizer Tool

提供SQLite数据库的可视化查看和分析功能
"""

import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
from datetime import datetime, timedelta
import numpy as np

class DatabaseVisualizer:
    """数据库可视化工具"""
    
    def __init__(self, db_path: str):
        """
        初始化数据库可视化工具
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        if not self.db_path.exists():
            raise FileNotFoundError(f"数据库文件不存在: {db_path}")
        
        self.conn = sqlite3.connect(str(self.db_path))
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置seaborn样式
        sns.set_style("whitegrid")
        
    def __del__(self):
        """析构函数，关闭数据库连接"""
        if hasattr(self, 'conn'):
            self.conn.close()
    
    def list_tables(self):
        """列出数据库中的所有表"""
        cursor = self.conn.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        """)
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 数据库: {self.db_path.name}")
        print(f"📋 包含 {len(tables)} 个表:")
        for i, table in enumerate(tables, 1):
            print(f"  {i}. {table}")
        
        return tables
    
    def describe_table(self, table_name: str):
        """描述表结构和基本统计信息"""
        print(f"\n📋 表: {table_name}")
        print("=" * 50)
        
        # 表结构
        cursor = self.conn.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        print("📝 表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 基本统计
        cursor = self.conn.execute(f"SELECT COUNT(*) FROM {table_name}")
        row_count = cursor.fetchone()[0]
        print(f"\n📊 记录数: {row_count:,}")
        
        # 如果是K线数据表，显示更多信息
        if any(col[1] in ['symbol', 'datetime', 'close'] for col in columns):
            self._describe_kline_table(table_name)
    
    def _describe_kline_table(self, table_name: str):
        """描述K线数据表的详细信息"""
        try:
            # 交易对数量
            cursor = self.conn.execute(f"SELECT COUNT(DISTINCT symbol) FROM {table_name}")
            symbol_count = cursor.fetchone()[0]
            print(f"📈 交易对数: {symbol_count}")
            
            # 时间范围
            cursor = self.conn.execute(f"""
                SELECT MIN(datetime), MAX(datetime) 
                FROM {table_name}
                WHERE datetime IS NOT NULL
            """)
            time_range = cursor.fetchone()
            if time_range[0]:
                print(f"⏰ 时间范围: {time_range[0]} ~ {time_range[1]}")
            
            # 热门交易对
            cursor = self.conn.execute(f"""
                SELECT symbol, COUNT(*) as count 
                FROM {table_name} 
                GROUP BY symbol 
                ORDER BY count DESC 
                LIMIT 5
            """)
            top_symbols = cursor.fetchall()
            print(f"🔥 热门交易对:")
            for symbol, count in top_symbols:
                print(f"  {symbol}: {count:,} 条记录")
                
        except Exception as e:
            print(f"⚠️ 无法获取K线表详细信息: {e}")
    
    def plot_symbol_price(self, table_name: str, symbol: str, days: int = 30):
        """绘制指定交易对的价格图表"""
        # 获取数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        query = f"""
            SELECT datetime, open, high, low, close, volume
            FROM {table_name}
            WHERE symbol = ? AND datetime >= ?
            ORDER BY datetime
        """
        
        df = pd.read_sql_query(query, self.conn, params=[symbol, start_date.isoformat()])
        
        if df.empty:
            print(f"❌ 未找到 {symbol} 的数据")
            return
        
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), height_ratios=[3, 1])
        
        # 价格图表
        ax1.plot(df['datetime'], df['close'], label='收盘价', linewidth=2)
        ax1.fill_between(df['datetime'], df['low'], df['high'], alpha=0.3, label='价格区间')
        ax1.set_title(f'{symbol} 价格走势 (最近{days}天)', fontsize=16)
        ax1.set_ylabel('价格', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 成交量图表
        ax2.bar(df['datetime'], df['volume'], alpha=0.7, color='orange', label='成交量')
        ax2.set_title('成交量', fontsize=14)
        ax2.set_ylabel('成交量', fontsize=12)
        ax2.set_xlabel('时间', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        output_path = f"chart_{symbol}_{days}days.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {output_path}")
        
        plt.show()
    
    def plot_market_overview(self, table_name: str, limit: int = 10):
        """绘制市场概览图表"""
        # 获取最新价格数据
        query = f"""
            SELECT symbol, close, volume,
                   (close - LAG(close) OVER (PARTITION BY symbol ORDER BY datetime)) / LAG(close) OVER (PARTITION BY symbol ORDER BY datetime) * 100 as change_pct
            FROM {table_name}
            WHERE datetime = (SELECT MAX(datetime) FROM {table_name})
            ORDER BY volume DESC
            LIMIT ?
        """
        
        df = pd.read_sql_query(query, self.conn, params=[limit])
        
        if df.empty:
            print("❌ 未找到市场数据")
            return
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 成交量排行
        ax1.barh(df['symbol'], df['volume'])
        ax1.set_title(f'成交量排行 (Top {limit})', fontsize=14)
        ax1.set_xlabel('成交量')
        
        # 价格分布
        ax2.hist(df['close'], bins=20, alpha=0.7, color='skyblue')
        ax2.set_title('价格分布', fontsize=14)
        ax2.set_xlabel('价格')
        ax2.set_ylabel('频次')
        
        # 涨跌幅分布
        if 'change_pct' in df.columns and not df['change_pct'].isna().all():
            colors = ['red' if x < 0 else 'green' for x in df['change_pct']]
            ax3.bar(df['symbol'], df['change_pct'], color=colors, alpha=0.7)
            ax3.set_title('涨跌幅 (%)', fontsize=14)
            ax3.set_ylabel('涨跌幅 (%)')
            ax3.tick_params(axis='x', rotation=45)
        else:
            ax3.text(0.5, 0.5, '无涨跌幅数据', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('涨跌幅数据不可用', fontsize=14)
        
        # 价格vs成交量散点图
        ax4.scatter(df['volume'], df['close'], alpha=0.7, s=100)
        ax4.set_title('价格 vs 成交量', fontsize=14)
        ax4.set_xlabel('成交量')
        ax4.set_ylabel('价格')
        
        plt.tight_layout()
        
        # 保存图表
        output_path = f"market_overview_{limit}symbols.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"📊 市场概览图表已保存: {output_path}")
        
        plt.show()
    
    def export_data(self, table_name: str, symbol: str = None, output_format: str = 'csv'):
        """导出数据到文件"""
        if symbol:
            query = f"SELECT * FROM {table_name} WHERE symbol = ? ORDER BY datetime"
            params = [symbol]
            filename = f"{table_name}_{symbol}.{output_format}"
        else:
            query = f"SELECT * FROM {table_name} ORDER BY datetime"
            params = []
            filename = f"{table_name}_all.{output_format}"
        
        df = pd.read_sql_query(query, self.conn, params=params)
        
        if df.empty:
            print("❌ 未找到数据")
            return
        
        if output_format.lower() == 'csv':
            df.to_csv(filename, index=False)
        elif output_format.lower() == 'json':
            df.to_json(filename, orient='records', date_format='iso')
        elif output_format.lower() == 'excel':
            df.to_excel(filename, index=False)
        
        print(f"📁 数据已导出: {filename} ({len(df)} 条记录)")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库可视化工具')
    parser.add_argument('db_path', help='数据库文件路径')
    parser.add_argument('--list', action='store_true', help='列出所有表')
    parser.add_argument('--describe', help='描述指定表')
    parser.add_argument('--plot-symbol', help='绘制指定交易对的价格图表')
    parser.add_argument('--table', default='klines', help='表名 (默认: klines)')
    parser.add_argument('--days', type=int, default=30, help='显示天数 (默认: 30)')
    parser.add_argument('--market-overview', action='store_true', help='显示市场概览')
    parser.add_argument('--export', help='导出数据 (指定交易对或all)')
    parser.add_argument('--format', default='csv', choices=['csv', 'json', 'excel'], help='导出格式')
    
    args = parser.parse_args()
    
    try:
        visualizer = DatabaseVisualizer(args.db_path)
        
        if args.list:
            visualizer.list_tables()
        elif args.describe:
            visualizer.describe_table(args.describe)
        elif args.plot_symbol:
            visualizer.plot_symbol_price(args.table, args.plot_symbol, args.days)
        elif args.market_overview:
            visualizer.plot_market_overview(args.table)
        elif args.export:
            symbol = None if args.export.lower() == 'all' else args.export
            visualizer.export_data(args.table, symbol, args.format)
        else:
            # 交互式模式
            tables = visualizer.list_tables()
            if tables:
                print(f"\n💡 使用示例:")
                print(f"  python {__file__} {args.db_path} --describe {tables[0]}")
                print(f"  python {__file__} {args.db_path} --market-overview")
                if 'klines' in tables:
                    print(f"  python {__file__} {args.db_path} --plot-symbol BTCUSDT")
    
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
