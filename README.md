# 🚀 TBTrade - 动态仓位管理交易系统

一个专业级的加密货币动态交易策略系统，具备世界级时间序列交易模拟引擎和智能仓位管理功能。

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)](README.md)

## 🎯 核心特性

### 🔥 世界级时间序列交易模拟引擎
- **真实时间顺序处理**：按4小时K线逐个时间点处理，完全模拟真实交易环境
- **实时决策机制**：每个时间点只能基于当前和历史数据，无法"看到未来"
- **多币种并行处理**：同时处理多个USDT交易对（支持132个币种）
- **完整历史数据验证**：3年完整数据验证（2022-2025年）

### 💰 智能动态仓位管理
```
核心公式：
- 总仓位 = 可用现金 + 所有持仓浮盈浮亏总和
- 交易额度 = 总仓位 × 0.2倍杠杆
- 最低保护 = 2000元（除非总仓位 < 2000元）
- 最大杠杆 = 5倍（资金不足时自动计算）
```

### 🎮 统一交易逻辑管理
- **集中化管理**：所有入场和退出逻辑统一管理，避免分散和混淆
- **配置化控制**：分批止盈等功能可通过参数开关控制
- **优先级排序**：固定止损 > 分批止盈 > 技术退出

### 📊 专业级输出和分析系统
- **分层级信息展示**：结构化的交易信息组织
- **智能输出控制**：3种详细程度模式（简洁/标准/详细）
- **完整日志保存**：所有终端输出永久保存到文件
- **专业图表生成**：时间序列图表和收益率分析

## 🏗️ 系统架构

### 核心模块
```
src/
├── strategies/
│   ├── ema_breakout_strategy.py      # EMA策略核心
│   ├── dynamic_position_manager.py   # 动态仓位管理器
│   └── ema_dynamic_strategy.py       # 统一逻辑管理
├── utils/
│   └── output_formatter.py           # 专业输出系统
└── data_layer/
    └── historical_data_fetcher.py    # 数据处理层
```

### 主要脚本
- `run_dynamic_backtest.py` - 动态回测主脚本
- `backtest_visualization.py` - 可视化分析工具

## 🚀 快速开始

### 环境要求
```bash
Python 3.8+
pandas >= 1.3.0
numpy >= 1.21.0
matplotlib >= 3.5.0
sqlite3 (内置)
```

### 安装依赖
```bash
pip install pandas numpy matplotlib
```

### 基础使用
```bash
# 基础回测
python run_dynamic_backtest.py --capital 10000 --symbols BTCUSDT

# 多币种回测
python run_dynamic_backtest.py --capital 10000 --symbols BTCUSDT ETHUSDT ADAUSDT

# 启用分批止盈
python run_dynamic_backtest.py --capital 10000 --symbols BTCUSDT --enable-partial-profit

# 简洁模式（快速查看）
python run_dynamic_backtest.py --capital 10000 --symbols BTCUSDT --verbose 0

# 详细模式（完整信息）
python run_dynamic_backtest.py --capital 10000 --symbols BTCUSDT --verbose 2
```

## 📈 交易策略

### EMA突破策略
- **入场条件**：EMA21 > EMA200 且 EMA21 > EMA55
- **冷却期机制**：30天信号冷却期，避免频繁交易
- **资金分配**：
  - 单个机会：使用全部可用资金
  - 多个机会：平均分配资金

### 风险管理
- **固定止损**：亏损超过15%强制平仓（最高优先级）
- **分批止盈**：盈利超过50%时减仓50%（可配置开关）
- **技术退出**：EMA21斜率连续3周期为负
- **杠杆控制**：最大5倍杠杆，智能计算

## 📊 输出系统

### 文件输出（每次回测生成5个文件）
```
backtest_results/dynamic_backtest_YYYYMMDD_HHMMSS/
├── full_backtest_log.txt           # 完整运行日志
├── detailed_summary.txt            # 详细分析报告
├── time_series_chart.png          # 时间序列图表
├── dynamic_backtest_report.txt    # 标准回测报告
└── dynamic_backtest_results.json  # 原始数据
```

### 终端输出示例
```
🎯 [01-29 08:00] 交易机会分析
├─ 发现机会: 1个 (BTCUSDT)
├─ 可用资金: 10,000元 (总仓位: 10,000元)
├─ 分配策略: 单机会全额分配
└─ 执行结果: 等待开仓...

📈 [BTCUSDT] 开仓详情
├─ 价格: $23,193
├─ 金额: 2,000元 (杠杆: 1.0x)
└─ 原因: EMA21突破EMA200买入

💼 当前组合状态
├─ 持仓数: 1个
├─ 总权益: 8,000元
├─ 可用资金: 8,000元
└─ 资金利用率: 25.0%
```

## 📊 回测结果示例

### 132币种完整测试
- **总交易次数**：319次
- **胜率**：39.8%
- **总收益率**：-23.70%
- **最终权益**：7,630元（初始10,000元）
- **最佳单笔**：LINKUSDT +270.3%

### 单币种测试（BTCUSDT）
- **总交易次数**：7次
- **胜率**：28.6%
- **总收益率**：-1.94%
- **最终权益**：9,806元

## 🔧 高级配置

### 命令行参数
```bash
--capital AMOUNT          # 初始资金（默认：10000）
--symbols SYMBOL [...]     # 交易币种列表
--start-date YYYY-MM-DD    # 开始日期
--end-date YYYY-MM-DD      # 结束日期
--enable-partial-profit    # 启用分批止盈
--verbose {0,1,2}          # 输出详细程度
--output-dir PATH          # 自定义输出目录
```

### 策略参数配置
```python
strategy_params = {
    'ema_short': 21,              # 短期EMA周期
    'ema_medium': 55,             # 中期EMA周期  
    'ema_long': 200,              # 长期EMA周期
    'stop_loss_pct': 0.15,        # 止损百分比
    'partial_profit_pct': 0.50,   # 分批止盈百分比
    'signal_cooldown_days': 30,   # 信号冷却期天数
    'min_ema200_periods': 200     # EMA200最少数据点
}
```

## 🎨 可视化功能

### 时间序列图表
- **上图**：累计收益率时间序列曲线
  - 蓝色实线显示累计收益变化
  - 红色虚线显示盈亏平衡线
- **下图**：单笔交易收益率柱状图
  - 绿色表示盈利交易
  - 红色表示亏损交易

### ASCII收益率图表
```
📊 收益率走势图:
    1: ▓▓▓▓                                               -6.5%
    2: ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓                                  -1.1%
    3: ███████████████████████████                        +2.9%
    4: ██████████████████████████████████████████████████ +11.9%
```

## 🔍 技术特性

### 数据处理
- **4小时K线数据**：支持多种时间周期
- **EMA指标计算**：基于日线数据计算EMA21/55/200
- **信号生成**：实时生成买入/卖出信号
- **数据验证**：完整的数据质量检查

### 性能优化
- **索引对齐**：高效的数据索引管理
- **内存优化**：大数据集的内存管理
- **并行处理**：多币种并行计算
- **缓存机制**：指标计算结果缓存

### 错误处理
- **异常安全**：完整的异常处理机制
- **数据验证**：输入数据的完整性检查
- **日志记录**：详细的错误日志和调试信息
- **恢复机制**：异常情况下的系统恢复

## 📚 使用案例

### 策略研究
```python
from src.strategies.ema_dynamic_strategy import EMADynamicStrategy

# 创建策略实例
strategy = EMADynamicStrategy(
    initial_capital=10000,
    enable_partial_profit=True,
    verbose_level=1
)

# 运行回测
results = strategy.simulate_real_time_trading(symbol_data)
```

### 批量测试
```bash
# 测试多个币种
python run_dynamic_backtest.py --capital 50000 --symbols BTCUSDT ETHUSDT ADAUSDT LINKUSDT DOTUSDT

# 测试不同时间段
python run_dynamic_backtest.py --capital 10000 --symbols BTCUSDT --start-date 2023-01-01 --end-date 2024-01-01
```

## 🛠️ 开发指南

### 项目结构
```
TBTrade/
├── src/                    # 源代码
│   ├── strategies/         # 交易策略
│   ├── utils/             # 工具模块
│   └── data_layer/        # 数据层
├── data/                  # 数据文件
├── backtest_results/      # 回测结果
├── tests/                 # 测试文件
└── docs/                  # 文档
```

### 扩展策略
1. 继承`EMABreakoutStrategy`基类
2. 实现自定义的信号生成逻辑
3. 配置策略参数
4. 集成到动态策略系统

### 自定义指标
1. 在`calculate_indicators`方法中添加新指标
2. 更新信号生成逻辑
3. 配置指标参数
4. 测试和验证

## 🤝 贡献指南

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

### 代码规范
- 遵循PEP8编码规范
- 添加详细的注释和文档
- 编写单元测试
- 确保代码质量

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 提交Pull Request
- 发送邮件

---

**⚠️ 风险提示**：本系统仅供学习和研究使用。加密货币交易存在高风险，请谨慎投资，自负盈亏。
