#!/usr/bin/env python3
"""
测试异步回测功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.async_backtest import async_backtest_manager, TaskStatus

def test_async_backtest():
    """测试异步回测功能"""
    print("🧪 测试异步回测功能...")
    
    # 测试配置
    test_config = {
        'symbols': ['BTCUSDT', 'ETHUSDT'],
        'initial_capital': 10000,
        'start_date': '2024-01-01',
        'end_date': '2024-01-31',
        'strategy_params': {
            'ema_short': 21,
            'ema_medium': 55,
            'ema_long': 200,
            'stop_loss_pct': 0.15,
            'take_profit_pct': 0.50
        },
        'enable_partial_profit': True,
        'verbose_level': 0
    }
    
    print("📝 提交测试任务...")
    task_id = async_backtest_manager.submit_backtest(test_config)
    print(f"✅ 任务已提交，ID: {task_id}")
    
    # 监控任务进度
    print("\n📊 监控任务进度...")
    max_wait_time = 60  # 最大等待60秒
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        task_status = async_backtest_manager.get_task_status(task_id)
        
        if not task_status:
            print("❌ 任务不存在")
            break
        
        status = task_status['status']
        progress = task_status['progress']
        message = task_status['message']
        
        print(f"状态: {status} | 进度: {progress:.1f}% | 消息: {message}")
        
        if status in ['completed', 'failed', 'cancelled']:
            break
        
        time.sleep(2)  # 每2秒检查一次
    
    # 获取最终结果
    final_status = async_backtest_manager.get_task_status(task_id)
    if final_status:
        print(f"\n🎯 最终状态: {final_status['status']}")
        
        if final_status['status'] == 'completed':
            result = final_status['result']
            if result and result.get('success'):
                print("✅ 回测成功完成！")
                print(f"结果类型: {type(result.get('results'))}")
            else:
                print(f"❌ 回测失败: {result.get('error') if result else '无结果'}")
        
        elif final_status['status'] == 'failed':
            print(f"❌ 任务执行失败: {final_status.get('error')}")
    
    return task_id

def test_task_management():
    """测试任务管理功能"""
    print("\n🧪 测试任务管理功能...")
    
    # 获取所有任务
    all_tasks = async_backtest_manager.get_all_tasks()
    print(f"📋 当前任务数: {len(all_tasks)}")
    
    for task_id, task_info in all_tasks.items():
        print(f"任务 {task_id[:8]}...: {task_info['status']} ({task_info['progress']:.1f}%)")
    
    # 测试取消功能（如果有运行中的任务）
    running_tasks = [tid for tid, info in all_tasks.items() if info['status'] == 'running']
    if running_tasks:
        test_task_id = running_tasks[0]
        print(f"\n🛑 测试取消任务: {test_task_id[:8]}...")
        
        if async_backtest_manager.cancel_task(test_task_id):
            print("✅ 任务取消成功")
            
            # 检查状态
            time.sleep(1)
            updated_status = async_backtest_manager.get_task_status(test_task_id)
            if updated_status:
                print(f"更新后状态: {updated_status['status']}")
        else:
            print("❌ 任务取消失败")

def test_concurrent_tasks():
    """测试并发任务"""
    print("\n🧪 测试并发任务...")
    
    # 提交多个任务
    task_ids = []
    for i in range(3):
        config = {
            'symbols': ['BTCUSDT'],
            'initial_capital': 10000,
            'start_date': '2024-01-01',
            'end_date': '2024-01-10',  # 较短的时间范围
            'strategy_params': {'ema_short': 21, 'ema_medium': 55, 'ema_long': 200},
            'verbose_level': 0
        }
        
        task_id = async_backtest_manager.submit_backtest(config)
        task_ids.append(task_id)
        print(f"提交任务 {i+1}: {task_id[:8]}...")
    
    # 检查任务状态
    time.sleep(2)
    print("\n📊 并发任务状态:")
    for task_id in task_ids:
        status = async_backtest_manager.get_task_status(task_id)
        if status:
            print(f"任务 {task_id[:8]}...: {status['status']}")
    
    return task_ids

if __name__ == "__main__":
    print("🧪 开始测试异步回测系统...")
    print("=" * 50)
    
    try:
        # 测试1: 基本异步回测
        task_id = test_async_backtest()
        
        # 测试2: 任务管理
        test_task_management()
        
        # 测试3: 并发任务（可选）
        # concurrent_tasks = test_concurrent_tasks()
        
        print("\n" + "=" * 50)
        print("✅ 异步回测系统测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
