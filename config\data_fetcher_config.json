{"comment": "配置文件用于 auto_4h_monitor.py 调用 historical_data_fetcher.py 的功能", "time_range": {"mode": "days", "days": 365, "time_label": "1year", "comment": "时间范围配置 - 默认获取1年(365天)的历史数据"}, "symbol_selection": {"mode": "top_n", "top_n": 100, "min_volume": 0, "comment": "币种选择 - 获取交易量前100个USDT交易对，无最小交易量限制"}, "kline_settings": {"interval": "4h", "comment": "K线间隔设置 - 4小时K线"}, "fetcher_settings": {"use_proxy": true, "proxy_port": 6754, "batch_delay": 0.5, "output_dir": "./data", "comment": "获取器设置 - 使用代理端口6754，批次延迟0.5秒"}, "auto_mode_settings": {"enable_gap_analysis": true, "enable_incremental_update": true, "max_symbols_per_batch": 10, "comment": "自动模式设置 - 启用增量更新和缺口分析"}, "presets": {"real_time_update": {"time_range": {"mode": "days", "days": 1, "time_label": "1day"}, "symbol_selection": {"mode": "top_n", "top_n": 100, "min_volume": 0}, "fetcher_settings": {"use_proxy": true, "proxy_port": 6754}, "comment": "实时更新预设 - 获取最近1天数据，前100个交易对，使用代理"}, "historical_validation": {"time_range": {"mode": "days", "days": 365, "time_label": "1year"}, "symbol_selection": {"mode": "top_n", "top_n": 100, "min_volume": 0}, "fetcher_settings": {"use_proxy": true, "proxy_port": 6754}, "comment": "历史验证预设 - 获取1年历史数据，前100个交易对，使用代理"}, "custom_range_template": {"time_range": {"mode": "custom_range", "start_date": "2024-01-01", "end_date": "2024-01-31", "time_label": "custom_range"}, "symbol_selection": {"mode": "specific", "symbols": "BTC,ETH,BNB,XRP,SOL"}, "comment": "自定义范围模板 - 指定日期范围和特定币种"}}}