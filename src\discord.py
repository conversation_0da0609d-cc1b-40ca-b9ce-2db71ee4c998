import requests
import json
from src.config_loader import load_config
config_data = load_config('./config.json')
channel_ids = []
channel_ids_beta = []

TOKEN = config_data["discord_bot_token"]
chn_id1 = config_data["discord_chn_id_private_1"] 
chn_id3 = config_data["discord_chn_id_private_2"]

chn_id2 = config_data["discord_chn_id_public_1"]

proxies = config_data["proxies"]

channel_ids.append(chn_id1)
channel_ids.append(chn_id2)

# channel_ids_beta.append(chn_id1)
channel_ids_beta.append(chn_id3)

def send_message(token, channel_id, message):
    try:
        url = f'https://discord.com/api/v10/channels/{channel_id}/messages'
        headers = {
            'Authorization': f'Bot {token}',
            'Content-Type': 'application/json'
        }
        payload = {
            'content': message
        }

        response = requests.post(url, headers=headers, data=json.dumps(payload), proxies=proxies, timeout=5)
        # print(response.json())
        if response.status_code == 200:
            print('Message sent successfully.')
        else:
            print('Failed to send message. Status code:', response.status_code)
    except Exception as e:
        print("discord channel send mes failed:", e)

def func_send_message_to_discord_beta(message):
    for id in channel_ids_beta:
        send_message(TOKEN, id, message)


def func_send_message_to_discord(message):
    for id in channel_ids:
        send_message(TOKEN, id, message)


if __name__ == "__main__":
    message_content = 'Hello Discord!'
    # send_message(TOKEN, CHANNEL_ID, message_content)
    # func_send_message_to_discord(message_content)
    func_send_message_to_discord_beta(message_content)