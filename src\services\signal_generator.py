"""
策略信号生成器
Strategy Signal Generator

负责基于实时数据生成交易信号，支持多策略融合和风险管理
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from dataclasses import dataclass
from enum import Enum

from ..strategies.base import BaseStrategy, SignalType
from ..strategies import get_strategy

@dataclass
class SignalRecord:
    """信号记录"""
    symbol: str
    strategy: str
    signal: SignalType
    confidence: float
    reason: str
    price: float
    timestamp: datetime
    indicators: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'symbol': self.symbol,
            'strategy': self.strategy,
            'signal': self.signal.name,
            'signal_value': self.signal.value,
            'confidence': self.confidence,
            'reason': self.reason,
            'price': self.price,
            'timestamp': self.timestamp.isoformat(),
            'indicators': self.indicators or {}
        }

class RiskLevel(Enum):
    """风险等级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    EXTREME = 4

class PositionManager:
    """仓位管理器"""
    
    def __init__(self, initial_capital: float = 10000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}  # {symbol: {'quantity': float, 'entry_price': float, 'entry_time': datetime}}
        self.max_position_pct = 0.1  # 单个交易对最大仓位比例
        self.max_total_position_pct = 0.8  # 总仓位比例
        self.stop_loss_pct = 0.05  # 止损比例
        self.take_profit_pct = 0.15  # 止盈比例
    
    def can_open_position(self, symbol: str, price: float, quantity_pct: float = 0.1) -> Tuple[bool, str]:
        """检查是否可以开仓"""
        # 检查是否已有仓位
        if symbol in self.positions:
            return False, f"已持有 {symbol} 仓位"
        
        # 检查单个仓位限制
        position_value = self.current_capital * quantity_pct
        max_position_value = self.initial_capital * self.max_position_pct
        if position_value > max_position_value:
            return False, f"超出单个仓位限制 ({self.max_position_pct*100}%)"
        
        # 检查总仓位限制
        total_position_value = sum(
            pos['quantity'] * price for pos in self.positions.values()
        )
        if (total_position_value + position_value) > self.initial_capital * self.max_total_position_pct:
            return False, f"超出总仓位限制 ({self.max_total_position_pct*100}%)"
        
        # 检查资金充足
        if position_value > self.current_capital:
            return False, "资金不足"
        
        return True, "可以开仓"
    
    def open_position(self, symbol: str, price: float, quantity_pct: float = 0.1) -> bool:
        """开仓"""
        can_open, reason = self.can_open_position(symbol, price, quantity_pct)
        if not can_open:
            return False
        
        position_value = self.current_capital * quantity_pct
        quantity = position_value / price
        
        self.positions[symbol] = {
            'quantity': quantity,
            'entry_price': price,
            'entry_time': datetime.now(),
            'stop_loss': price * (1 - self.stop_loss_pct),
            'take_profit': price * (1 + self.take_profit_pct)
        }
        
        self.current_capital -= position_value
        return True
    
    def close_position(self, symbol: str, price: float) -> Optional[Dict[str, Any]]:
        """平仓"""
        if symbol not in self.positions:
            return None
        
        position = self.positions[symbol]
        proceeds = position['quantity'] * price
        pnl = proceeds - (position['quantity'] * position['entry_price'])
        pnl_pct = pnl / (position['quantity'] * position['entry_price']) * 100
        
        self.current_capital += proceeds
        del self.positions[symbol]
        
        return {
            'symbol': symbol,
            'quantity': position['quantity'],
            'entry_price': position['entry_price'],
            'exit_price': price,
            'pnl': pnl,
            'pnl_pct': pnl_pct,
            'hold_time': datetime.now() - position['entry_time']
        }
    
    def check_stop_loss_take_profit(self, symbol: str, current_price: float) -> Optional[str]:
        """检查止损止盈"""
        if symbol not in self.positions:
            return None
        
        position = self.positions[symbol]
        
        if current_price <= position['stop_loss']:
            return "stop_loss"
        elif current_price >= position['take_profit']:
            return "take_profit"
        
        return None
    
    def get_portfolio_status(self) -> Dict[str, Any]:
        """获取投资组合状态"""
        total_value = self.current_capital
        for symbol, position in self.positions.items():
            # 这里需要当前价格，暂时用入场价格估算
            total_value += position['quantity'] * position['entry_price']
        
        return {
            'total_value': total_value,
            'cash': self.current_capital,
            'positions_count': len(self.positions),
            'positions': self.positions.copy(),
            'total_return_pct': (total_value - self.initial_capital) / self.initial_capital * 100
        }

class StrategySignalGenerator:
    """策略信号生成器"""
    
    def __init__(self, initial_capital: float = 10000):
        """
        初始化信号生成器
        
        Args:
            initial_capital: 初始资金
        """
        self.strategies = {}  # {symbol: [strategies]}
        self.signal_history = []  # 信号历史记录
        self.position_manager = PositionManager(initial_capital)
        
        # 信号融合配置
        self.strategy_weights = {
            'CRSIStrategy': 0.3,
            'EMAStrategy': 0.25,
            'RSIStrategy': 0.2,
            'BollingerStrategy': 0.25
        }
        
        # 风险管理配置
        self.min_confidence = 60  # 最小置信度
        self.signal_cooldown = timedelta(hours=4)  # 信号冷却时间
        self.max_signals_per_day = 5  # 每日最大信号数
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
    
    def add_strategy(self, symbol: str, strategy_name: str, params: Dict[str, Any] = None):
        """添加策略"""
        try:
            strategy = get_strategy(strategy_name, params=params)
            
            if symbol not in self.strategies:
                self.strategies[symbol] = []
            
            self.strategies[symbol].append(strategy)
            self.logger.info(f"🎯 为 {symbol} 添加策略: {strategy_name}")
            
        except Exception as e:
            self.logger.error(f"❌ 添加策略失败: {e}")
    
    def remove_strategy(self, symbol: str, strategy_name: str):
        """移除策略"""
        if symbol in self.strategies:
            self.strategies[symbol] = [
                s for s in self.strategies[symbol] 
                if s.name != strategy_name
            ]
            self.logger.info(f"🗑️ 移除 {symbol} 的策略: {strategy_name}")
    
    def generate_signals(self, symbol: str, data: pd.DataFrame) -> List[SignalRecord]:
        """生成策略信号"""
        if symbol not in self.strategies or not self.strategies[symbol]:
            return []
        
        signals = []
        
        try:
            for strategy in self.strategies[symbol]:
                try:
                    # 加载数据到策略
                    strategy.load_data(data)
                    
                    # 获取信号
                    signal_info = strategy.get_signal()
                    
                    if signal_info and signal_info['signal'] != SignalType.NEUTRAL:
                        # 获取当前指标
                        indicators = strategy.get_current_indicators(data)
                        
                        signal_record = SignalRecord(
                            symbol=symbol,
                            strategy=strategy.name,
                            signal=signal_info['signal'],
                            confidence=signal_info['confidence'],
                            reason=signal_info['reason'],
                            price=data.iloc[-1]['close'],
                            timestamp=datetime.now(),
                            indicators=indicators
                        )
                        
                        signals.append(signal_record)
                        
                except Exception as e:
                    self.logger.error(f"❌ {symbol} {strategy.name} 信号生成失败: {e}")
        
        except Exception as e:
            self.logger.error(f"❌ {symbol} 信号生成失败: {e}")
        
        return signals
    
    def fuse_signals(self, signals: List[SignalRecord]) -> Optional[SignalRecord]:
        """融合多个策略信号"""
        if not signals:
            return None
        
        # 按信号类型分组
        buy_signals = [s for s in signals if s.signal in [SignalType.BUY, SignalType.STRONG_BUY]]
        sell_signals = [s for s in signals if s.signal in [SignalType.SELL, SignalType.STRONG_SELL]]
        
        # 计算加权置信度
        def calculate_weighted_confidence(signal_list: List[SignalRecord]) -> float:
            if not signal_list:
                return 0
            
            total_weight = 0
            weighted_confidence = 0
            
            for signal in signal_list:
                weight = self.strategy_weights.get(signal.strategy, 0.25)
                strength_multiplier = 1.5 if 'STRONG' in signal.signal.name else 1.0
                
                total_weight += weight * strength_multiplier
                weighted_confidence += signal.confidence * weight * strength_multiplier
            
            return weighted_confidence / total_weight if total_weight > 0 else 0
        
        buy_confidence = calculate_weighted_confidence(buy_signals)
        sell_confidence = calculate_weighted_confidence(sell_signals)
        
        # 确定最终信号
        if buy_confidence > sell_confidence and buy_confidence >= self.min_confidence:
            # 选择置信度最高的买入信号作为基础
            base_signal = max(buy_signals, key=lambda s: s.confidence)
            final_signal = SignalType.STRONG_BUY if buy_confidence >= 80 else SignalType.BUY
            
            return SignalRecord(
                symbol=base_signal.symbol,
                strategy="FUSED",
                signal=final_signal,
                confidence=buy_confidence,
                reason=f"融合信号: {len(buy_signals)}个买入策略",
                price=base_signal.price,
                timestamp=datetime.now(),
                indicators={'buy_signals': len(buy_signals), 'sell_signals': len(sell_signals)}
            )
        
        elif sell_confidence > buy_confidence and sell_confidence >= self.min_confidence:
            # 选择置信度最高的卖出信号作为基础
            base_signal = max(sell_signals, key=lambda s: s.confidence)
            final_signal = SignalType.STRONG_SELL if sell_confidence >= 80 else SignalType.SELL
            
            return SignalRecord(
                symbol=base_signal.symbol,
                strategy="FUSED",
                signal=final_signal,
                confidence=sell_confidence,
                reason=f"融合信号: {len(sell_signals)}个卖出策略",
                price=base_signal.price,
                timestamp=datetime.now(),
                indicators={'buy_signals': len(buy_signals), 'sell_signals': len(sell_signals)}
            )
        
        return None
    
    def apply_risk_filters(self, signal: SignalRecord) -> Tuple[bool, str]:
        """应用风险过滤器"""
        # 检查信号冷却时间
        recent_signals = [
            s for s in self.signal_history 
            if s.symbol == signal.symbol and 
               datetime.now() - s.timestamp < self.signal_cooldown
        ]
        
        if recent_signals:
            return False, f"信号冷却中，距离上次信号 {datetime.now() - recent_signals[-1].timestamp}"
        
        # 检查每日信号数量限制
        today_signals = [
            s for s in self.signal_history 
            if s.timestamp.date() == datetime.now().date()
        ]
        
        if len(today_signals) >= self.max_signals_per_day:
            return False, f"今日信号数量已达上限 ({self.max_signals_per_day})"
        
        # 检查仓位管理
        if signal.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            can_open, reason = self.position_manager.can_open_position(
                signal.symbol, signal.price
            )
            if not can_open:
                return False, f"仓位管理限制: {reason}"
        
        elif signal.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
            if signal.symbol not in self.position_manager.positions:
                return False, "没有持仓，无法卖出"
        
        return True, "通过风险检查"
    
    def process_signal(self, symbol: str, data: pd.DataFrame) -> Optional[SignalRecord]:
        """处理信号生成的完整流程"""
        try:
            # 1. 生成原始信号
            raw_signals = self.generate_signals(symbol, data)
            
            if not raw_signals:
                return None
            
            # 2. 融合信号
            fused_signal = self.fuse_signals(raw_signals)
            
            if not fused_signal:
                return None
            
            # 3. 应用风险过滤
            passed_risk, risk_reason = self.apply_risk_filters(fused_signal)
            
            if not passed_risk:
                self.logger.info(f"⚠️ {symbol} 信号被风险过滤器拒绝: {risk_reason}")
                return None
            
            # 4. 记录信号
            self.signal_history.append(fused_signal)
            
            # 5. 更新仓位（模拟）
            self._update_position(fused_signal)
            
            self.logger.info(f"✅ {symbol} 生成最终信号: {fused_signal.signal.name} (置信度: {fused_signal.confidence:.1f})")
            
            return fused_signal
            
        except Exception as e:
            self.logger.error(f"❌ {symbol} 信号处理失败: {e}")
            return None
    
    def _update_position(self, signal: SignalRecord):
        """更新仓位（模拟交易）"""
        try:
            if signal.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                # 开多仓
                quantity_pct = 0.15 if signal.signal == SignalType.STRONG_BUY else 0.1
                success = self.position_manager.open_position(
                    signal.symbol, signal.price, quantity_pct
                )
                if success:
                    self.logger.info(f"📈 {signal.symbol} 开多仓: {signal.price:.4f}")
                
            elif signal.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                # 平仓
                trade_result = self.position_manager.close_position(
                    signal.symbol, signal.price
                )
                if trade_result:
                    self.logger.info(f"📉 {signal.symbol} 平仓: {trade_result['pnl']:.2f} ({trade_result['pnl_pct']:.2f}%)")
        
        except Exception as e:
            self.logger.error(f"❌ 更新仓位失败: {e}")
    
    def check_stop_loss_take_profit(self, symbol: str, current_price: float) -> Optional[SignalRecord]:
        """检查止损止盈"""
        action = self.position_manager.check_stop_loss_take_profit(symbol, current_price)
        
        if action:
            signal_type = SignalType.SELL
            reason = f"触发{action}"
            confidence = 100  # 止损止盈信号置信度为100
            
            signal = SignalRecord(
                symbol=symbol,
                strategy="RISK_MANAGEMENT",
                signal=signal_type,
                confidence=confidence,
                reason=reason,
                price=current_price,
                timestamp=datetime.now()
            )
            
            # 执行平仓
            self._update_position(signal)
            self.signal_history.append(signal)
            
            return signal
        
        return None
    
    def get_signal_history(self, symbol: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """获取信号历史"""
        history = self.signal_history
        
        if symbol:
            history = [s for s in history if s.symbol == symbol]
        
        return [s.to_dict() for s in history[-limit:]]
    
    def get_portfolio_status(self) -> Dict[str, Any]:
        """获取投资组合状态"""
        return self.position_manager.get_portfolio_status()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_signals = len(self.signal_history)
        buy_signals = len([s for s in self.signal_history if s.signal in [SignalType.BUY, SignalType.STRONG_BUY]])
        sell_signals = len([s for s in self.signal_history if s.signal in [SignalType.SELL, SignalType.STRONG_SELL]])
        
        # 按策略统计
        strategy_stats = {}
        for signal in self.signal_history:
            if signal.strategy not in strategy_stats:
                strategy_stats[signal.strategy] = {'count': 0, 'avg_confidence': 0}
            strategy_stats[signal.strategy]['count'] += 1
        
        # 计算平均置信度
        for strategy in strategy_stats:
            signals = [s for s in self.signal_history if s.strategy == strategy]
            strategy_stats[strategy]['avg_confidence'] = np.mean([s.confidence for s in signals])
        
        return {
            'total_signals': total_signals,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'strategy_stats': strategy_stats,
            'portfolio': self.get_portfolio_status()
        }

# 使用示例
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建信号生成器
    generator = StrategySignalGenerator(initial_capital=10000)
    
    # 添加策略
    generator.add_strategy("BTCUSDT", "CRSI")
    generator.add_strategy("BTCUSDT", "EMA")
    generator.add_strategy("BTCUSDT", "RSI")
    
    # 模拟数据处理
    print("🚀 策略信号生成器已初始化")
    print(f"📊 统计信息: {generator.get_statistics()}") 