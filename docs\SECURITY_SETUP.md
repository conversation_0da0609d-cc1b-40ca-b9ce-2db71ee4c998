# 🔒 安全配置指南

## ⚠️ 重要安全提醒

您的项目中**发现了严重的安全风险**！请立即按照以下步骤进行安全配置。

### 🚨 发现的安全问题

1. **Discord Bot Token 明文暴露**：`config/config.json` 中的 Discord token 直接可见
2. **API 密钥管理不当**：缺乏环境变量保护机制
3. **敏感配置信息直接存储**：配置文件被纳入版本控制

---

## 🛠️ 安全修复步骤

### 第一步：立即删除敏感信息

1. **备份现有的 Discord token 和其他密钥**（在安全的地方记录）
2. **删除或重置暴露的 Discord bot token**
3. **从 Git 历史中移除敏感信息**

```bash
# 如果已经提交到 Git，需要清理历史记录
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch config/config.json' \
--prune-empty --tag-name-filter cat -- --all

# 强制推送清理后的历史
git push origin --force --all
```

### 第二步：设置环境变量

创建 `.env` 文件（已添加到 .gitignore 中）：

```bash
# 复制模板文件
cp .env.example .env
```

在 `.env` 文件中填入真实的配置信息：

```env
# Binance API 配置
BINANCE_API_KEY=你的_binance_api_key
BINANCE_API_SECRET=你的_binance_api_secret

# Discord 配置  
DISCORD_BOT_TOKEN=你的新_discord_bot_token
DISCORD_CHANNEL_ID_PRIVATE_1=你的频道ID
DISCORD_CHANNEL_ID_PRIVATE_2=你的频道ID
DISCORD_CHANNEL_ID_PUBLIC_1=你的频道ID

# 其他配置...
```

### 第三步：更新代码引用

现有代码已更新为使用新的安全配置系统：

- ✅ `src/config_manager.py` - 统一的配置管理
- ✅ `src/logger_config.py` - 标准化日志系统  
- ✅ `src/state_manager.py` - 状态管理（替代全局变量）
- ✅ `config/config_secure.json` - 安全的配置模板

### 第四步：验证配置

运行配置验证脚本：

```bash
python -c "from src.config_manager import config_manager; print(config_manager.get_state_summary())"
```

---

## 🔐 最佳安全实践

### 1. 环境变量管理

- **生产环境**：使用系统环境变量或密钥管理服务
- **开发环境**：使用 `.env` 文件（已加入 .gitignore）
- **绝不**将 `.env` 文件提交到版本控制

### 2. 密钥轮换

- **定期更换** API 密钥和 token
- **监控** API 使用情况，发现异常立即更换
- **使用只读权限**的 API 密钥（如果可能）

### 3. 访问控制

- **限制** Discord bot 权限范围
- **设置** API 密钥的 IP 白名单
- **启用** 二次验证 (2FA)

### 4. 监控和审计

- **记录** API 调用日志
- **监控** 异常登录和 API 使用
- **定期检查**配置文件的安全性

---

## 🔧 代码改进摘要

### 新增的安全特性

1. **ConfigManager 类**：统一管理所有配置，支持环境变量
2. **日志系统**：替代 print 语句，提供结构化日志
3. **状态管理**：线程安全的状态管理，替代全局变量
4. **完善的 .gitignore**：防止敏感文件被提交

### 向后兼容性

现有代码保持兼容，但建议逐步迁移到新的管理系统：

```python
# 旧方式（仍可用）
from src.config import API_KEY

# 新方式（推荐）
from src.config_manager import config_manager
api_key = config_manager.get('binance.api_key')
```

---

## ⚡ 快速行动清单

- [ ] 备份并删除暴露的敏感信息
- [ ] 创建 `.env` 文件并配置真实密钥
- [ ] 重新生成暴露的 Discord bot token
- [ ] 清理 Git 历史中的敏感信息
- [ ] 测试新的配置系统
- [ ] 通知团队成员安全更改

---

## 📞 如需帮助

如果在配置过程中遇到问题，请：

1. 查看日志文件：`logs/trade_api_alert.log`
2. 运行诊断命令检查配置状态
3. 确保所有依赖已正确安装：`uv pip install -e .`

**记住：安全无小事，立即行动！** 🚨 