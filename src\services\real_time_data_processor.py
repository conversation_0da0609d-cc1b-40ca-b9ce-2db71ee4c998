"""
实时数据流处理器
Real-time Data Stream Processor

负责实时获取和处理市场数据，支持WebSocket连接和数据流管理
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
import pandas as pd
import numpy as np
import sqlite3
from pathlib import Path

try:
    from binance import ThreadedWebSocketManager
    from binance.client import Client
    BINANCE_AVAILABLE = True
except ImportError:
    BINANCE_AVAILABLE = False
    ThreadedWebSocketManager = None
    Client = None

from ..core.indicators import *
from ..strategies.base import BaseStrategy

class RealTimeDataProcessor:
    """实时数据流处理器"""
    
    def __init__(self, api_key: str = None, api_secret: str = None, 
                 db_path: str = "./data/market_data.db",
                 interval: str = "4h"):
        """
        初始化实时数据处理器
        
        Args:
            api_key: Binance API密钥
            api_secret: Binance API密钥
            db_path: 数据库路径
            interval: K线间隔 (1h, 4h, 1d等)
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.db_path = Path(db_path)
        self.interval = interval
        
        # 初始化客户端
        self.client = None
        self.twm = None
        
        # 数据缓存
        self.data_cache = {}  # {symbol: DataFrame}
        self.last_update = {}  # {symbol: timestamp}
        
        # 回调函数
        self.data_callbacks = []  # 数据更新回调
        self.signal_callbacks = []  # 信号生成回调
        
        # 监控的交易对
        self.symbols = []
        self.strategies = {}  # {symbol: [strategies]}
        
        # 状态管理
        self.is_running = False
        self.connection_status = {}
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
        # 创建数据库
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        self.db_path.parent.mkdir(exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS real_time_klines (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    open_time INTEGER NOT NULL,
                    close_time INTEGER NOT NULL,
                    open_price REAL NOT NULL,
                    high_price REAL NOT NULL,
                    low_price REAL NOT NULL,
                    close_price REAL NOT NULL,
                    volume REAL NOT NULL,
                    quote_volume REAL NOT NULL,
                    trades_count INTEGER,
                    interval TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, open_time, interval)
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_symbol_time 
                ON real_time_klines(symbol, open_time)
            """)
    
    def initialize_client(self, api_key: str = None, api_secret: str = None):
        """初始化Binance客户端"""
        if not BINANCE_AVAILABLE:
            self.logger.error("❌ python-binance未安装，无法使用Binance API")
            return
            
        if api_key:
            self.api_key = api_key
        if api_secret:
            self.api_secret = api_secret
            
        if self.api_key and self.api_secret:
            self.client = Client(self.api_key, self.api_secret)
            self.logger.info("✅ Binance客户端初始化成功")
        else:
            self.client = Client()  # 公共API
            self.logger.warning("⚠️ 使用公共API，功能受限")
    
    def add_symbol(self, symbol: str, strategies: List[BaseStrategy] = None):
        """添加监控的交易对"""
        if symbol not in self.symbols:
            self.symbols.append(symbol)
            self.strategies[symbol] = strategies or []
            self.connection_status[symbol] = False
            self.logger.info(f"📊 添加监控交易对: {symbol}")
    
    def add_strategy(self, symbol: str, strategy: BaseStrategy):
        """为交易对添加策略"""
        if symbol not in self.strategies:
            self.strategies[symbol] = []
        self.strategies[symbol].append(strategy)
        self.logger.info(f"🎯 为 {symbol} 添加策略: {strategy.name}")
    
    def add_data_callback(self, callback: Callable[[str, Dict], None]):
        """添加数据更新回调函数"""
        self.data_callbacks.append(callback)
    
    def add_signal_callback(self, callback: Callable[[str, Dict], None]):
        """添加信号生成回调函数"""
        self.signal_callbacks.append(callback)
    
    def load_historical_data(self, symbol: str, limit: int = 100) -> pd.DataFrame:
        """加载历史数据作为基础"""
        try:
            if not BINANCE_AVAILABLE:
                self.logger.warning("⚠️ Binance API不可用，返回空数据")
                return pd.DataFrame()
                
            if not self.client:
                self.initialize_client()
            
            if not self.client:
                self.logger.warning("⚠️ 无法初始化Binance客户端")
                return pd.DataFrame()
            
            # 获取历史K线数据
            klines = self.client.get_historical_klines(
                symbol, self.interval, f"{limit} {self.interval} ago UTC"
            )
            
            if not klines:
                self.logger.warning(f"⚠️ 无法获取 {symbol} 的历史数据")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(klines, columns=[
                'open_time', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades_count', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            # 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'quote_volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
            df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
            df.set_index('open_time', inplace=True)
            
            # 缓存数据
            self.data_cache[symbol] = df
            self.last_update[symbol] = datetime.now()
            
            self.logger.info(f"📈 加载 {symbol} 历史数据: {len(df)} 条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载 {symbol} 历史数据失败: {e}")
            return pd.DataFrame()
    
    def _handle_socket_message(self, msg):
        """处理WebSocket消息"""
        try:
            if msg['e'] == 'kline':
                kline_data = msg['k']
                symbol = kline_data['s']
                
                # 只处理监控的交易对
                if symbol not in self.symbols:
                    return
                
                # 只处理完整的K线（is_closed=True）
                if not kline_data['x']:
                    return
                
                # 解析K线数据
                kline = {
                    'open_time': pd.to_datetime(kline_data['t'], unit='ms'),
                    'close_time': pd.to_datetime(kline_data['T'], unit='ms'),
                    'open': float(kline_data['o']),
                    'high': float(kline_data['h']),
                    'low': float(kline_data['l']),
                    'close': float(kline_data['c']),
                    'volume': float(kline_data['v']),
                    'quote_volume': float(kline_data['q']),
                    'trades_count': int(kline_data['n'])
                }
                
                # 更新缓存
                self._update_cache(symbol, kline)
                
                # 保存到数据库
                self._save_kline_to_db(symbol, kline)
                
                # 触发数据回调
                for callback in self.data_callbacks:
                    try:
                        callback(symbol, kline)
                    except Exception as e:
                        self.logger.error(f"❌ 数据回调执行失败: {e}")
                
                # 生成策略信号
                self._generate_signals(symbol)
                
                self.logger.debug(f"📊 {symbol} 新K线: {kline['close']:.4f}")
                
        except Exception as e:
            self.logger.error(f"❌ 处理WebSocket消息失败: {e}")
    
    def _update_cache(self, symbol: str, kline: Dict):
        """更新数据缓存"""
        if symbol not in self.data_cache:
            self.data_cache[symbol] = pd.DataFrame()
        
        # 创建新行
        new_row = pd.DataFrame([kline])
        new_row.set_index('open_time', inplace=True)
        
        # 添加到缓存
        if self.data_cache[symbol].empty:
            self.data_cache[symbol] = new_row
        else:
            # 检查是否是更新现有K线还是新K线
            if kline['open_time'] in self.data_cache[symbol].index:
                # 更新现有K线
                self.data_cache[symbol].loc[kline['open_time']] = new_row.iloc[0]
            else:
                # 添加新K线
                self.data_cache[symbol] = pd.concat([self.data_cache[symbol], new_row])
                
                # 保持缓存大小（最多保留200条记录）
                if len(self.data_cache[symbol]) > 200:
                    self.data_cache[symbol] = self.data_cache[symbol].tail(200)
        
        self.last_update[symbol] = datetime.now()
    
    def _save_kline_to_db(self, symbol: str, kline: Dict):
        """保存K线数据到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO real_time_klines 
                    (symbol, open_time, close_time, open_price, high_price, 
                     low_price, close_price, volume, quote_volume, trades_count, interval)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    symbol,
                    int(kline['open_time'].timestamp() * 1000),
                    int(kline['close_time'].timestamp() * 1000),
                    kline['open'],
                    kline['high'],
                    kline['low'],
                    kline['close'],
                    kline['volume'],
                    kline['quote_volume'],
                    kline['trades_count'],
                    self.interval
                ))
        except Exception as e:
            self.logger.error(f"❌ 保存K线数据失败: {e}")
    
    def _generate_signals(self, symbol: str):
        """生成策略信号"""
        if symbol not in self.strategies or not self.strategies[symbol]:
            return
        
        if symbol not in self.data_cache or self.data_cache[symbol].empty:
            return
        
        try:
            data = self.data_cache[symbol].copy()
            
            for strategy in self.strategies[symbol]:
                try:
                    # 加载数据到策略
                    strategy.load_data(data)
                    
                    # 获取最新信号
                    signal = strategy.get_signal()
                    
                    if signal and signal['signal'].value != 0:  # 非中性信号
                        signal_data = {
                            'symbol': symbol,
                            'strategy': strategy.name,
                            'signal': signal['signal'].name,
                            'confidence': signal['confidence'],
                            'reason': signal['reason'],
                            'timestamp': datetime.now(),
                            'price': data.iloc[-1]['close']
                        }
                        
                        # 触发信号回调
                        for callback in self.signal_callbacks:
                            try:
                                callback(symbol, signal_data)
                            except Exception as e:
                                self.logger.error(f"❌ 信号回调执行失败: {e}")
                        
                        self.logger.info(f"🚨 {symbol} {strategy.name} 信号: {signal['signal'].name} (置信度: {signal['confidence']})")
                
                except Exception as e:
                    self.logger.error(f"❌ {symbol} {strategy.name} 信号生成失败: {e}")
        
        except Exception as e:
            self.logger.error(f"❌ {symbol} 信号生成失败: {e}")
    
    def start_monitoring(self):
        """开始实时监控"""
        if self.is_running:
            self.logger.warning("⚠️ 监控已在运行中")
            return
        
        if not self.symbols:
            self.logger.error("❌ 没有配置监控的交易对")
            return
        
        try:
            if not BINANCE_AVAILABLE:
                self.logger.error("❌ Binance API不可用，无法启动实时监控")
                return
                
            # 初始化客户端
            if not self.client:
                self.initialize_client()
            
            # 加载历史数据
            for symbol in self.symbols:
                self.load_historical_data(symbol)
            
            # 启动WebSocket管理器
            self.twm = ThreadedWebSocketManager(
                api_key=self.api_key, 
                api_secret=self.api_secret
            )
            self.twm.start()
            
            # 为每个交易对启动K线流
            for symbol in self.symbols:
                stream_name = self.twm.start_kline_socket(
                    callback=self._handle_socket_message,
                    symbol=symbol,
                    interval=self.interval
                )
                self.connection_status[symbol] = True
                self.logger.info(f"🔗 启动 {symbol} WebSocket连接: {stream_name}")
            
            self.is_running = True
            self.logger.info(f"🚀 实时监控已启动，监控 {len(self.symbols)} 个交易对")
            
        except Exception as e:
            self.logger.error(f"❌ 启动监控失败: {e}")
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止实时监控"""
        if not self.is_running:
            return
        
        try:
            if self.twm:
                self.twm.stop()
                self.twm = None
            
            self.is_running = False
            self.connection_status = {symbol: False for symbol in self.symbols}
            
            self.logger.info("🛑 实时监控已停止")
            
        except Exception as e:
            self.logger.error(f"❌ 停止监控失败: {e}")
    
    def get_latest_data(self, symbol: str, limit: int = 50) -> pd.DataFrame:
        """获取最新数据"""
        if symbol in self.data_cache:
            return self.data_cache[symbol].tail(limit).copy()
        return pd.DataFrame()
    
    def get_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            'is_running': self.is_running,
            'symbols': self.symbols,
            'connection_status': self.connection_status,
            'last_update': {
                symbol: timestamp.isoformat() if timestamp else None 
                for symbol, timestamp in self.last_update.items()
            },
            'data_cache_size': {
                symbol: len(df) for symbol, df in self.data_cache.items()
            },
            'strategies_count': {
                symbol: len(strategies) for symbol, strategies in self.strategies.items()
            }
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_monitoring()

# 使用示例
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建处理器
    processor = RealTimeDataProcessor(interval="4h")
    
    # 添加监控交易对
    processor.add_symbol("BTCUSDT")
    processor.add_symbol("ETHUSDT")
    
    # 添加数据回调
    def on_data_update(symbol: str, kline: Dict):
        print(f"📊 {symbol} 新数据: {kline['close']:.2f}")
    
    def on_signal_generated(symbol: str, signal: Dict):
        print(f"🚨 {symbol} 信号: {signal['signal']} (置信度: {signal['confidence']})")
    
    processor.add_data_callback(on_data_update)
    processor.add_signal_callback(on_signal_generated)
    
    try:
        # 启动监控
        processor.start_monitoring()
        
        # 保持运行
        while True:
            time.sleep(60)
            status = processor.get_status()
            print(f"📈 监控状态: {status['is_running']}, 连接: {sum(status['connection_status'].values())}/{len(status['symbols'])}")
            
    except KeyboardInterrupt:
        print("\n🛑 用户中断，停止监控...")
    finally:
        processor.stop_monitoring() 