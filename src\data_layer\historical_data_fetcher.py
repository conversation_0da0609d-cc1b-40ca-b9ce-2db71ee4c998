#!/usr/bin/env python3
"""
USDT交易对历史数据获取系统 (智能增量版本)
USDT Pairs Historical Data Fetcher (Smart Incremental Version)

支持智能增量更新的历史数据获取系统：
- 自动扫描本地数据库，避免重复下载
- 精确分析数据缺口，按币种和时间维度补充
- 支持交互式配置时间范围和币种数量
- 智能数据库管理，统一使用 usdt_historical_data.db
"""

import sys
import json
import sqlite3
from pathlib import Path
from datetime import datetime, timedelta
import urllib.request
import urllib.parse
import urllib.error
import time
import ssl

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def scan_local_databases(data_dir='./data'):
    """扫描本地数据库文件"""
    print(f"\n🔍 扫描本地数据库...")
    
    data_path = Path(data_dir)
    if not data_path.exists():
        print("❌ 数据目录不存在")
        return []
    
    db_files = list(data_path.glob('*.db'))
    local_databases = []
    
    for db_file in db_files:
        try:
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'usdt_klines_%'")
            tables = cursor.fetchall()
            
            for table_name, in tables:
                # 解析表名获取信息
                parts = table_name.replace('usdt_klines_', '').split('_')
                if len(parts) >= 2:
                    time_label = '_'.join(parts[:-1])
                    interval = parts[-1]
                    
                    # 获取数据统计
                    cursor.execute(f"""
                        SELECT 
                            COUNT(*) as total_klines,
                            COUNT(DISTINCT symbol) as unique_symbols,
                            MIN(datetime_str) as earliest_date,
                            MAX(datetime_str) as latest_date
                        FROM {table_name}
                    """)
                    
                    stats = cursor.fetchone()
                    if stats and stats[0] > 0:
                        # 获取币种列表
                        cursor.execute(f"SELECT DISTINCT symbol FROM {table_name} ORDER BY symbol")
                        symbols = [row[0] for row in cursor.fetchall()]
                        
                        local_databases.append({
                            'db_file': db_file,
                            'table_name': table_name,
                            'time_label': time_label,
                            'interval': interval,
                            'total_klines': stats[0],
                            'unique_symbols': stats[1],
                            'earliest_date': stats[2],
                            'latest_date': stats[3],
                            'symbols': symbols
                        })
            
            conn.close()
            
        except Exception as e:
            print(f"⚠️  读取数据库 {db_file.name} 时出错: {e}")
    
    if local_databases:
        print(f"✅ 发现 {len(local_databases)} 个本地数据表")
        print("\n📊 本地数据概览:")
        print("-" * 80)
        print("数据库文件\t\t时间标签\t间隔\tK线数\t币种数\t时间范围")
        print("-" * 80)
        
        for db_info in local_databases:
            print(f"{db_info['db_file'].name[:20]:<20}\t{db_info['time_label'][:10]:<10}\t{db_info['interval']}\t{db_info['total_klines']:,}\t{db_info['unique_symbols']}\t{db_info['earliest_date'][:10]} ~ {db_info['latest_date'][:10]}")
    else:
        print("❌ 未发现本地数据")
    
    return local_databases

def get_symbol_time_coverage(conn, table_name, symbol):
    """获取指定币种的时间覆盖范围"""
    try:
        cursor = conn.execute(f"""
            SELECT MIN(datetime_str), MAX(datetime_str), COUNT(*)
            FROM {table_name}
            WHERE symbol = ?
        """, (symbol,))
        
        result = cursor.fetchone()
        if result and result[0]:
            return {
                'start': datetime.strptime(result[0], '%Y-%m-%d %H:%M:%S'),
                'end': datetime.strptime(result[1], '%Y-%m-%d %H:%M:%S'),
                'count': result[2]
            }
        return None
    except:
        return None

def analyze_data_gaps(config, local_databases):
    """分析数据缺口，确定需要获取的增量数据"""
    print(f"\n🔍 分析数据缺口...")
    
    # 寻找匹配的本地数据
    matching_db = None
    for db_info in local_databases:
        if db_info['interval'] == config['interval']:
            matching_db = db_info
            break
    
    if not matching_db:
        print(f"❌ 未找到匹配间隔 ({config['interval']}) 的本地数据")
        return {
            'need_full_download': True,
            'missing_symbols': [],
            'missing_time_ranges': [],
            'symbol_gaps': {},
            'existing_data': None,
            'existing_symbols': [],
            'time_overlap': False,
            'requested_symbols': None,
            'need_symbol_analysis': config.get('selection_mode') == 'top_n'
        }
    
    print(f"✅ 找到匹配的本地数据: {matching_db['table_name']}")
    
    # 分析全局时间缺口
    existing_start = datetime.strptime(matching_db['earliest_date'], '%Y-%m-%d %H:%M:%S')
    existing_end = datetime.strptime(matching_db['latest_date'], '%Y-%m-%d %H:%M:%S')
    
    requested_start = config['start_time']
    requested_end = config['end_time']
    
    global_missing_time_ranges = []
    
    # 检查是否需要更早的数据
    if requested_start < existing_start:
        global_missing_time_ranges.append({
            'start': requested_start,
            'end': existing_start,
            'description': f"更早数据: {requested_start.strftime('%Y-%m-%d')} ~ {existing_start.strftime('%Y-%m-%d')}"
        })
    
    # 检查是否需要更新的数据
    if requested_end > existing_end:
        global_missing_time_ranges.append({
            'start': existing_end,
            'end': requested_end,
            'description': f"更新数据: {existing_end.strftime('%Y-%m-%d')} ~ {requested_end.strftime('%Y-%m-%d')}"
        })
    
    # 分析币种缺口
    existing_symbols = set(matching_db['symbols'])
    
    if config['selection_mode'] == 'top_n':
        # 对于top_n模式，需要标记为需要后续处理
        requested_symbols = None
        missing_symbols = []
        symbol_gaps = {}
    else:
        requested_symbols = set([s.strip().upper() + ('USDT' if not s.strip().upper().endswith('USDT') else '') 
                               for s in config['symbols'].split(',')])
        missing_symbols = list(requested_symbols - existing_symbols)
        
        # 详细分析每个币种的时间覆盖情况
        symbol_gaps = {}
        conn = sqlite3.connect(str(matching_db['db_file']))
        
        for symbol in requested_symbols:
            if symbol in existing_symbols:
                # 检查现有币种的时间完整性
                coverage = get_symbol_time_coverage(conn, matching_db['table_name'], symbol)
                if coverage:
                    symbol_missing_ranges = []
                    
                    # 检查是否需要更早的数据
                    if requested_start < coverage['start']:
                        symbol_missing_ranges.append({
                            'start': requested_start,
                            'end': coverage['start'],
                            'description': f"更早数据: {requested_start.strftime('%Y-%m-%d')} ~ {coverage['start'].strftime('%Y-%m-%d')}"
                        })
                    
                    # 检查是否需要更新的数据
                    if requested_end > coverage['end']:
                        symbol_missing_ranges.append({
                            'start': coverage['end'],
                            'end': requested_end,
                            'description': f"更新数据: {coverage['end'].strftime('%Y-%m-%d')} ~ {requested_end.strftime('%Y-%m-%d')}"
                        })
                    
                    if symbol_missing_ranges:
                        symbol_gaps[symbol] = {
                            'existing_coverage': coverage,
                            'missing_ranges': symbol_missing_ranges
                        }
        
        conn.close()
    
    analysis = {
        'need_full_download': False,
        'existing_data': matching_db,
        'missing_time_ranges': global_missing_time_ranges,
        'missing_symbols': missing_symbols,
        'existing_symbols': list(existing_symbols),
        'symbol_gaps': symbol_gaps,
        'time_overlap': not (requested_end < existing_start or requested_start > existing_end),
        'requested_symbols': requested_symbols,
        'need_symbol_analysis': config['selection_mode'] == 'top_n'
    }
    
    # 显示分析结果
    print(f"\n📊 数据缺口分析:")
    print(f"  现有数据时间: {existing_start.strftime('%Y-%m-%d')} ~ {existing_end.strftime('%Y-%m-%d')}")
    print(f"  请求数据时间: {requested_start.strftime('%Y-%m-%d')} ~ {requested_end.strftime('%Y-%m-%d')}")
    print(f"  现有币种数量: {len(existing_symbols)}")
    
    if global_missing_time_ranges:
        print(f"  ⚠️  全局缺失时间段: {len(global_missing_time_ranges)} 个")
        for time_range in global_missing_time_ranges:
            print(f"    - {time_range['description']}")
    else:
        print(f"  ✅ 全局时间范围完整")
    
    if config['selection_mode'] == 'specific':
        if missing_symbols:
            print(f"  ⚠️  完全缺失币种: {len(missing_symbols)} 个")
            for symbol in missing_symbols[:5]:
                print(f"    - {symbol}")
            if len(missing_symbols) > 5:
                print(f"    - ... 和其他 {len(missing_symbols)-5} 个")
        
        if symbol_gaps:
            print(f"  ⚠️  部分缺失币种: {len(symbol_gaps)} 个")
            for symbol, gap_info in list(symbol_gaps.items())[:3]:
                print(f"    - {symbol}: 缺失 {len(gap_info['missing_ranges'])} 个时间段")
            if len(symbol_gaps) > 3:
                print(f"    - ... 和其他 {len(symbol_gaps)-3} 个")
        
        if not missing_symbols and not symbol_gaps:
            print(f"  ✅ 所有指定币种数据完整")
    elif config['selection_mode'] == 'top_n':
        print(f"  🔄 需要分析前{config['top_n']}个币种 (现有{len(existing_symbols)}个)")
    
    return analysis

def get_user_input():
    """获取用户输入配置"""
    print("📈 USDT交易对历史数据获取系统 (智能增量版本)")
    print("="*60)
    
    # 先扫描本地数据
    local_databases = scan_local_databases()
    
    config = {}
    
    # 1. 时间范围选择
    print("\n⏰ 请选择时间范围:")
    print("  1. 1周 (7天)")
    print("  2. 1个月 (30天)")
    print("  3. 3个月 (90天)")
    print("  4. 6个月 (180天)")
    print("  5. 1年 (365天)")
    print("  6. 自定义天数")
    print("  7. 指定日期范围")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-7): ").strip()
            
            if choice == '1':
                config['days'] = 7
                config['time_label'] = '1week'
                break
            elif choice == '2':
                config['days'] = 30
                config['time_label'] = '1month'
                break
            elif choice == '3':
                config['days'] = 90
                config['time_label'] = '3months'
                break
            elif choice == '4':
                config['days'] = 180
                config['time_label'] = '6months'
                break
            elif choice == '5':
                config['days'] = 365
                config['time_label'] = '1year'
                break
            elif choice == '6':
                days = int(input("请输入天数: "))
                if days > 0:
                    config['days'] = days
                    config['time_label'] = f'{days}days'
                    break
                else:
                    print("❌ 天数必须大于0")
            elif choice == '7':
                start_date = input("请输入开始日期 (YYYY-MM-DD): ").strip()
                end_date = input("请输入结束日期 (YYYY-MM-DD): ").strip()
                try:
                    start_time = datetime.strptime(start_date, '%Y-%m-%d')
                    end_time = datetime.strptime(end_date, '%Y-%m-%d')
                    if start_time < end_time:
                        config['start_time'] = start_time
                        config['end_time'] = end_time
                        config['days'] = (end_time - start_time).days
                        config['time_label'] = f"{start_date}_to_{end_date}"
                        config['custom_range'] = True
                        break
                    else:
                        print("❌ 开始日期必须早于结束日期")
                except ValueError:
                    print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
            else:
                print("❌ 请输入1-7之间的数字")
        except ValueError:
            print("❌ 输入无效，请重新输入")
    
    # 设置时间范围（如果不是自定义范围）
    if not config.get('custom_range'):
        config['end_time'] = datetime.now()
        config['start_time'] = config['end_time'] - timedelta(days=config['days'])
    
    # 2. 币种选择
    print(f"\n💰 请选择币种获取方式:")
    print("  1. 获取交易量前N个币种")
    print("  2. 指定具体币种")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-2): ").strip()
            
            if choice == '1':
                while True:
                    try:
                        top_n = int(input("请输入要获取的币种数量 (建议10-100): "))
                        if 1 <= top_n <= 500:
                            config['selection_mode'] = 'top_n'
                            config['top_n'] = top_n
                            break
                        else:
                            print("❌ 币种数量应在1-500之间")
                    except ValueError:
                        print("❌ 请输入有效数字")
                break
            elif choice == '2':
                symbols_input = input("请输入币种列表 (用逗号分隔，如: BTC,ETH,BNB): ").strip()
                if symbols_input:
                    config['selection_mode'] = 'specific'
                    config['symbols'] = symbols_input
                    break
                else:
                    print("❌ 请输入至少一个币种")
            else:
                print("❌ 请输入1或2")
        except ValueError:
            print("❌ 输入无效，请重新输入")
    
    # 3. K线间隔
    print(f"\n📊 请选择K线时间间隔:")
    print("  1. 1小时 (1h)")
    print("  2. 4小时 (4h) - 推荐")
    print("  3. 1天 (1d)")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-3，默认2): ").strip()
            
            if choice == '1':
                config['interval'] = '1h'
                break
            elif choice == '2' or choice == '':
                config['interval'] = '4h'
                break
            elif choice == '3':
                config['interval'] = '1d'
                break
            else:
                print("❌ 请输入1-3之间的数字")
        except ValueError:
            print("❌ 输入无效，请重新输入")
    
    # 4. 其他配置
    print(f"\n⚙️  其他配置:")
    
    # 最小交易量过滤
    while True:
        try:
            min_volume_input = input("最小24h交易量过滤 (USDT，默认0): ").strip()
            if min_volume_input == '':
                config['min_volume'] = 0
                break
            else:
                min_volume = float(min_volume_input)
                if min_volume >= 0:
                    config['min_volume'] = min_volume
                    break
                else:
                    print("❌ 交易量不能为负数")
        except ValueError:
            print("❌ 请输入有效数字")
    
    # 代理设置
    while True:
        try:
            use_proxy = input("是否使用代理 (y/n，默认y): ").strip().lower()
            if use_proxy in ['y', 'yes', ''] or use_proxy == '':
                config['use_proxy'] = True
                while True:
                    try:
                        proxy_port_input = input("代理端口 (默认6754): ").strip()
                        if proxy_port_input == '':
                            config['proxy_port'] = 6754
                            break
                        else:
                            proxy_port = int(proxy_port_input)
                            if 1 <= proxy_port <= 65535:
                                config['proxy_port'] = proxy_port
                                break
                            else:
                                print("❌ 端口号应在1-65535之间")
                    except ValueError:
                        print("❌ 请输入有效端口号")
                break
            elif use_proxy in ['n', 'no']:
                config['use_proxy'] = False
                config['proxy_port'] = None
                break
            else:
                print("❌ 请输入y或n")
        except ValueError:
            print("❌ 输入无效，请重新输入")
    
    # 输出目录
    output_dir = input("输出目录 (默认./data): ").strip()
    config['output_dir'] = output_dir if output_dir else './data'
    
    # 存储本地数据库信息
    config['local_databases'] = local_databases
    
    return config

def display_config(config, gap_analysis=None):
    """显示配置信息"""
    print(f"\n📋 配置确认:")
    print("="*40)
    print(f"⏰ 时间范围: {config['days']} 天")
    print(f"📅 开始时间: {config['start_time'].strftime('%Y-%m-%d')}")
    print(f"📅 结束时间: {config['end_time'].strftime('%Y-%m-%d')}")
    print(f"📊 K线间隔: {config['interval']}")
    
    if config['selection_mode'] == 'top_n':
        print(f"💰 币种选择: 交易量前 {config['top_n']} 个")
    else:
        print(f"💰 币种选择: 指定币种 ({config['symbols']})")
    
    print(f"💵 最小交易量: {config['min_volume']:,.0f} USDT")
    print(f"🌐 代理设置: {'端口 ' + str(config['proxy_port']) if config['use_proxy'] else '不使用代理'}")
    print(f"📁 输出目录: {config['output_dir']}")
    
    # 显示增量更新信息
    if gap_analysis:
        print(f"\n🔄 增量更新计划:")
        if gap_analysis['need_full_download']:
            print(f"  📥 需要完整下载 (无匹配的本地数据)")
            # 估算数据量
            interval_hours = {'1h': 1, '4h': 4, '1d': 24}[config['interval']]
            if config['selection_mode'] == 'top_n':
                estimated_symbols = config['top_n']
            else:
                estimated_symbols = len([s.strip() for s in config['symbols'].split(',') if s.strip()])
            
            estimated_klines = estimated_symbols * (config['days'] * 24 // interval_hours)
            estimated_time = estimated_symbols * 0.5 * 2
            
            print(f"  预计K线数: {estimated_klines:,} 条")
            print(f"  预计耗时: {estimated_time/60:.1f} 分钟")
        else:
            print(f"  📊 基于现有数据: {gap_analysis['existing_data']['table_name']}")
            
            if gap_analysis['missing_time_ranges']:
                print(f"  ⏰ 需要补充时间段: {len(gap_analysis['missing_time_ranges'])} 个")
                total_missing_days = 0
                for time_range in gap_analysis['missing_time_ranges']:
                    days = (time_range['end'] - time_range['start']).days
                    total_missing_days += days
                    print(f"    - {time_range['description']} ({days}天)")
                
                # 估算增量数据
                interval_hours = {'1h': 1, '4h': 4, '1d': 24}[config['interval']]
                existing_symbols = len(gap_analysis['existing_symbols'])
                estimated_klines = existing_symbols * (total_missing_days * 24 // interval_hours)
                estimated_time = existing_symbols * 0.3  # 增量更新更快
                
                print(f"  📊 预计增量K线数: {estimated_klines:,} 条")
                print(f"  ⏱️  预计耗时: {estimated_time/60:.1f} 分钟")
            else:
                print(f"  ✅ 时间范围完整，无需补充")
            
            if gap_analysis['missing_symbols']:
                print(f"  💰 需要补充币种: {len(gap_analysis['missing_symbols'])} 个")
                for symbol in gap_analysis['missing_symbols'][:5]:
                    print(f"    - {symbol}")
                if len(gap_analysis['missing_symbols']) > 5:
                    print(f"    - ... 和其他 {len(gap_analysis['missing_symbols'])-5} 个")
    else:
        # 估算数据量
        interval_hours = {'1h': 1, '4h': 4, '1d': 24}[config['interval']]
        if config['selection_mode'] == 'top_n':
            estimated_symbols = config['top_n']
        else:
            estimated_symbols = len([s.strip() for s in config['symbols'].split(',') if s.strip()])
        
        estimated_klines = estimated_symbols * (config['days'] * 24 // interval_hours)
        estimated_time = estimated_symbols * 0.5 * 2  # 粗略估算
        
        print(f"\n📊 预估数据:")
        print(f"  预计K线数: {estimated_klines:,} 条")
        print(f"  预计耗时: {estimated_time/60:.1f} 分钟")
    
    while True:
        confirm = input(f"\n确认开始获取数据? (y/n): ").strip().lower()
        if confirm in ['y', 'yes']:
            return True
        elif confirm in ['n', 'no']:
            return False
        else:
            print("❌ 请输入y或n")

class USDTHistoricalFetcher:
    """USDT交易对历史数据获取器"""
    
    def __init__(self, proxy_port=6754, use_proxy=True, batch_delay=0.5):
        # API端点
        self.base_url = "https://api.binance.com"
        self.data_url = "https://data-api.binance.vision"
        self.batch_delay = batch_delay
        
        # 代理配置
        if use_proxy:
            self.proxy_config = {'http': f'http://127.0.0.1:{proxy_port}', 
                               'https': f'http://127.0.0.1:{proxy_port}'}
            print(f"✅ 使用代理: 127.0.0.1:{proxy_port}")
        else:
            self.proxy_config = None
            print("✅ 直连模式 (不使用代理)")
        
        # 设置SSL上下文
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE
        
        # 创建opener
        if self.proxy_config:
            proxy_handler = urllib.request.ProxyHandler(self.proxy_config)
            https_handler = urllib.request.HTTPSHandler(context=self.ssl_context)
            self.opener = urllib.request.build_opener(proxy_handler, https_handler)
        else:
            https_handler = urllib.request.HTTPSHandler(context=self.ssl_context)
            self.opener = urllib.request.build_opener(https_handler)
        
        print("✅ 历史数据获取器初始化完成")
    
    def _make_request(self, url, timeout=30):
        """发送HTTP请求"""
        try:
            request = urllib.request.Request(url)
            request.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)')
            
            with self.opener.open(request, timeout=timeout) as response:
                if response.status == 200:
                    data = json.loads(response.read().decode('utf-8'))
                    return data
                else:
                    print(f"❌ HTTP错误: {response.status}")
                    return None
        except Exception as e:
            print(f"❌ 请求错误: {str(e)[:100]}...")
            return None
    
    def get_usdt_pairs_current(self, min_volume=0):
        """获取USDT交易对当前数据"""
        print(f"\n📊 获取USDT交易对实时数据...")
        
        endpoint = f"{self.data_url}/api/v3/ticker/24hr"
        data = self._make_request(endpoint)
        
        if not data:
            print("❌ 无法获取实时数据")
            return {}
        
        # 筛选USDT交易对，按交易量排序
        usdt_pairs = {}
        for item in data:
            symbol = item['symbol']
            if symbol.endswith('USDT'):
                quote_volume = float(item['quoteVolume'])
                if quote_volume >= min_volume:
                    usdt_pairs[symbol] = {
                        'symbol': symbol,
                        'lastPrice': float(item['lastPrice']),
                        'priceChangePercent': float(item['priceChangePercent']),
                        'quoteVolume': quote_volume,
                        'volume': float(item['volume']),
                        'count': int(item['count'])
                    }
        
        # 按交易量排序
        sorted_pairs = dict(sorted(usdt_pairs.items(), 
                                 key=lambda x: x[1]['quoteVolume'], 
                                 reverse=True))
        
        print(f"✅ 获取到 {len(sorted_pairs)} 个USDT交易对 (最小交易量: {min_volume:,.0f} USDT)")
        return sorted_pairs
    
    def select_symbols(self, usdt_pairs, config):
        """选择要获取数据的币种"""
        if config['selection_mode'] == 'specific':
            # 指定具体币种
            symbols = [s.strip().upper() for s in config['symbols'].split(',')]
            # 验证币种是否存在
            valid_symbols = []
            for symbol in symbols:
                if not symbol.endswith('USDT'):
                    symbol += 'USDT'
                if symbol in usdt_pairs:
                    valid_symbols.append(symbol)
                else:
                    print(f"⚠️  币种 {symbol} 不存在或交易量不足")
            
            selected_pairs = {symbol: usdt_pairs[symbol] for symbol in valid_symbols}
            print(f"🎯 指定币种: {len(selected_pairs)} 个")
            
        else:
            # 选择交易量前N的币种
            selected_pairs = {}
            for i, (symbol, data) in enumerate(usdt_pairs.items()):
                if i >= config['top_n']:
                    break
                selected_pairs[symbol] = data
            
            print(f"🔝 选择交易量前 {len(selected_pairs)} 的USDT交易对")
        
        return selected_pairs
    
    def get_historical_klines_batch(self, symbol, interval='4h', start_time=None, end_time=None):
        """批量获取历史K线数据"""
        days = (end_time - start_time).days
        print(f"📈 获取 {symbol} {days}天的{interval}数据...")
        
        all_klines = []
        
        # 分段获取，每次获取30天的数据
        segment_days = 30
        current_start = start_time
        
        while current_start < end_time:
            current_end = min(current_start + timedelta(days=segment_days), end_time)
            
            start_timestamp = int(current_start.timestamp() * 1000)
            end_timestamp = int(current_end.timestamp() * 1000)
            
            # 构建API URL
            endpoint = f"{self.data_url}/api/v3/klines"
            params = {
                'symbol': symbol,
                'interval': interval,
                'startTime': start_timestamp,
                'endTime': end_timestamp,
                'limit': 1000
            }
            
            url = f"{endpoint}?" + urllib.parse.urlencode(params)
            
            data = self._make_request(url)
            
            if data:
                # 处理K线数据
                for item in data:
                    kline = {
                        'symbol': symbol,
                        'open_time': int(item[0]),
                        'open_price': float(item[1]),
                        'high_price': float(item[2]),
                        'low_price': float(item[3]),
                        'close_price': float(item[4]),
                        'volume': float(item[5]),
                        'close_time': int(item[6]),
                        'quote_volume': float(item[7]),
                        'trade_count': int(item[8]),
                        'datetime': datetime.fromtimestamp(int(item[0]) / 1000)
                    }
                    all_klines.append(kline)
                
                print(f"✅ 获取 {symbol} 段数据: {current_start.strftime('%m-%d')} ~ {current_end.strftime('%m-%d')} ({len(data)}条)")
            else:
                print(f"❌ 获取 {symbol} 段数据失败: {current_start.strftime('%m-%d')} ~ {current_end.strftime('%m-%d')}")
            
            # 移动到下一段
            current_start = current_end
            
            # 添加延迟避免API限制
            time.sleep(self.batch_delay)
        
        print(f"✅ {symbol} 总计获取: {len(all_klines)} 条K线数据")
        return all_klines
    
    def batch_get_historical_data(self, symbols, interval='4h', start_time=None, end_time=None):
        """批量获取历史数据"""
        days = (end_time - start_time).days
        print(f"\n🔄 批量获取 {len(symbols)} 个USDT对的{days}天历史数据...")
        print(f"📅 数据时间范围: {start_time.strftime('%Y-%m-%d')} ~ {end_time.strftime('%Y-%m-%d')}")
        
        all_klines = []
        success_count = 0
        total_start_time = time.time()
        
        for i, symbol in enumerate(symbols, 1):
            print(f"\n[{i}/{len(symbols)}] 🔄 处理 {symbol}...")
            start_time_single = time.time()
            
            klines = self.get_historical_klines_batch(symbol, interval, start_time, end_time)
            
            if klines:
                all_klines.extend(klines)
                success_count += 1
                elapsed = time.time() - start_time_single
                print(f"✅ {symbol}: {len(klines)} 条记录 (耗时: {elapsed:.1f}秒)")
            else:
                print(f"❌ {symbol}: 获取失败")
            
            # 每5个币种后休息
            if i % 5 == 0:
                print("⏳ 等待2秒避免API限制...")
                time.sleep(2)
        
        total_elapsed = time.time() - total_start_time
        
        print(f"\n📊 批量获取完成:")
        print(f"  成功: {success_count}/{len(symbols)} 个交易对")
        print(f"  总K线数: {len(all_klines):,} 条")
        print(f"  总耗时: {total_elapsed/60:.1f} 分钟")
        print(f"  平均每个币种: {total_elapsed/len(symbols):.1f} 秒")
        
        return all_klines

def create_database(db_path, time_label, interval):
    """创建数据库"""
    print(f"\n💾 创建历史数据库: {db_path}")
    
    conn = sqlite3.connect(str(db_path))
    
    # 创建USDT交易对表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS usdt_pairs_current (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            last_price REAL NOT NULL,
            price_change_percent REAL NOT NULL,
            quote_volume REAL NOT NULL,
            volume REAL NOT NULL,
            trade_count INTEGER NOT NULL,
            update_time INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, update_time)
        )
    """)
    
    # 创建历史K线表 (动态表名)
    table_name = f"usdt_klines_{time_label}_{interval}"
    conn.execute(f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            open_time INTEGER NOT NULL,
            open_price REAL NOT NULL,
            high_price REAL NOT NULL,
            low_price REAL NOT NULL,
            close_price REAL NOT NULL,
            volume REAL NOT NULL,
            close_time INTEGER NOT NULL,
            quote_volume REAL NOT NULL,
            trade_count INTEGER NOT NULL,
            datetime_str TEXT NOT NULL,
            year_month TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, open_time)
        )
    """)
    
    # 创建优化索引
    conn.execute(f"""
        CREATE INDEX IF NOT EXISTS idx_{table_name}_symbol_time 
        ON {table_name}(symbol, open_time DESC)
    """)
    
    conn.execute(f"""
        CREATE INDEX IF NOT EXISTS idx_{table_name}_time 
        ON {table_name}(open_time DESC)
    """)
    
    conn.execute(f"""
        CREATE INDEX IF NOT EXISTS idx_{table_name}_year_month 
        ON {table_name}(year_month, symbol)
    """)
    
    conn.execute(f"""
        CREATE INDEX IF NOT EXISTS idx_{table_name}_symbol 
        ON {table_name}(symbol)
    """)
    
    conn.commit()
    print(f"✅ 历史数据库表创建完成: {table_name}")
    
    return conn, table_name

def save_klines(conn, klines, table_name):
    """保存历史K线数据"""
    print(f"\n💾 保存历史K线数据到表: {table_name}")
    
    inserted = 0
    updated = 0
    batch_size = 1000
    
    for i in range(0, len(klines), batch_size):
        batch = klines[i:i+batch_size]
        
        for kline in batch:
            try:
                year_month = kline['datetime'].strftime('%Y-%m')
                
                # 先尝试插入
                cursor = conn.execute(f"""
                    INSERT OR IGNORE INTO {table_name}
                    (symbol, open_time, open_price, high_price, low_price, close_price,
                     volume, close_time, quote_volume, trade_count, datetime_str, year_month)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    kline['symbol'], kline['open_time'], kline['open_price'],
                    kline['high_price'], kline['low_price'], kline['close_price'],
                    kline['volume'], kline['close_time'], kline['quote_volume'],
                    kline['trade_count'], kline['datetime'].strftime('%Y-%m-%d %H:%M:%S'),
                    year_month
                ))
                
                if cursor.rowcount > 0:
                    inserted += 1
                else:
                    # 更新现有记录
                    conn.execute(f"""
                        UPDATE {table_name} SET
                        open_price=?, high_price=?, low_price=?, close_price=?,
                        volume=?, close_time=?, quote_volume=?, trade_count=?, 
                        datetime_str=?, year_month=?
                        WHERE symbol=? AND open_time=?
                    """, (
                        kline['open_price'], kline['high_price'], kline['low_price'],
                        kline['close_price'], kline['volume'], kline['close_time'],
                        kline['quote_volume'], kline['trade_count'],
                        kline['datetime'].strftime('%Y-%m-%d %H:%M:%S'), year_month,
                        kline['symbol'], kline['open_time']
                    ))
                    updated += 1
                    
            except Exception as e:
                print(f"保存K线错误 {kline.get('symbol', 'unknown')}: {e}")
        
        # 每批次提交一次
        conn.commit()
        
        if (i + batch_size) % 10000 == 0:
            print(f"📊 已处理: {i + batch_size:,}/{len(klines):,} 条记录...")
    
    conn.commit()
    print(f"✅ 保存K线数据: 新增{inserted:,}条，更新{updated:,}条")
    return inserted + updated

def analyze_data(conn, table_name, config):
    """分析数据"""
    print(f"\n📊 USDT交易对数据分析")
    print("="*60)
    
    # 历史数据统计
    cursor = conn.execute(f"""
        SELECT 
            COUNT(*) as total_klines,
            COUNT(DISTINCT symbol) as unique_symbols,
            MIN(datetime_str) as earliest_date,
            MAX(datetime_str) as latest_date,
            COUNT(DISTINCT year_month) as months_covered
        FROM {table_name}
    """)
    
    stats = cursor.fetchone()
    
    if stats:
        print(f"📈 历史K线数据统计:")
        print(f"  总K线数: {stats[0]:,}")
        print(f"  交易对数: {stats[1]:,}")
        print(f"  时间范围: {stats[2]} ~ {stats[3]}")
        print(f"  覆盖月份: {stats[4]} 个月")
        print(f"  配置天数: {config['days']} 天")
    
    # 按月份统计
    print(f"\n📅 按月份数据分布:")
    cursor = conn.execute(f"""
        SELECT year_month, COUNT(*) as klines_count, COUNT(DISTINCT symbol) as symbols_count
        FROM {table_name}
        GROUP BY year_month
        ORDER BY year_month DESC
        LIMIT 12
    """)
    
    print("月份\t\tK线数\t交易对数")
    print("-" * 35)
    for row in cursor.fetchall():
        year_month, klines_count, symbols_count = row
        print(f"{year_month}\t\t{klines_count:,}\t{symbols_count}")
    
    # 数据完整性检查
    print(f"\n🔍 数据完整性检查:")
    cursor = conn.execute(f"""
        SELECT symbol, COUNT(*) as klines_count, 
               MIN(datetime_str) as first_date, 
               MAX(datetime_str) as last_date
        FROM {table_name}
        GROUP BY symbol
        ORDER BY klines_count DESC
        LIMIT 10
    """)
    
    print("交易对\t\tK线数\t最早日期\t\t最新日期")
    print("-" * 65)
    for row in cursor.fetchall():
        symbol, count, first_date, last_date = row
        print(f"{symbol[:12]:<12}\t{count:,}\t{first_date[:10]}\t{last_date[:10]}")

def main():
    """主函数"""
    try:
        # 获取用户输入配置
        config = get_user_input()
        
        # 分析数据缺口
        gap_analysis = analyze_data_gaps(config, config['local_databases'])
        
        # 显示配置并确认
        if not display_config(config, gap_analysis):
            print("❌ 用户取消操作")
            return
        
        print(f"\n⏰ 开始时间: {datetime.now()}")
        
        # 初始化获取器
        fetcher = USDTHistoricalFetcher(
            proxy_port=config.get('proxy_port', 6754),
            use_proxy=config['use_proxy'],
            batch_delay=0.5
        )
        
        # 1. 获取USDT交易对实时数据
        print(f"\n🔄 第一步: 获取USDT交易对实时数据")
        usdt_pairs = fetcher.get_usdt_pairs_current(config['min_volume'])
        
        if not usdt_pairs:
            print("❌ 无法获取USDT交易对数据")
            return
        
        # 2. 选择币种
        selected_pairs = fetcher.select_symbols(usdt_pairs, config)
        
        if not selected_pairs:
            print("❌ 没有选择到任何币种")
            return
        
        # 2.1 重新分析币种缺口（针对top_n模式）
        if gap_analysis['need_symbol_analysis']:
            print(f"\n🔄 重新分析币种缺口...")
            requested_symbols = set(selected_pairs.keys())
            existing_symbols = set(gap_analysis['existing_symbols'])
            
            missing_symbols = list(requested_symbols - existing_symbols)
            
            # 详细分析每个币种的时间覆盖情况
            symbol_gaps = {}
            if gap_analysis['existing_data']:
                conn_temp = sqlite3.connect(str(gap_analysis['existing_data']['db_file']))
                table_name = gap_analysis['existing_data']['table_name']
                
                for symbol in requested_symbols:
                    if symbol in existing_symbols:
                        # 检查现有币种的时间完整性
                        coverage = get_symbol_time_coverage(conn_temp, table_name, symbol)
                        if coverage:
                            symbol_missing_ranges = []
                            
                            # 检查是否需要更早的数据
                            if config['start_time'] < coverage['start']:
                                symbol_missing_ranges.append({
                                    'start': config['start_time'],
                                    'end': coverage['start'],
                                    'description': f"更早数据: {config['start_time'].strftime('%Y-%m-%d')} ~ {coverage['start'].strftime('%Y-%m-%d')}"
                                })
                            
                            # 检查是否需要更新的数据
                            if config['end_time'] > coverage['end']:
                                symbol_missing_ranges.append({
                                    'start': coverage['end'],
                                    'end': config['end_time'],
                                    'description': f"更新数据: {coverage['end'].strftime('%Y-%m-%d')} ~ {config['end_time'].strftime('%Y-%m-%d')}"
                                })
                            
                            if symbol_missing_ranges:
                                symbol_gaps[symbol] = {
                                    'existing_coverage': coverage,
                                    'missing_ranges': symbol_missing_ranges
                                }
                
                conn_temp.close()
            
            # 更新分析结果
            gap_analysis['missing_symbols'] = missing_symbols
            gap_analysis['symbol_gaps'] = symbol_gaps
            gap_analysis['requested_symbols'] = requested_symbols
            
            print(f"📊 币种分析结果:")
            print(f"  请求币种数: {len(requested_symbols)}")
            print(f"  现有币种数: {len(existing_symbols)}")
            print(f"  完全缺失币种: {len(missing_symbols)}")
            print(f"  部分缺失币种: {len(symbol_gaps)}")
            
            if missing_symbols:
                print(f"  完全缺失币种列表:")
                for i, symbol in enumerate(missing_symbols[:10], 1):
                    print(f"    {i:2d}. {symbol}")
                if len(missing_symbols) > 10:
                    print(f"    ... 和其他 {len(missing_symbols)-10} 个")
            
            if symbol_gaps:
                print(f"  部分缺失币种列表:")
                for i, (symbol, gap_info) in enumerate(list(symbol_gaps.items())[:5], 1):
                    print(f"    {i:2d}. {symbol}: 缺失 {len(gap_info['missing_ranges'])} 个时间段")
                if len(symbol_gaps) > 5:
                    print(f"    ... 和其他 {len(symbol_gaps)-5} 个")
        
        # 3. 确定数据库和表
        if gap_analysis['need_full_download']:
            # 创建新数据库 - 使用更合理的命名
            db_name = f"usdt_historical_data.db"
            db_path = Path(config['output_dir']) / db_name
            db_path.parent.mkdir(exist_ok=True)
            
            # 表名包含间隔信息
            table_name = f"usdt_klines_{config['interval']}"
            
            # 检查是否已存在同名数据库
            if db_path.exists():
                print(f"⚠️  数据库已存在: {db_name}")
                conn = sqlite3.connect(str(db_path))
                # 检查表是否存在
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                if cursor.fetchone():
                    print(f"✅ 使用现有数据表: {table_name}")
                else:
                    print(f"🆕 创建新数据表: {table_name}")
                    conn, table_name = create_database(db_path, "historical", config['interval'])
            else:
                conn, table_name = create_database(db_path, "historical", config['interval'])
        else:
            # 使用现有数据库
            db_path = gap_analysis['existing_data']['db_file']
            table_name = gap_analysis['existing_data']['table_name']
            conn = sqlite3.connect(str(db_path))
            print(f"✅ 使用现有数据库: {db_path.name}")
            print(f"✅ 使用现有数据表: {table_name}")
        
        # 5. 智能获取历史数据
        print(f"\n🔄 第二步: 智能获取历史数据")
        
        all_klines = []
        
        if gap_analysis['need_full_download']:
            # 完整下载
            selected_symbols = list(selected_pairs.keys())
            print(f"📋 完整获取 {len(selected_symbols)} 个USDT交易对的历史数据:")
            
            for i, symbol in enumerate(selected_symbols, 1):
                if i <= 20:
                    volume = selected_pairs[symbol]['quoteVolume']
                    print(f"  {i:2d}. {symbol:<12} (24h交易量: {volume:>12,.0f} USDT)")
                elif i == 21:
                    print(f"  ... 和其他 {len(selected_symbols)-20} 个交易对")
                    break
            
            all_klines = fetcher.batch_get_historical_data(
                selected_symbols, 
                config['interval'], 
                config['start_time'], 
                config['end_time']
            )
        else:
            # 增量更新
            print(f"🔄 增量更新模式")
            
            # 1. 获取完全缺失的币种的完整数据
            if gap_analysis['missing_symbols']:
                print(f"📋 为 {len(gap_analysis['missing_symbols'])} 个完全缺失币种获取完整数据:")
                
                new_klines = fetcher.batch_get_historical_data(
                    gap_analysis['missing_symbols'],
                    config['interval'],
                    config['start_time'],
                    config['end_time']
                )
                
                if new_klines:
                    all_klines.extend(new_klines)
            
            # 2. 为部分缺失的币种补充缺失时间段
            if gap_analysis.get('symbol_gaps'):
                print(f"📋 为 {len(gap_analysis['symbol_gaps'])} 个币种补充缺失时间段:")
                
                for symbol, gap_info in gap_analysis['symbol_gaps'].items():
                    print(f"  🔄 处理 {symbol}:")
                    
                    for time_range in gap_info['missing_ranges']:
                        print(f"    ⏰ {time_range['description']}")
                        
                        symbol_klines = fetcher.batch_get_historical_data(
                            [symbol],
                            config['interval'],
                            time_range['start'],
                            time_range['end']
                        )
                        
                        if symbol_klines:
                            all_klines.extend(symbol_klines)
            
            # 3. 全局时间缺口补充（为所有现有币种补充）
            if gap_analysis['missing_time_ranges']:
                # 只为没有个别时间缺口的币种补充全局时间缺口
                symbols_with_individual_gaps = set(gap_analysis.get('symbol_gaps', {}).keys())
                existing_symbols_need_global_update = [
                    s for s in gap_analysis['existing_symbols'] 
                    if s not in symbols_with_individual_gaps
                ]
                
                if existing_symbols_need_global_update:
                    print(f"📋 为 {len(existing_symbols_need_global_update)} 个币种补充全局缺失时间段:")
                    
                    for time_range in gap_analysis['missing_time_ranges']:
                        print(f"  ⏰ {time_range['description']}")
                        
                        time_klines = fetcher.batch_get_historical_data(
                            existing_symbols_need_global_update,
                            config['interval'],
                            time_range['start'],
                            time_range['end']
                        )
                        
                        if time_klines:
                            all_klines.extend(time_klines)
            
            if not all_klines:
                print("✅ 本地数据已完整，无需获取新数据")
        
        # 6. 保存历史数据
        if all_klines:
            save_klines(conn, all_klines, table_name)
            
            # 7. 数据分析
            analyze_data(conn, table_name, config)
        
        # 关闭数据库
        conn.close()
        
        # 总结
        print(f"\n🎊 USDT交易对历史数据获取完成！")
        print("="*60)
        print(f"✅ USDT交易对总数: {len(usdt_pairs)}")
        
        if gap_analysis['need_full_download']:
            print(f"✅ 完整获取交易对: {len(selected_pairs)}")
            print(f"✅ 新增K线数据: {len(all_klines):,}")
        else:
            print(f"✅ 现有交易对: {len(gap_analysis['existing_symbols'])}")
            print(f"✅ 增量K线数据: {len(all_klines):,}")
            if gap_analysis['missing_symbols']:
                print(f"✅ 新增币种: {len(gap_analysis['missing_symbols'])}")
        
        print(f"✅ 数据库文件: {db_path}")
        print(f"✅ 数据表名: {table_name}")
        print(f"✅ 数据更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 计算数据库大小
        if db_path.exists():
            db_size_mb = db_path.stat().st_size / (1024 * 1024)
            print(f"✅ 数据库大小: {db_size_mb:.1f} MB")
        
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    print(f"\n⏰ 结束时间: {datetime.now()}") 