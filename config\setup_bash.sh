#!/bin/bash
# TradeApi Alert Git Bash 环境设置脚本
# 使用方法: source config/setup_bash.sh

echo "正在设置 TradeApi Alert 开发环境..."

# 检查并复制.bashrc配置到用户目录
if [ ! -f ~/.bashrc ]; then
    echo "创建 ~/.bashrc 文件..."
    touch ~/.bashrc
fi

# 检查是否已经添加了我们的配置
if ! grep -q "# TradeApi Alert Config" ~/.bashrc; then
    echo "# TradeApi Alert Config" >> ~/.bashrc
    echo "source $(pwd)/config/.bashrc" >> ~/.bashrc
    echo "已添加 TradeApi Alert 配置到 ~/.bashrc"
else
    echo "配置已存在于 ~/.bashrc 中"
fi

# 立即加载配置
source config/.bashrc

echo ""
echo "✅ 环境设置完成！"
echo ""
echo "🚀 现在可以使用以下工具："
echo "   - uv (Python包管理): $(uv --version)"
echo "   - Node.js: $(node --version)"
echo "   - npm: $(npm --version)"
echo "   - npx: $(npx --version)"
echo ""
echo "💡 可用的便捷别名:"
echo "   ll, la, gs, ga, gc, gp, gl, activate, py"
echo ""
echo "🔧 启动项目虚拟环境: activate"
echo "🏃 运行项目: uv run python src/main.py" 