"""
EMA动态仓位策略
EMA Dynamic Position Strategy

结合EMA突破策略和动态仓位管理的完整交易系统
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime

from .ema_breakout_strategy import EMABreakoutStrategy, SignalType
from .dynamic_position_manager import DynamicPositionManager
from ..utils.output_formatter import BacktestOutputFormatter


class EMADynamicStrategy:
    """EMA动态仓位策略"""
    
    def __init__(self, strategy_params: Dict = None, initial_capital: float = 10000,
                 enable_partial_profit: bool = False, verbose_level: int = 1):
        """
        初始化EMA动态仓位策略

        Args:
            strategy_params: EMA策略参数
            initial_capital: 初始资金
            enable_partial_profit: 是否启用分批止盈
            verbose_level: 输出详细程度 (0=简洁, 1=标准, 2=详细)
        """
        # 创建基础EMA策略
        self.ema_strategy = EMABreakoutStrategy(strategy_params or {}, None)

        # 创建动态仓位管理器
        self.position_manager = DynamicPositionManager(
            base_strategy=self.ema_strategy,
            initial_capital=initial_capital
        )

        self.initial_capital = initial_capital
        self.current_data = {}  # 存储当前各币种的数据

        # 交易逻辑配置
        self.enable_partial_profit = enable_partial_profit  # 分批止盈开关

        # 输出格式化器
        self.formatter = BacktestOutputFormatter(verbose_level)

        # 统计数据
        self.stats = {
            'total_signals': 0,
            'successful_entries': 0,
            'failed_entries': 0,
            'closed_trades': 0,
            'profitable_trades': 0,
            'losing_trades': 0
        }

        # 交易历史记录
        self.trade_history = []
        
    def simulate_real_time_trading(self, symbol_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        时间序列交易模拟（模拟真实交易行为）

        Args:
            symbol_data: 各币种的历史数据 {symbol: DataFrame}

        Returns:
            Dict: 模拟结果
        """
        print("🕐 开始时间序列交易模拟...")

        # 1. 构建统一时间轴
        all_timestamps = self._build_unified_timeline(symbol_data)
        print(f"📅 时间范围: {all_timestamps[0]} 至 {all_timestamps[-1]}")
        print(f"⏰ 总时间点: {len(all_timestamps)} 个")

        # 2. 为每个币种预计算指标和信号
        symbol_indicators = {}
        symbol_signals = {}
        for symbol, data in symbol_data.items():
            try:
                indicators = self.ema_strategy.calculate_indicators(data)
                signals = self.ema_strategy.generate_signals(indicators)

                # 强制对齐信号数据的索引与指标数据的索引
                if len(signals) == len(indicators):
                    signals.index = indicators.index
                    print(f"✅ {symbol} 指标和信号计算完成，索引已对齐")
                else:
                    print(f"⚠️ {symbol} 数据长度不匹配：指标{len(indicators)}, 信号{len(signals)}")
                    # 截取到相同长度
                    min_len = min(len(indicators), len(signals))
                    indicators = indicators.iloc[:min_len]
                    signals = signals.iloc[:min_len]
                    signals.index = indicators.index
                    print(f"✅ {symbol} 已截取到相同长度{min_len}并对齐索引")

                symbol_indicators[symbol] = indicators
                symbol_signals[symbol] = signals

            except Exception as e:
                print(f"⚠️ {symbol} 计算失败: {e}")
                import traceback
                traceback.print_exc()
                continue

        if not symbol_indicators or not symbol_signals:
            return {'error': '无有效指标或信号数据'}

        # 3. 逐时间点模拟交易
        print(f"\n🎯 开始逐时间点交易模拟...")

        debug_count = 0
        signal_check_count = 0
        data_check_count = 0

        for i, timestamp in enumerate(all_timestamps):
            # 设置当前进度信息
            progress = (i / len(all_timestamps)) * 100
            self._current_progress_info = {
                'progress': progress,
                'timestamp': timestamp,
                'index': i,
                'total': len(all_timestamps)
            }

            if i % 100 == 0:  # 每100个时间点打印进度
                self.formatter.print_progress_simple(progress, timestamp)

                # 每2000个时间点显示阶段性总结
                if i > 0 and i % 2000 == 0:
                    current_prices = {symbol: data['price'] for symbol, data in current_data.items()} if current_data else {}
                    if current_prices:
                        portfolio_summary = self.position_manager.get_portfolio_summary(current_prices)

                        stage_stats = self.stats.copy()
                        stage_stats.update({
                            'initial_capital': self.initial_capital,
                            'current_equity': portfolio_summary['total_portfolio_value'],
                            'available_cash': portfolio_summary['available_cash'],
                            'position_value': portfolio_summary['total_portfolio_value'] - portfolio_summary['available_cash'],
                            'active_positions': portfolio_summary['active_positions'],
                            'total_return_pct': (portfolio_summary['total_portfolio_value'] - self.initial_capital) / self.initial_capital * 100,
                            'max_drawdown_pct': 0  # TODO: 计算最大回撤
                        })

                        self.formatter.print_stage_summary(
                            progress, all_timestamps[0], timestamp, stage_stats
                        )

                        # 显示最近交易记录
                        if self.trade_history:
                            self.formatter.print_trade_table(self.trade_history, max_rows=5)

            # 获取当前时间点的数据（使用预计算的信号）
            current_data = self._get_current_timestamp_data_v2(timestamp, symbol_indicators, symbol_signals)

            if current_data:
                data_check_count += 1

                # 调试：检查是否有买入信号
                buy_signals = [symbol for symbol, data in current_data.items()
                              if data['signal_data'].get('signal', 0) == 1]
                if buy_signals:
                    signal_check_count += 1
                    if debug_count < 5:  # 只打印前5个信号用于调试
                        print(f"🔍 调试 {timestamp}: 发现买入信号 {buy_signals}")
                        for symbol in buy_signals[:2]:  # 只显示前2个
                            data = current_data[symbol]
                            print(f"  {symbol}: 价格{data['price']:.4f}, 信号{data['signal_data'].get('signal', 'N/A')}, 原因: {data['signal_data'].get('reason', 'N/A')}")
                        debug_count += 1

                # 额外调试：检查前几个时间点的信号值
                if debug_count < 3 and data_check_count <= 10:
                    for symbol, data in current_data.items():
                        signal_val = data['signal_data'].get('signal', 'N/A')
                        if signal_val != 0:  # 只显示非0信号
                            print(f"🔍 时间点{i} {timestamp}: {symbol} 信号值={signal_val}")

                # 模拟真实交易决策过程
                self._simulate_trading_decision(timestamp, current_data)

        print(f"\n🔍 调试信息: 总共发现 {signal_check_count} 个时间点有买入信号")
        print(f"🔍 调试信息: 总共有 {data_check_count} 个时间点有数据")

        # 4. 最终平仓
        final_data = self._get_current_timestamp_data(all_timestamps[-1], symbol_indicators)
        if final_data:
            self._close_all_positions(all_timestamps[-1], {s: d['price'] for s, d in final_data.items()})

        print("✅ 交易模拟完成")
        return self._calculate_backtest_results()

    def _build_unified_timeline(self, symbol_data: Dict[str, pd.DataFrame]) -> List[datetime]:
        """构建统一时间轴"""
        all_timestamps = set()
        for data in symbol_data.values():
            all_timestamps.update(data.index)
        return sorted(all_timestamps)

    def _get_current_timestamp_data(self, timestamp: datetime,
                                   symbol_indicators: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """
        获取当前时间点的数据（模拟实时数据获取）

        Args:
            timestamp: 当前时间点
            symbol_indicators: 各币种的指标数据

        Returns:
            Dict: {symbol: {'price': float, 'signal_data': dict}}
        """
        current_data = {}

        for symbol, indicators in symbol_indicators.items():
            if timestamp in indicators.index:
                row = indicators.loc[timestamp]

                # 实时生成信号（基于当前时间点及之前的数据）
                historical_data = indicators.loc[:timestamp]

                try:
                    # 使用EMA策略实时生成信号
                    signals = self.ema_strategy.generate_signals(historical_data)

                    if timestamp in signals.index:
                        signal_row = signals.loc[timestamp]

                        current_data[symbol] = {
                            'price': row['close'],
                            'signal_data': signal_row.to_dict(),
                            'indicators': row.to_dict()
                        }
                except Exception as e:
                    # 信号生成失败，跳过该币种
                    continue

        return current_data

    def _get_current_timestamp_data_v2(self, timestamp: datetime,
                                      symbol_indicators: Dict[str, pd.DataFrame],
                                      symbol_signals: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """
        获取当前时间点的数据（使用预计算的信号，索引已对齐）

        Args:
            timestamp: 当前时间点
            symbol_indicators: 各币种的指标数据
            symbol_signals: 各币种的信号数据

        Returns:
            Dict: {symbol: {'price': float, 'signal_data': dict}}
        """
        current_data = {}

        for symbol in symbol_indicators.keys():
            # 现在索引已对齐，可以直接使用datetime索引
            if (timestamp in symbol_indicators[symbol].index and
                timestamp in symbol_signals[symbol].index):

                indicator_row = symbol_indicators[symbol].loc[timestamp]
                signal_row = symbol_signals[symbol].loc[timestamp]

                current_data[symbol] = {
                    'price': indicator_row['close'],
                    'signal_data': signal_row.to_dict(),
                    'indicators': indicator_row.to_dict()
                }

        return current_data

    def _unified_exit_logic(self, position, current_price: float, signal_data: dict) -> dict:
        """
        统一的退出逻辑管理

        Args:
            position: 持仓信息
            current_price: 当前价格
            signal_data: 信号数据

        Returns:
            dict: {'should_exit': bool, 'reason': str, 'partial_ratio': float}
        """
        entry_price = position.entry_price

        # 计算盈亏
        current_gain_pct = (current_price - entry_price) / entry_price
        current_loss_pct = (entry_price - current_price) / entry_price

        # 退出条件优先级排序

        # 1. 固定止损：亏损大于15%（最高优先级）
        if current_loss_pct > self.ema_strategy.params['stop_loss_pct']:
            return {
                'should_exit': True,
                'reason': f"固定止损，亏损{current_loss_pct*100:.1f}%",
                'partial_ratio': 1.0
            }

        # 2. 分批止盈：盈利大于50%且未分批止盈（可配置开关）
        if (hasattr(self, 'enable_partial_profit') and self.enable_partial_profit and
            current_gain_pct > self.ema_strategy.params['partial_profit_pct'] and
            not position.partial_profit_taken):
            return {
                'should_exit': True,
                'reason': f"分批止盈，盈利{current_gain_pct*100:.1f}%，减仓50%",
                'partial_ratio': 0.5
            }

        # 3. 技术退出：EMA信号
        if signal_data.get('signal', 0) == SignalType.SELL.value:
            return {
                'should_exit': True,
                'reason': signal_data.get('reason', '技术退出'),
                'partial_ratio': 1.0
            }

        # 无退出条件
        return {
            'should_exit': False,
            'reason': '',
            'partial_ratio': 1.0
        }

    def _unified_entry_logic(self, symbol: str, current_price: float, signal_data: dict) -> dict:
        """
        统一的入场逻辑管理

        Args:
            symbol: 交易对
            current_price: 当前价格
            signal_data: 信号数据

        Returns:
            dict: {'should_enter': bool, 'reason': str, 'confidence': float}
        """
        # 检查是否已有该币种的持仓
        if symbol in self.position_manager.positions:
            return {
                'should_enter': False,
                'reason': '已有持仓',
                'confidence': 0.0
            }

        # 检查是否有买入信号
        if signal_data.get('signal', 0) == SignalType.BUY.value:
            return {
                'should_enter': True,
                'reason': signal_data.get('reason', '买入信号'),
                'confidence': signal_data.get('confidence', 0.5)
            }

        # 无入场条件
        return {
            'should_enter': False,
            'reason': '无买入信号',
            'confidence': 0.0
        }

    def _simulate_trading_decision(self, timestamp: datetime, current_data: Dict[str, Dict]):
        """
        模拟真实交易决策过程

        Args:
            timestamp: 当前时间
            current_data: 当前时间点的数据 {symbol: {'price': float, 'signal_data': dict}}
        """
        # 提取价格信息
        current_prices = {symbol: data['price'] for symbol, data in current_data.items()}

        # 1. 更新所有持仓的跟踪信息
        for symbol in self.position_manager.positions.keys():
            if symbol in current_prices:
                self.position_manager.update_position_tracking(symbol, current_prices[symbol])

        # 2. 检查退出信号（优先处理，释放资金）
        self._check_exit_signals_realtime(timestamp, current_data)

        # 3. 检查入场信号（使用释放后的资金）
        self._check_entry_signals_realtime(timestamp, current_data)
    
    def _check_exit_signals_realtime(self, timestamp: datetime, current_data: Dict[str, Dict]):
        """实时检查退出信号（使用统一退出逻辑）"""
        positions_to_close = []

        for symbol, position in self.position_manager.positions.items():
            if symbol not in current_data:
                continue

            current_price = current_data[symbol]['price']
            signal_data = current_data[symbol]['signal_data']

            # 使用统一的退出逻辑判断
            exit_decision = self._unified_exit_logic(position, current_price, signal_data)

            if exit_decision['should_exit']:
                positions_to_close.append({
                    'symbol': symbol,
                    'exit_price': current_price,
                    'exit_time': timestamp,
                    'exit_reason': exit_decision['reason'],
                    'partial_ratio': exit_decision['partial_ratio']
                })

        # 执行平仓（释放资金）
        for close_info in positions_to_close:
            trade_record = self.position_manager.close_position(**close_info)
            if trade_record:
                self.stats['closed_trades'] += 1
                if trade_record['pnl_pct'] > 0:
                    self.stats['profitable_trades'] += 1
                else:
                    self.stats['losing_trades'] += 1

                self.formatter.print_position_close(
                    timestamp, close_info['symbol'],
                    trade_record['entry_price'], close_info['exit_price'],
                    trade_record['pnl_pct'], close_info['exit_reason']
                )

                # 记录平仓历史
                self.trade_history.append({
                    'timestamp': timestamp,
                    'symbol': close_info['symbol'],
                    'action': '平仓',
                    'price': close_info['exit_price'],
                    'return': trade_record['pnl_pct']
                })
    
    def _check_entry_signals_realtime(self, timestamp: datetime, current_data: Dict[str, Dict]):
        """实时检查入场信号（使用统一入场逻辑）"""
        # 1. 收集所有买入机会
        buy_opportunities = []

        for symbol, data in current_data.items():
            current_price = data['price']
            signal_data = data['signal_data']

            # 使用统一的入场逻辑判断
            entry_decision = self._unified_entry_logic(symbol, current_price, signal_data)

            if entry_decision['should_enter']:
                buy_opportunities.append({
                    'symbol': symbol,
                    'price': current_price,
                    'confidence': entry_decision['confidence'],
                    'reason': entry_decision['reason']
                })

        if not buy_opportunities:
            return

        # 2. 更新统计数据
        self.stats['total_signals'] += len(buy_opportunities)

        # 3. 计算可用交易资金
        current_prices = {symbol: data['price'] for symbol, data in current_data.items()}
        total_trade_amount = self.position_manager.calculate_trade_amount(current_prices)
        portfolio_summary = self.position_manager.get_portfolio_summary(current_prices)

        # 4. 显示交易事件进度
        if hasattr(self, '_current_progress_info'):
            progress = self._current_progress_info['progress']
            self.formatter.print_trading_event_progress(
                progress, timestamp, f"发现{len(buy_opportunities)}个交易机会"
            )

        # 5. 打印交易机会分析
        self.formatter.print_trading_opportunity(
            timestamp, buy_opportunities,
            portfolio_summary['available_cash'],
            portfolio_summary['total_portfolio_value']
        )

        # 4. 资金分配策略
        num_opportunities = len(buy_opportunities)

        if num_opportunities == 1:
            # 单个机会：使用全部资金
            allocated_amount = total_trade_amount
        else:
            # 多个机会：平均分配资金
            allocated_amount = total_trade_amount / num_opportunities

        # 4. 逐个执行开仓
        successful_entries = 0

        for opportunity in buy_opportunities:
            symbol = opportunity['symbol']
            price = opportunity['price']

            # 检查是否有足够资金或可用杠杆
            can_open, leverage = self.position_manager.can_open_position(allocated_amount)

            if can_open:
                # 开仓
                success = self.position_manager.open_position(
                    symbol=symbol,
                    entry_price=price,
                    entry_time=timestamp,
                    trade_amount=allocated_amount,
                    leverage=leverage
                )

                if success:
                    successful_entries += 1
                    self.stats['successful_entries'] += 1

                    # 获取EMA数据用于详细显示
                    ema_data = current_data[symbol]['indicators'] if self.formatter.verbose_level >= 2 else None

                    self.formatter.print_position_open(
                        timestamp, symbol, price, allocated_amount,
                        leverage, opportunity['reason'], ema_data
                    )

                    # 记录开仓历史
                    self.trade_history.append({
                        'timestamp': timestamp,
                        'symbol': symbol,
                        'action': '开仓',
                        'price': price,
                        'amount': allocated_amount,
                        'leverage': leverage
                    })
                else:
                    self.stats['failed_entries'] += 1
            else:
                self.stats['failed_entries'] += 1
                if self.formatter.verbose_level >= 1:
                    print(f"⚠️ {symbol} 资金不足，无法开仓（需要{self.formatter.format_amount(allocated_amount)}）")

        if successful_entries > 0:
            # 更新资金状态
            portfolio_summary = self.position_manager.get_portfolio_summary(current_prices)
            self.formatter.print_portfolio_status(
                portfolio_summary['available_cash'],
                portfolio_summary['total_portfolio_value'],
                portfolio_summary['active_positions'],
                portfolio_summary.get('unrealized_pnl', 0)
            )
    
    def _close_all_positions(self, final_timestamp: datetime, current_prices: Dict[str, float]):
        """最终平仓所有持仓"""
        for symbol in list(self.position_manager.positions.keys()):
            if symbol in current_prices:
                self.position_manager.close_position(
                    symbol=symbol,
                    exit_price=current_prices[symbol],
                    exit_time=final_timestamp,
                    exit_reason="回测结束强制平仓"
                )
    
    def _calculate_backtest_results(self) -> Dict[str, Any]:
        """计算回测结果"""
        trades = self.position_manager.closed_trades
        
        if not trades:
            return {'error': '无交易记录'}
        
        # 基础统计
        total_trades = len(trades)
        winning_trades = [t for t in trades if t['pnl'] > 0]
        losing_trades = [t for t in trades if t['pnl'] <= 0]
        
        win_rate = (len(winning_trades) / total_trades * 100) if total_trades > 0 else 0
        
        # 收益统计
        total_pnl = sum(t['pnl'] for t in trades)
        total_return_pct = (total_pnl / self.initial_capital * 100)
        
        # 按单位头寸计算总收益率
        total_return_by_unit = sum(t['pnl_pct'] for t in trades)
        
        # 平均收益
        avg_win = np.mean([t['pnl'] for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t['pnl'] for t in losing_trades]) if losing_trades else 0
        
        # 最大回撤（简化计算）
        equity_curve = [self.initial_capital]
        running_equity = self.initial_capital
        
        for trade in trades:
            running_equity += trade['pnl']
            equity_curve.append(running_equity)
        
        peak = equity_curve[0]
        max_drawdown = 0
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            if peak > 0:
                drawdown = (peak - equity) / peak * 100
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
        
        # 夏普比率
        returns = [t['pnl_pct'] / 100 for t in trades]
        if len(returns) > 1:
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = (mean_return / std_return * np.sqrt(252)) if std_return > 0 else 0
        else:
            sharpe_ratio = 0
        
        return {
            'total_trades': total_trades,
            'win_rate': round(win_rate, 2),
            'total_return': round(total_return_pct, 2),
            'total_return_by_unit': round(total_return_by_unit, 2),
            'total_pnl': round(total_pnl, 2),
            'avg_win': round(avg_win, 2),
            'avg_loss': round(avg_loss, 2),
            'max_drawdown': round(max_drawdown, 2),
            'sharpe_ratio': round(sharpe_ratio, 3),
            'final_equity': round(self.initial_capital + total_pnl, 2),
            'trades': trades
        }
