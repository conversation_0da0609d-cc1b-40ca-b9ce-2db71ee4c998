#!/usr/bin/env python3
"""
TBTrade集成测试脚本
测试Web界面与现有TBTrade系统的集成功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_integration():
    """测试集成功能"""
    print("🧪 开始TBTrade集成测试...")
    
    try:
        # 测试导入
        print("\n1. 测试模块导入...")
        from utils.tbtrade_integration import tbtrade_integration
        from utils.data_loader import get_available_symbols, load_market_data
        print("✅ 模块导入成功")
        
        # 测试系统状态
        print("\n2. 测试系统状态...")
        status = tbtrade_integration.get_system_status()
        print(f"数据库状态: {status['database_status']}")
        print(f"数据状态: {status['data_status']}")
        print(f"策略状态: {status['strategy_status']}")
        
        # 测试币种列表
        print("\n3. 测试币种列表...")
        symbols = get_available_symbols()
        print(f"可用币种数量: {len(symbols)}")
        print(f"前5个币种: {symbols[:5]}")
        
        # 测试数据加载
        print("\n4. 测试数据加载...")
        if symbols:
            test_symbol = symbols[0]
            print(f"测试加载 {test_symbol} 数据...")
            try:
                data = load_market_data(test_symbol, start_date='2024-01-01', end_date='2024-01-31')
                print(f"✅ 数据加载成功，共 {len(data)} 条记录")
                print(f"数据时间范围: {data.index[0]} 到 {data.index[-1]}")
            except Exception as e:
                print(f"⚠️ 数据加载失败，将使用模拟数据: {e}")
        
        # 测试回测配置
        print("\n5. 测试回测配置...")
        test_config = {
            'symbols': symbols[:2] if len(symbols) >= 2 else symbols,
            'initial_capital': 10000,
            'start_date': '2024-01-01',
            'end_date': '2024-01-31',
            'strategy_params': {
                'ema_short': 21,
                'ema_medium': 55,
                'ema_long': 200
            },
            'enable_partial_profit': True,
            'verbose_level': 1
        }
        
        print(f"回测配置: {test_config}")
        
        # 注意：这里不实际运行回测，因为可能需要较长时间
        print("⚠️ 跳过实际回测执行（避免长时间等待）")
        
        print("\n✅ 集成测试完成！")
        print("🚀 Web应用已准备就绪，可以启动Streamlit应用")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_streamlit_app():
    """测试Streamlit应用启动"""
    print("\n🌐 测试Streamlit应用...")
    
    try:
        import streamlit as st
        print("✅ Streamlit已安装")
        
        # 检查主应用文件
        app_file = Path(__file__).parent / "streamlit_app.py"
        if app_file.exists():
            print("✅ 主应用文件存在")
        else:
            print("❌ 主应用文件不存在")
        
        # 检查页面文件
        pages_dir = Path(__file__).parent / "pages"
        if pages_dir.exists():
            page_files = list(pages_dir.glob("*.py"))
            print(f"✅ 发现 {len(page_files)} 个页面文件")
        else:
            print("❌ 页面目录不存在")
        
        print("\n🚀 启动命令:")
        print("cd web")
        print("streamlit run streamlit_app.py")
        
    except ImportError:
        print("❌ Streamlit未安装，请运行: pip install streamlit")

if __name__ == "__main__":
    test_integration()
    test_streamlit_app()
    
    print("\n" + "="*50)
    print("🎯 下一步操作:")
    print("1. 确保已安装依赖: pip install -r requirements.txt")
    print("2. 启动Web应用: streamlit run streamlit_app.py")
    print("3. 在浏览器中访问: http://localhost:8501")
    print("="*50)
