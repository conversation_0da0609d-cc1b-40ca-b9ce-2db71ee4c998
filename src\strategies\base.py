from abc import ABC, abstractmethod
from pandas import DataFrame
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime
from enum import Enum

class SignalType(Enum):
    """交易信号类型"""
    NEUTRAL = 0
    BUY = 1
    SELL = -1
    STRONG_BUY = 2
    STRONG_SELL = -2

class BaseStrategy(ABC):
    """策略抽象基类 - 支持回测和实时交易"""
    
    def __init__(self, config: Dict[str, Any], params: Dict[str, Any] = None):
        """
        初始化策略
        :param config: 系统配置字典
        :param params: 策略参数字典
        """
        self.config = config
        self.params = params or self.get_default_parameters()
        self.name = self.__class__.__name__
        
        # 数据存储
        self.historical_data = pd.DataFrame()
        self.indicators = pd.DataFrame()
        self.signals = pd.DataFrame()
        
        # 交易配置
        self.trade_start = config.get('trade_hours', {}).get('start', '00:00')
        self.trade_end = config.get('trade_hours', {}).get('end', '23:59')
        
        # 回测相关
        self.backtest_results = {}
        self.positions = []
        self.trades = []
        
        self._validate_parameters()

    @abstractmethod
    def _validate_parameters(self):
        """验证策略参数有效性"""
        pass

    @classmethod
    @abstractmethod
    def get_default_parameters(cls) -> Dict[str, Any]:
        """获取策略默认参数"""
        pass

    @classmethod
    @abstractmethod
    def get_required_columns(cls) -> List[str]:
        """获取策略需要的数据列"""
        pass

    @abstractmethod
    def calculate_indicators(self, data: DataFrame) -> DataFrame:
        """
        计算技术指标
        :param data: 输入的K线数据
        :return: 包含技术指标的DataFrame
        """
        pass

    @abstractmethod
    def generate_signals(self, data: DataFrame) -> DataFrame:
        """
        生成交易信号
        :param data: 包含价格和指标数据的DataFrame
        :return: 包含信号的DataFrame
        """
        pass

    def load_data(self, data: DataFrame) -> None:
        """加载历史数据并计算指标"""
        self.historical_data = data.copy()
        self.indicators = self.calculate_indicators(data)
        self.signals = self.generate_signals(self.indicators)

    def get_signal(self, timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        获取指定时间点的交易信号
        :param timestamp: 时间戳，None表示最新信号
        :return: 信号字典
        """
        if self.signals.empty:
            return {'signal': SignalType.NEUTRAL, 'confidence': 0, 'reason': '无数据'}
        
        if timestamp is None:
            # 返回最新信号
            latest_signal = self.signals.iloc[-1]
        else:
            # 返回指定时间的信号
            mask = self.signals.index <= timestamp
            if not mask.any():
                return {'signal': SignalType.NEUTRAL, 'confidence': 0, 'reason': '时间超出范围'}
            latest_signal = self.signals[mask].iloc[-1]
        
        return {
            'signal': SignalType(latest_signal.get('signal', 0)),
            'confidence': latest_signal.get('confidence', 0),
            'reason': latest_signal.get('reason', ''),
            'timestamp': latest_signal.name if hasattr(latest_signal, 'name') else timestamp
        }

    def backtest(self, data: DataFrame, initial_capital: float = 10000, 
                 commission: float = 0.001) -> Dict[str, Any]:
        """
        执行策略回测
        :param data: 历史数据
        :param initial_capital: 初始资金
        :param commission: 手续费率
        :return: 回测结果
        """
        self.load_data(data)
        
        if self.signals.empty:
            return {'error': '无法生成交易信号'}
        
        # 初始化回测变量
        capital = float(initial_capital)
        position = 0.0  # 持仓数量
        position_value = 0.0  # 持仓价值
        entry_price = 0.0
        trades = []
        equity_curve = []
        
        # 风险管理参数
        max_position_pct = 0.95  # 最大持仓比例
        min_trade_amount = initial_capital * 0.01  # 最小交易金额
        max_single_loss = initial_capital * 0.1  # 单笔最大亏损
        
        for i, (timestamp, row) in enumerate(self.signals.iterrows()):
            try:
                current_price = float(data.loc[timestamp, 'close'])
                signal = SignalType(row.get('signal', 0))
                
                # 数值有效性检查
                if not np.isfinite(current_price) or current_price <= 0:
                    continue
                
                # 计算当前权益
                position_value = position * current_price if position > 0 else 0
                current_equity = capital + position_value
                
                # 数值范围检查
                if current_equity <= 0 or current_equity > initial_capital * 1000:
                    # 如果权益异常，重置为合理值
                    if current_equity > initial_capital * 1000:
                        current_equity = min(current_equity, initial_capital * 10)
                        capital = current_equity - position_value
                
                # 记录权益曲线
                equity_curve.append({
                    'timestamp': timestamp,
                    'equity': current_equity,
                    'price': current_price,
                    'position': position,
                    'capital': capital
                })
                
                # 处理交易信号
                if signal in [SignalType.BUY, SignalType.STRONG_BUY] and position == 0:
                    # 开多仓 - 改进的持仓计算
                    if capital > min_trade_amount:
                        # 计算可用资金（考虑手续费）
                        available_capital = capital * max_position_pct
                        trade_cost = available_capital * commission
                        net_capital = available_capital - trade_cost
                        
                        # 计算持仓数量
                        position = net_capital / current_price
                        entry_price = current_price
                        
                        # 更新资金
                        capital = capital - available_capital
                        
                        trades.append({
                            'timestamp': timestamp,
                            'type': 'BUY',
                            'price': current_price,
                            'quantity': position,
                            'cost': available_capital,
                            'signal_strength': signal.value
                        })
                    
                elif signal in [SignalType.SELL, SignalType.STRONG_SELL] and position > 0:
                    # 平仓 - 改进的收益计算
                    gross_proceeds = position * current_price
                    trade_cost = gross_proceeds * commission
                    net_proceeds = gross_proceeds - trade_cost
                    
                    # 计算收益
                    trade_capital = position * entry_price
                    pnl = net_proceeds - trade_capital
                    pnl_pct = (pnl / trade_capital * 100) if trade_capital > 0 else 0
                    
                    # 数值有效性检查
                    if not np.isfinite(pnl) or not np.isfinite(pnl_pct):
                        pnl = 0
                        pnl_pct = 0
                        net_proceeds = trade_capital
                    
                    # 限制单笔收益和亏损在合理范围内
                    max_single_gain = initial_capital * 5
                    if pnl > max_single_gain:
                        pnl = max_single_gain
                        net_proceeds = trade_capital + pnl
                    elif pnl < -max_single_loss:
                        pnl = -max_single_loss
                        net_proceeds = trade_capital + pnl
                    
                    # 限制收益率在合理范围内
                    pnl_pct = min(max(pnl_pct, -100), 500)
                    
                    # 更新资金
                    capital += net_proceeds
                    
                    trades.append({
                        'timestamp': timestamp,
                        'type': 'SELL',
                        'price': current_price,
                        'quantity': position,
                        'proceeds': net_proceeds,
                        'pnl': pnl,
                        'pnl_pct': pnl_pct,
                        'signal_strength': signal.value
                    })
                    
                    position = 0
                    entry_price = 0
                    
            except (KeyError, ValueError, TypeError) as e:
                # 跳过有问题的数据点
                continue
        
        # 如果最后还有持仓，按最后价格平仓
        if position > 0:
            try:
                final_price = float(data.iloc[-1]['close'])
                if np.isfinite(final_price) and final_price > 0:
                    gross_proceeds = position * final_price
                    trade_cost = gross_proceeds * commission
                    net_proceeds = gross_proceeds - trade_cost
                    
                    trade_capital = position * entry_price
                    pnl = net_proceeds - trade_capital
                    pnl_pct = (pnl / trade_capital * 100) if trade_capital > 0 else 0
                    
                    # 数值有效性检查
                    if not np.isfinite(pnl) or not np.isfinite(pnl_pct):
                        pnl = 0
                        pnl_pct = 0
                        net_proceeds = trade_capital
                    
                    # 限制单笔收益和亏损在合理范围内
                    max_single_gain = initial_capital * 5
                    if pnl > max_single_gain:
                        pnl = max_single_gain
                        net_proceeds = trade_capital + pnl
                    elif pnl < -max_single_loss:
                        pnl = -max_single_loss
                        net_proceeds = trade_capital + pnl
                    
                    # 限制收益率在合理范围内
                    pnl_pct = min(max(pnl_pct, -100), 500)
                    
                    capital += net_proceeds
                    
                    trades.append({
                        'timestamp': data.index[-1],
                        'type': 'SELL',
                        'price': final_price,
                        'quantity': position,
                        'proceeds': net_proceeds,
                        'pnl': pnl,
                        'pnl_pct': pnl_pct,
                        'signal_strength': 0
                    })
            except (IndexError, ValueError, TypeError):
                pass
        
        # 计算回测指标
        results = self._calculate_backtest_metrics(
            trades, equity_curve, initial_capital, data
        )
        
        self.backtest_results = results
        self.trades = trades
        
        return results

    def _calculate_backtest_metrics(self, trades: List[Dict], equity_curve: List[Dict], 
                                  initial_capital: float, data: DataFrame) -> Dict[str, Any]:
        """计算回测指标"""
        if not trades:
            return {'error': '无交易记录'}
        
        # 基础统计
        buy_trades = [t for t in trades if t['type'] == 'BUY']
        sell_trades = [t for t in trades if t['type'] == 'SELL' and 'pnl' in t]
        
        total_trades = len(sell_trades)
        if total_trades == 0:
            return {'error': '无完整交易'}
        
        # 收益统计 - 改进的计算方法
        total_pnl = sum(t['pnl'] for t in sell_trades)
        
        # 使用最终权益计算总收益率
        final_equity = equity_curve[-1]['equity'] if equity_curve else initial_capital
        total_return_pct = ((final_equity - initial_capital) / initial_capital * 100)
        
        # 数值范围检查和修正
        if not np.isfinite(total_return_pct) or abs(total_return_pct) > 10000:
            # 如果收益率异常，使用PnL计算
            total_return_pct = (total_pnl / initial_capital * 100)
            if not np.isfinite(total_return_pct) or abs(total_return_pct) > 1000:
                total_return_pct = min(max(total_return_pct, -100), 1000)
        
        winning_trades = [t for t in sell_trades if t['pnl'] > 0]
        losing_trades = [t for t in sell_trades if t['pnl'] <= 0]
        
        win_rate = (len(winning_trades) / total_trades * 100) if total_trades > 0 else 0
        avg_win = np.mean([t['pnl'] for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t['pnl'] for t in losing_trades]) if losing_trades else 0
        
        # 最大回撤 - 改进的计算方法
        if not equity_curve:
            max_drawdown = 0
        else:
            equity_values = [max(e['equity'], 0) for e in equity_curve]  # 确保非负
            peak = equity_values[0]
            max_drawdown = 0
            
            for equity in equity_values:
                if equity > peak:
                    peak = equity
                if peak > 0:  # 避免除零
                    drawdown = (peak - equity) / peak * 100
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown
        
        # 夏普比率 - 改进的计算方法
        if len(equity_curve) < 2:
            sharpe_ratio = 0
        else:
            returns = []
            for i in range(1, len(equity_curve)):
                prev_equity = max(equity_curve[i-1]['equity'], 1)  # 避免除零
                curr_equity = equity_curve[i]['equity']
                ret = (curr_equity - prev_equity) / prev_equity
                
                # 过滤异常收益率
                if np.isfinite(ret) and abs(ret) < 10:  # 限制单日收益率在1000%以内
                    returns.append(ret)
            
            if returns and len(returns) > 1:
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                if std_return > 0 and np.isfinite(mean_return) and np.isfinite(std_return):
                    sharpe_ratio = mean_return / std_return * np.sqrt(252)
                    # 限制夏普比率在合理范围内
                    sharpe_ratio = min(max(sharpe_ratio, -10), 10)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0
        
        # 盈亏比
        if avg_loss != 0:
            profit_factor = abs(avg_win / avg_loss)
            profit_factor = min(profit_factor, 100)  # 限制最大盈亏比
        else:
            profit_factor = float('inf') if avg_win > 0 else 0
        
        return {
            'total_trades': total_trades,
            'win_rate': round(win_rate, 2),
            'total_return_pct': round(total_return_pct, 2),
            'total_pnl': round(total_pnl, 2),
            'avg_win': round(avg_win, 2),
            'avg_loss': round(avg_loss, 2),
            'max_drawdown': round(max_drawdown, 2),
            'sharpe_ratio': round(sharpe_ratio, 3),
            'profit_factor': round(profit_factor, 2) if np.isfinite(profit_factor) else 0,
            'equity_curve': equity_curve,
            'trades': sell_trades,
            'final_equity': round(final_equity, 2) if equity_curve else initial_capital
        }

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'parameters': self.params,
            'required_columns': self.get_required_columns(),
            'description': self.__doc__ or '无描述'
        }

    def __str__(self) -> str:
        return f"{self.name}({self.params})"

    def __repr__(self) -> str:
        return self.__str__() 