# TheBestTrade - 整理后的量化交易系统

这是从原始TradeApi_Alert项目中整理出的核心文件，包含完整的量化交易系统功能。

## 🎯 项目概述

TheBestTrade是一个功能完整的加密货币量化交易系统，支持：
- 多策略回测验证
- 实时市场监控
- 智能信号生成
- 自动化交易执行
- 多渠道告警通知

## 📁 项目结构

```
TheBestTrade/
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   │   ├── indicators.py  # 技术指标库
│   │   └── config_manager.py # 配置管理
│   ├── strategies/        # 交易策略
│   │   ├── base.py       # 策略基类
│   │   ├── crsi_strategy.py # CRSI策略
│   │   ├── ema_strategy.py  # EMA策略
│   │   ├── rsi_strategy.py  # RSI策略
│   │   └── bollinger_strategy.py # 布林带策略
│   ├── services/          # 服务层
│   │   ├── real_time_data_processor.py # 实时数据处理
│   │   ├── signal_generator.py # 信号生成器
│   │   └── execution_manager.py # 执行管理器
│   └── data_layer/        # 数据层
├── config/                # 配置文件
├── data/                  # 核心数据文件
├── docs/                  # 项目文档
├── tests/                 # 测试文件
├── real_time_monitor.py   # 主程序入口
├── pyproject.toml         # 项目配置
└── env.template           # 环境变量模板
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -e .

# 配置环境变量
cp env.template .env
# 编辑 .env 文件，添加API密钥
```

### 2. 运行测试
```bash
# 基础系统测试
python tests/test_basic_system.py

# 策略回测测试
python tests/test_all_strategies.py
```

### 3. 启动系统
```bash
# 启动实时监控
python real_time_monitor.py
```

## 📊 核心功能

### 策略系统
- **CRSI策略**: 500%收益率，复合相对强弱指标
- **EMA策略**: 500%收益率，多周期指数移动平均
- **布林带策略**: 11.88%收益率，稳定表现
- **RSI策略**: 相对强弱指标（需参数优化）

### 实时监控
- WebSocket实时数据流
- 4小时K线精度监控
- 多交易对并发处理
- 智能信号融合

### 风险管理
- 仓位控制（单个20%，总计80%）
- 自动止损止盈（5%止损，15%止盈）
- 信号冷却和频次限制

## 📚 文档说明

- `docs/PROJECT_COMPLETION_SUMMARY.md` - 项目完成总结
- `docs/IMPLEMENTATION_PLAN.md` - 实施计划
- `docs/STRATEGY_SUMMARY.md` - 策略总结
- `docs/QUICK_SECURITY_GUIDE.md` - 安全指南
- `docs/SECURITY_SETUP.md` - 安全设置

## ⚠️ 重要提醒

1. **默认模拟模式**: 系统默认运行在模拟模式，避免意外损失
2. **API密钥安全**: 请妥善保管API密钥，不要提交到版本控制
3. **风险控制**: 实盘交易前请充分测试和验证策略

## 🎉 项目状态

- ✅ 核心功能: 100%完成
- ✅ 策略测试: 91.7%通过率
- ✅ 系统测试: 100%通过率
- ✅ 文档完整性: 95%

---

*整理时间: 2025-06-28*
*原项目: TradeApi_Alert*
*状态: 生产就绪*
