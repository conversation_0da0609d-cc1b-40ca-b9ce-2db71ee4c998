import time
from datetime import datetime, timedelta, timezone
import requests
import hashlib
import hmac
import json


# 填入您的API密钥和密钥
API_KEY = 'dKF4ICe1v2nb9vNzbwLMkaHTuBEGdyq2cuoh5Va9C2yKXsbwpKhmfVjfpcSBJrb2'
API_SECRET = 'T3lW86xqHkAMUsmKqZwoDV3dGhks9M9MHNUhLdPlp9kwnTN8JhDlgaQ7wZPYUquu'

# API基本URL
BASE_URL = 'https://fapi.binance.com'

trade_url = 'https://fapi.binance.com/fapi/v1/order'
leverage_url = 'https://fapi.binance.com/fapi/v1/leverage'
marginType_url = 'https://fapi.binance.com/fapi/v1/marginType'
account_url = 'https://fapi.binance.com/fapi/v2/account'
openoder_url = 'https://fapi.binance.com/fapi/v1/openOrders'
perpkline_url = "https://fapi.binance.com/fapi/v1/klines"  # 永续合约K线数据api
delallorders_url = "https://fapi.binance.com/fapi/v1/allOpenOrders"
delorder_url = "https://fapi.binance.com/fapi/v1/order"
exchginfo_url = 'https://fapi.binance.com/fapi/v1/exchangeInfo'

# 设置代理
proxies = {
    "http": "http://127.0.0.1:7890",
    "https": "https://127.0.0.1:7890",
}

ret_str = ""

def pretty_print_json(json_context):
    try:
        # 将JSON字符串解析为Python对象
        if type(json_context) is dict or type(json_context) is list:
            json_obj = json_context
        elif type(json_context) is str:
            json_obj = json.loads(json_context)

        # 将Python对象转换为格式化的JSON字符串
        pretty_json_str = json.dumps(json_obj, indent=4)

        print(pretty_json_str)

    except json.JSONDecodeError as e:
        print("Failed to parse JSON:", e)


# 创建签名
def create_signature(query_string):
    return hmac.new(API_SECRET.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()

def create_market_order(symbol, side, quantity):
    global ret_str
    try:
        timestamp = int(time.time() * 1000)
        
        query_string = (
            f'symbol={symbol}&side={side}&positionSide=BOTH&quantity={quantity}&'
            f'type=MARKET&timestamp={timestamp}'
        )
        signature = create_signature(query_string)
        
        params = {
            'symbol': symbol,
            'side': side,
            "positionSide": "BOTH",
            'quantity': quantity,
            'type': 'MARKET',
            'timestamp': timestamp,
            'signature': signature
        }
        
        response = requests.post(trade_url, params=params, headers={'X-MBX-APIKEY': API_KEY},proxies=proxies, timeout=5)
        # if side == 'SELL': #解决在持仓被止损时，会有该symbol的止盈订单还存在的问题
        #     delete_allorders(symbol)
        ret_str = ret_str + "create_market_order: "+side+" "+ str(quantity) +" "+str(symbol)+"success"+"\n"
        return response.json()
    except requests.exceptions.RequestException as e:
        print("create_market_order Request exception:", e)
        ret_str = ret_str + "create_market_order: "+side+" "+ str(quantity) +" "+str(symbol)+"fail"+"\n"


def create_take_profit_market_order(symbol, quantity,price):
    global ret_str
    try:
        timestamp = int(time.time() * 1000)
        
        side = 'SELL'#'BUY'
        type = 'TAKE_PROFIT_MARKET'
        # closePosition = 'false' #全部售出
        query_string = (
            f'symbol={symbol}&side={side}&quantity={quantity}&'
            f'type={type}&stopprice={price}&timestamp={timestamp}'
        )
        signature = create_signature(query_string)
        
        params = {
            'symbol': symbol,
            'side': side,
            'quantity': quantity,
            # 'closePosition':closePosition,
            'type': type,
            'stopprice': price,
            'timestamp': timestamp,
            'signature': signature
        }
        
        response = requests.post(trade_url, params=params, headers={'X-MBX-APIKEY': API_KEY},proxies=proxies, timeout=5)
        ret_str += "create_take_profit_market_order "+ symbol +" "+side+" "+str(quantity)+" at "+str(price)+"success"+"\n"
        return response.json()
    except requests.exceptions.RequestException as e:
        print("create_take_profit_market_order Request exception:", e)
        ret_str += "create_take_profit_market_order "+ symbol +" "+side+" "+str(quantity)+" at "+str(price)+"fail"+"\n"


def create_stop_market_order(symbol, price):
    global ret_str
    try:
        timestamp = int(time.time() * 1000)
        
        side = 'SELL'
        type = 'STOP_MARKET'
        closePosition = 'true' #全部售出
        query_string = (
            f'symbol={symbol}&side={side}&closePosition={closePosition}&'
            f'type={type}&stopprice={price}&timestamp={timestamp}'
        )
        signature = create_signature(query_string)
        
        params = {
            'symbol': symbol,
            'side': side,
            'closePosition':closePosition,
            'type': type,
            'stopprice': price,
            'timestamp': timestamp,
            'signature': signature
        }
        
        response = requests.post(trade_url, params=params, headers={'X-MBX-APIKEY': API_KEY},proxies=proxies, timeout=5)
        ret_str += "create_stop_market_order "+str(symbol)+" at "+str(price)+"success"+"\n"
        return response.json()
    except requests.exceptions.RequestException as e:
        print("create_stop_market_order Request exception:", e)
        ret_str += "create_stop_market_order "+str(symbol)+" at "+str(price)+"fail"+"\n"

def delete_order(symbol,orderId):
    global ret_str
    try:
        timestamp = int(time.time() * 1000)
        
        query_string = (
            f'symbol={symbol}&orderId={orderId}'
            f'&timestamp={timestamp}'
        )
        signature = create_signature(query_string)
        
        params = {
            'symbol': symbol,
            'orderId': orderId,
            'timestamp': timestamp,
            'signature': signature
        }
        
        response = requests.delete(delorder_url, params=params, headers={'X-MBX-APIKEY': API_KEY},proxies=proxies, timeout=5)
        print(response.json())
        ret_str += "delete_order "+str(symbol)+" success"+"\n"

        return response.json()
    except requests.exceptions.RequestException as e:
        ret_str += "delete_order "+str(symbol)+" fail"+"\n"
        print("delete_order Request exception:", e)


def delete_allorders(symbol):
    try:
        timestamp = int(time.time() * 1000)
        
        query_string = (
            f'symbol={symbol}'
            f'&timestamp={timestamp}'
        )
        signature = create_signature(query_string)
        
        params = {
            'symbol': symbol,
            'timestamp': timestamp,
            'signature': signature
        }
        
        response = requests.delete(delallorders_url, params=params, headers={'X-MBX-APIKEY': API_KEY},proxies=proxies, timeout=5)
        print(response.json())
        return response.json()
    except requests.exceptions.RequestException as e:
        print("delete_allorders Request exception:", e)
        
        
    

def change_leverage(symbol, leverage):
    try:
        timestamp = int(time.time() * 1000)
        
        query_string = (
            f'symbol={symbol}&leverage={leverage}'
            f'&timestamp={timestamp}'
        )
        signature = create_signature(query_string)
        
        params = {
            'symbol': symbol,
            'leverage': leverage,
            'timestamp': timestamp,
            'signature': signature
        }
        
        response = requests.post(leverage_url, params=params, headers={'X-MBX-APIKEY': API_KEY},proxies=proxies, timeout=5)
        return response.json()
    except requests.exceptions.RequestException as e:
        print("change_leverage Request exception:", e)
        
def change_marginType(symbol, marginType):
    try:
        timestamp = int(time.time() * 1000)
        
        query_string = (
            f'symbol={symbol}&marginType={marginType}'
            f'&timestamp={timestamp}'
        )
        signature = create_signature(query_string)
        
        params = {
            'symbol': symbol,
            'marginType': marginType,
            'timestamp': timestamp,
            'signature': signature
        }
        
        response = requests.post(marginType_url, params=params, headers={'X-MBX-APIKEY': API_KEY},proxies=proxies, timeout=5)
        return response.json()
    except requests.exceptions.RequestException as e:
        print("change_marginType Request exception:", e)
        
# 获取账户信息
def get_account():
    try:
        timestamp = int(time.time() * 1000)

        query_string = (
            f'timestamp={timestamp}'
        )
        signature = create_signature(query_string)
        
        params = {
            "timestamp": timestamp,
            "signature":signature
        }

        response = requests.get(account_url, params=params, headers={"X-MBX-APIKEY": API_KEY},proxies=proxies, timeout=5)
        return response.json()
    
    except requests.exceptions.RequestException as e:
        print("get_account Request exception:", e)
        
# 获取当前持仓和挂单
def get_positions_and_orders():
    global ret_str
    positions = []
    orders = []
    try:
        get_account_response = get_account()
        for position in get_account_response['positions']:
            if float(position['positionAmt']) > 0:
                positions.append(position)
                print('position:', position)
                

                timestamp = int(time.time() * 1000)
                symbol = position['symbol']
                query_string = (
                    f'symbol={symbol}&'
                    f'timestamp={timestamp}'
                )
                signature = create_signature(query_string)
                
                params = {
                    "symbol":symbol,
                    "timestamp": timestamp,
                    "signature":signature
                }
                
                open_orders = requests.get(openoder_url, params=params, headers={"X-MBX-APIKEY": API_KEY},proxies=proxies, timeout=5)
                orders.append(open_orders.json())
                print('open_orders:', open_orders.json())
                
        return positions, orders
    
    except requests.exceptions.RequestException as e:
        print("get_positions_and_orders exception:", e)
        ret_str += "get_positions_and_orders fail"+"\n"
        return None, None


"""
[
    [
        1499040000000,      // k线开盘时间
        "0.01634790",       // 开盘价
        "0.80000000",       // 最高价
        "0.01575800",       // 最低价
        "0.01577100",       // 收盘价(当前K线未结束的即为最新价)
        "148976.11427815",  // 成交量
        1499644799999,      // k线收盘时间
        "2434.19055334",    // 成交额
        308,                // 成交笔数
        "1756.87402397",    // 主动买入成交量
        "28.46694368",      // 主动买入成交额
        "17928899.62484339" // 请忽略该参数
    ]
]
"""

def get_klines_info(symbol, start_time, end_time, interval):
    try:
        start_time = int(start_time.timestamp() * 1000)
        end_time = int(end_time.timestamp() * 1000)
        time_start = time.time()
        response = requests.get(perpkline_url, params={
            "symbol": symbol,
            "interval": interval, "startTime": start_time, "endTime": end_time
            # ,"apikey": API_KEY
        }, proxies=proxies, timeout=5)
        time_end = time.time()

        data = response.json()
        return data

    except Exception as e:
        global g_unfinished_token
        g_unfinished_token.append(symbol)
        print("get_binance_"+symbol+"_kinfo Error:", e)
        return None
"""
"positions": [  // 头寸，将返回所有市场symbol。
        //根据用户持仓模式展示持仓方向，即单向模式下只返回BOTH持仓情况，双向模式下只返回 LONG 和 SHORT 持仓情况
        {
            "symbol": "BTCUSDT",  // 交易对
            "initialMargin": "0",   // 当前所需起始保证金(基于最新标记价格)
            "maintMargin": "0", //维持保证金
            "unrealizedProfit": "0.00000000",  // 持仓未实现盈亏
            "positionInitialMargin": "0",  // 持仓所需起始保证金(基于最新标记价格)
            "openOrderInitialMargin": "0",  // 当前挂单所需起始保证金(基于最新标记价格)
            "leverage": "100",  // 杠杆倍率
            "isolated": true,  // 是否是逐仓模式
            "entryPrice": "0.00000",  // 持仓成本价
            "maxNotional": "250000",  // 当前杠杆下用户可用的最大名义价值
            "bidNotional": "0",  // 买单净值，忽略
            "askNotional": "0",  // 买单净值，忽略
            "positionSide": "BOTH",  // 持仓方向
            "positionAmt": "0",      // 持仓数量
            "updateTime": 0         // 更新时间 
        }
    ]
}
"""
#TODO:止损价的变化应该是修改订单而不是重新下单（1.时间会变化 2.前一个单没有取消）

#持仓追踪功能
def func_trace_positions():
    #1.判断当前持仓的这段时间最高价格是否满足了2：1盈亏比，如果是则将止损价提到开仓价
    #2.10%止盈一半，将止损价提高到2：1处，剩余的一半则按照移动止盈（即：）
    #3.如果24h过去还在区间中则市价平掉（即：没有触发止损的仓位和没有2：1上方区间的仓位）
    global ret_str
    ret_str = ""
    try:
        positions, orders = get_positions_and_orders()
        if positions is None or orders is None:
            positions, orders = get_positions_and_orders()
        if (positions and orders) is not None:
            if len(positions) > 0:
                
                trace_list = []
                file_path = "./trade_data/tracelist.json"
                with open(file_path, "r") as file:
                    try:
                        trace_list = json.load(file)
                    except json.JSONDecodeError:
                        print(f"Error reading JSON from file: {file_path}")                      
                
                for position in positions:
                    timestamp = datetime.now().timestamp() #position['updateTime']
                    symbol = position['symbol']
                    entryPrice = float(position['entryPrice'])
                    positionAmt = float(position['positionAmt'])
                    initialMargin = float(position['initialMargin'])
                    stopPrice = 0.0
                    flag_trm = False
                    flag_sm = False     
                    
                    if initialMargin >= 700:#340: #只对程式化的仓位进行操作
                        continue
                                        
                    if len(trace_list) > 0:
                        for key in trace_list: 
                            if key['symbol'] == symbol:
                                timestamp = key['start_time']
                                
                    symbol_info = get_symbol_info(symbol)
                    if len(symbol_info) < 0:
                        symbol_info = get_symbol_info(symbol)
                        
                    if len(orders) > 0:
                        for order in orders:
                            for ord in order:
                                if ord['symbol'] == symbol:
                                    
                                    if ord['type'] == 'TAKE_PROFIT_MARKET': 
                                        flag_trm = True
                                    if ord['type'] == 'STOP_MARKET':
                                        flag_sm = True
                                        stopPrice = float(ord['stopPrice'])
                                        sl_value = entryPrice-stopPrice #止损差值
                                        
                                        start_time = datetime.fromtimestamp(timestamp)
                                        end_time = datetime.now()
                                        
                                        # #持仓大于24h,且持仓没有更新的，终止持仓
                                        # tmp_start_time = timestamp
                                        # tmp_end_time = int(end_time.timestamp())
                                        # print("tmp_end_time:", tmp_end_time, "tmp_start_time:", tmp_start_time)
                                        # print("tmp_end_time-tmp_start_time:", tmp_end_time-tmp_start_time)
                                        # if abs((tmp_end_time-tmp_start_time)) >= 86400:
                                        #     side = 'SELL'
                                        #     quantity = positionAmt
                                        #     create_market_order(symbol, side, quantity)
                                            # continue
                                            
                                        all_kinfos = get_klines_info(symbol, start_time, end_time, '1h')
                                        print('all_kinfos:', all_kinfos)
                                        
                                        high_price_max = 0.0
                                        for kinfo in all_kinfos[1:]: #滤掉第一根
                                            if float(kinfo[2]) > high_price_max:
                                                high_price_max = float(kinfo[2])
                                                
                                        new_stop_price = 0.0
                                        cur_clsprice = float(all_kinfos[-1][4])
                                        
                                        if stopPrice >= entryPrice:
                                            if high_price_max >= entryPrice*1.1:
                                                per = (high_price_max-entryPrice)/entryPrice
                                                new_stop_price = entryPrice*(1+0.618*per)
                                                
                                                if new_stop_price>= cur_clsprice: #快速冲高回落，得甩仓了
                                                    side = 'SELL'
                                                    quantity = positionAmt
                                                    create_market_order(symbol, side, quantity)
                                                    continue
                                            
                                            elif high_price_max >= entryPrice*1.08:
                                                new_stop_price = entryPrice*1.03

                                        else:
                                            if high_price_max >= sl_value*2+entryPrice:
                                                new_stop_price = 1.005*entryPrice
                                        
                                        
                                        # #判断当前持仓的这段时间最高价格是否满足了2：1盈亏比，如果是则将止损价提到开仓价    
                                        # if high_price_max >= entryPrice*1.1:
                                        #     per = (high_price_max-entryPrice)/entryPrice
                                        #     new_stop_price = entryPrice*(1+0.618*per)
                                            
                                        #     if new_stop_price>= cur_clsprice: #快速冲高回落，得甩仓了
                                        #         side = 'SELL'
                                        #         quantity = positionAmt
                                        #         create_market_order(symbol, side, quantity)
                                        #         continue
                                            
                                        # elif high_price_max >= entryPrice*1.08:
                                        #     new_stop_price = entryPrice*1.03
                                            
                                        # elif high_price_max >= entryPrice*1.06:
                                        #     new_stop_price = entryPrice*1.005
                                        print("high_price_max:", high_price_max, "new_stop_price:", new_stop_price)
                                        
                                        if new_stop_price < cur_clsprice: #满足条件有新的止损价，因为是做多所以新的止损价需要大于原来的止损价
                                            new_stop_price = round(new_stop_price, symbol_info['pricePrecision'])
                                            if new_stop_price > stopPrice: #需要先调到合适的精度后再作比较
                                                delete_order(symbol, ord['orderId']) #先撤销开始的止损单，再重新设置
                                                time.sleep(2)
                                                create_stop_market_order(symbol, new_stop_price)
                                                continue
                                    
                    #没有设置止损则默认设置3%
                    if flag_sm == False:
                        tp_price = entryPrice * 0.97
                        print('test1')
                        tp_price = round(tp_price, symbol_info['pricePrecision'])  
                        response = create_stop_market_order(symbol, tp_price)
                        print(response)
                        
                    if (flag_trm == False) and (stopPrice > 0 and  stopPrice < entryPrice):#滤除止盈全部打了后，再设置的情况
                        quantity = 0.0
                        tp_price = 0.0
                        print('test2')

                        #区间上沿平仓15%, 0.9%
                        quantity = positionAmt*0.15 #如果quantity过小的话，会有sell all的问题
                        quantity = round(quantity, symbol_info['quantityPrecision'])
                        tp_price = entryPrice+2*sl_value
                        tp_price = round(tp_price, symbol_info['pricePrecision']) 
                        response1 = create_take_profit_market_order(symbol, quantity, tp_price)
                        print(response1)
                        time.sleep(2)
                        #8%再平仓15%, 1.2%
                        quantity = positionAmt*0.15 #如果quantity过小的话，会有sell all的问题
                        quantity = round(quantity, symbol_info['quantityPrecision'])
                        tp_price = entryPrice*1.08
                        tp_price = round(tp_price, symbol_info['pricePrecision']) 
                        response2 = create_take_profit_market_order(symbol, quantity, tp_price)
                        print(response2)
                        time.sleep(2)
                        #10%再平仓20%,2%
                        quantity = positionAmt*0.2 #如果quantity过小的话，会有sell all的问题
                        quantity = round(quantity, symbol_info['quantityPrecision'])
                        tp_price = entryPrice*1.1
                        tp_price = round(tp_price, symbol_info['pricePrecision']) 
                        response3 = create_take_profit_market_order(symbol, quantity, tp_price)
                        print(response3)
                        time.sleep(2)
                        
            return ret_str,True
    except Exception as e:
        print("Error:", e)
        return ret_str,False


def calc_need_len_after_point(num):
    need_len = 0
    price_str = str(num)    
    pos = price_str.find('.')
    
    if pos != -1:
        # 计算小数点后的位数
        decimal_places = len(price_str) - pos - 1
        need_len = decimal_places
    return need_len

def get_symbol_info(symbol):
    try:
        response = requests.get(exchginfo_url, proxies=proxies, timeout=5)
        symbol_info = response.json()
        for symbol_data in symbol_info["symbols"]:
            if symbol_data["symbol"] == symbol:
                return symbol_data
        return None

    except requests.exceptions.RequestException as e:
        print("Request exception:", e)
        return None

def func_trade_UM(symbol, cur_openprice, cur_closeprice):
    #止损百分比最大不超过3%，如果此根k涨幅小于3%，则以openprice作为止损价。
    tp_price = 0.0
    increase = (float)((cur_closeprice-cur_openprice)/cur_openprice)
    if increase > 0.03:
        tp_price = 0.97*cur_closeprice
    else:
        tp_price = cur_openprice
    
    need_len = calc_need_len_after_point(cur_closeprice)
    symbol_info = get_symbol_info(symbol)
    if len(symbol_info) < 0:
        symbol_info = get_symbol_info(symbol)
    
    tp_price = round(tp_price, symbol_info['pricePrecision'])
    side = 'BUY'
    usdt_num = 70#140
    leverage = 20#10

    print("symbol:", symbol)
    change_leverage_response = change_leverage(symbol, int(leverage))
    if 'code' in change_leverage_response:
        if change_leverage_response['code'] == -4028:
            leverage = leverage/2
            usdt_num= 2*usdt_num
            change_leverage_response = change_leverage(symbol, int(leverage))
            if 'code' in change_leverage_response:
                if change_leverage_response['code'] == -4028:
                    leverage = leverage/2
                    usdt_num= 2*usdt_num
                    change_leverage_response = change_leverage(symbol, int(leverage))
            
    marginType = 'ISOLATED'
    change_marginType_response = change_marginType(symbol, marginType)
    print("change_leverage_response:", change_leverage_response)
    print("change_marginType_response:", change_marginType_response)
    if cur_closeprice <= cur_openprice:
        return ""
    ret_str = ""
    if change_leverage_response['leverage'] == leverage and (change_marginType_response['msg'] == "success" or change_marginType_response['code'] == -4046):
        # 创建市价订单
        quantity = float(usdt_num*leverage / cur_closeprice)
        quantity = round(quantity, symbol_info['quantityPrecision'])
        market_order_result = create_market_order(symbol, side, quantity)
        print("market_order_result:", market_order_result)
        print(tp_price, leverage, quantity)
        if 'code' not in market_order_result:
            # 调用创建卖出订单函数，设置止盈价格和数量
            stop_market_result = create_stop_market_order(symbol, tp_price)
            print("stop_market_result:", stop_market_result)
            if 'code' not in stop_market_result:
                tmp_increase = str(100*round((float)((tp_price-cur_closeprice)/tp_price),2))+"%"
                ret_str = str(leverage)+"x ISOLATED "+symbol+" postion:"+str(usdt_num*leverage)+" SL price:"+str(tp_price)+" increase:"+tmp_increase+"\n"
            else:
                ret_str = str(leverage)+"x ISOLATED "+symbol+" postion:"+str(usdt_num*leverage)+"don`t set SL price,please check it!!!"+"\n"
        else:
            ret_str = symbol + ' '+str(market_order_result['code'])+' '+str(market_order_result['msg'])+"\n"
            
            # time.sleep(2) #怀疑时设置太快，没反应过来有这个symbol
            # tp_price = 1.1*cur_closeprice
            # tp_price = round(tp_price, symbol_info['pricePrecision'])
            # quantity = quantity/2
            # create_take_profit_market_order_response = create_take_profit_market_order(symbol, quantity, tp_price)
            # print('create_take_profit_market_order_response:', create_take_profit_market_order_response)
            
    return ret_str

if __name__ == '__main__':
    # market_order_result = create_market_order('BTCUSDT', 'BUY', 0.001)
    # print("market_order_result:", market_order_result)
    # response = create_stop_market_order('BTCUSDT', 25000)
    # print(response)
    
    # response = create_take_profit_market_order('BTCUSDT', 0.001, 31000)
    # print(response)
    # get_positions_and_orders()
    # func_trace_positions()
    # delete_order('BTCUSDT', 182242601841)
    
    # change_leverage_response = change_leverage('LPTUSDT', 20)
    # usdt_num = 70
    # leverage = 20
    # symbol = 'LPTUSDT'
    # print("symbol:", symbol)
    # change_leverage_response = change_leverage(symbol, int(leverage))
    # print("change_leverage_response:", change_leverage_response)

    # if change_leverage_response['code'] == -4028:
    #     leverage = leverage/2
    #     usdt_num= 2*usdt_num
    #     print(leverage, type(leverage), usdt_num, type(usdt_num))
    #     change_leverage_response = change_leverage(symbol, int(leverage))
    #     print("change_leverage_response:", change_leverage_response)

    get_account_response = get_account()
    # pretty_print_json(get_account_response)
    for position in get_account_response['positions']:
        if position['symbol'] == '1000SHIBUSDT':
            pretty_print_json(position)
    # print(get_account_response)
    pass