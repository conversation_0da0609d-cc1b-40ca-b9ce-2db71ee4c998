#!/usr/bin/env python3
"""
数据管理工具
Data Manager

提供数据查询、统计、验证和维护的高级功能
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import json
from pathlib import Path

from ..logger_config import get_logger
from .historical_data_fetcher import HistoricalDataFetcher
from .data_storage import DataStorage

logger = get_logger(__name__)


class DataManager:
    """数据管理器"""
    
    def __init__(self, storage: Optional[DataStorage] = None, 
                 fetcher: Optional[HistoricalDataFetcher] = None):
        """
        初始化数据管理器
        
        Args:
            storage: 数据存储实例
            fetcher: 数据获取器实例
        """
        self.storage = storage or DataStorage()
        self.fetcher = fetcher or HistoricalDataFetcher()
        
        logger.info("数据管理器初始化完成")
    
    def sync_symbol_data(self, symbol: str, interval: str = "4h", 
                        days_back: int = 30, force_full_sync: bool = False) -> Dict[str, Any]:
        """
        同步指定交易对的数据（增量或全量）
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            days_back: 向前同步的天数
            force_full_sync: 强制全量同步
            
        Returns:
            Dict[str, Any]: 同步结果统计
        """
        logger.info(f"开始同步 {symbol} ({interval}) 数据，回溯 {days_back} 天")
        
        result = {
            'symbol': symbol,
            'interval': interval,
            'success': False,
            'new_records': 0,
            'total_records': 0,
            'sync_type': 'full' if force_full_sync else 'incremental',
            'time_range': None,
            'errors': []
        }
        
        try:
            end_time = datetime.now()
            
            if force_full_sync:
                # 全量同步
                start_time = end_time - timedelta(days=days_back)
                logger.info(f"执行全量同步: {start_time.date()} - {end_time.date()}")
            else:
                # 增量同步：从最新数据时间点开始
                latest_time = self.storage.get_latest_timestamp(symbol, interval)
                if latest_time:
                    # 从最新时间开始，稍微重叠避免数据丢失
                    start_time = latest_time - timedelta(hours=4)  # 重叠4小时
                    logger.info(f"执行增量同步: 从 {latest_time.date()} 开始")
                else:
                    # 没有历史数据，执行全量同步
                    start_time = end_time - timedelta(days=days_back)
                    result['sync_type'] = 'full'
                    logger.info(f"无历史数据，执行全量同步: {start_time.date()} - {end_time.date()}")
            
            # 获取数据
            df = self.fetcher.fetch_klines_range(symbol, start_time, end_time, interval)
            
            if df is not None and not df.empty:
                # 保存数据
                new_records = self.storage.save_klines_dataframe(df, symbol, interval)
                
                # 获取总记录数
                total_df = self.storage.get_klines(symbol, interval)
                total_records = len(total_df)
                
                result.update({
                    'success': True,
                    'new_records': new_records,
                    'total_records': total_records,
                    'time_range': (start_time, end_time)
                })
                
                logger.info(f"✅ {symbol} 同步完成: 新增 {new_records} 条，总计 {total_records} 条")
            else:
                result['errors'].append("未获取到数据")
                logger.warning(f"❌ {symbol} 同步失败: 未获取到数据")
        
        except Exception as e:
            result['errors'].append(str(e))
            logger.error(f"❌ {symbol} 同步异常: {e}")
        
        return result
    
    def batch_sync_symbols(self, symbols: List[str], interval: str = "4h", 
                          days_back: int = 30) -> Dict[str, Any]:
        """
        批量同步多个交易对的数据
        
        Args:
            symbols: 交易对列表
            interval: 时间间隔
            days_back: 向前同步的天数
            
        Returns:
            Dict[str, Any]: 批量同步结果
        """
        logger.info(f"开始批量同步 {len(symbols)} 个交易对")
        
        results = {
            'total_symbols': len(symbols),
            'successful': 0,
            'failed': 0,
            'total_new_records': 0,
            'symbol_results': {},
            'start_time': datetime.now()
        }
        
        for i, symbol in enumerate(symbols, 1):
            logger.info(f"进度 {i}/{len(symbols)}: 同步 {symbol}")
            
            result = self.sync_symbol_data(symbol, interval, days_back)
            results['symbol_results'][symbol] = result
            
            if result['success']:
                results['successful'] += 1
                results['total_new_records'] += result['new_records']
            else:
                results['failed'] += 1
            
            # 进度报告
            if i % 10 == 0:
                logger.info(f"批量同步进度: {i}/{len(symbols)}, 成功: {results['successful']}, 失败: {results['failed']}")
        
        results['end_time'] = datetime.now()
        results['duration'] = results['end_time'] - results['start_time']
        
        logger.info(f"✅ 批量同步完成: 成功 {results['successful']}, 失败 {results['failed']}, "
                   f"新增 {results['total_new_records']} 条记录, 耗时 {results['duration']}")
        
        return results
    
    def get_data_summary(self, symbol: str = None, interval: str = None) -> pd.DataFrame:
        """
        获取数据概览
        
        Args:
            symbol: 交易对符号（可选）
            interval: 时间间隔（可选）
            
        Returns:
            pd.DataFrame: 数据概览
        """
        try:
            metadata_df = self.storage.get_metadata(symbol, interval)
            
            if metadata_df.empty:
                logger.warning("没有找到数据")
                return pd.DataFrame()
            
            # 添加数据质量评分
            summary_data = []
            for _, row in metadata_df.iterrows():
                sym = row['symbol']
                intv = row['interval']
                
                # 获取数据范围
                data_range = self.storage.get_data_range(sym, intv)
                
                summary_item = {
                    'symbol': sym,
                    'interval': intv,
                    'total_records': row['total_records'],
                    'first_time': row['first_time'],
                    'last_time': row['last_time'],
                    'last_update': row['last_update']
                }
                
                if data_range:
                    # 计算数据完整性
                    days_span = (data_range[1] - data_range[0]).days
                    expected_records = self._calculate_expected_records(days_span, intv)
                    completeness = min(row['total_records'] / expected_records * 100, 100) if expected_records > 0 else 0
                    
                    summary_item.update({
                        'days_span': days_span,
                        'completeness_%': round(completeness, 2)
                    })
                
                summary_data.append(summary_item)
            
            summary_df = pd.DataFrame(summary_data)
            return summary_df
            
        except Exception as e:
            logger.error(f"获取数据概览失败: {e}")
            return pd.DataFrame()
    
    def _calculate_expected_records(self, days: int, interval: str) -> int:
        """计算期望的记录数"""
        interval_map = {
            '1m': 1440,    # 24*60
            '5m': 288,     # 24*12
            '15m': 96,     # 24*4
            '30m': 48,     # 24*2
            '1h': 24,      # 24
            '4h': 6,       # 24/4
            '1d': 1        # 1
        }
        
        return days * interval_map.get(interval, 6)  # 默认4h
    
    def validate_data_quality(self, symbol: str, interval: str, 
                            days_back: int = 7) -> Dict[str, Any]:
        """
        验证数据质量
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            days_back: 检查最近的天数
            
        Returns:
            Dict[str, Any]: 数据质量报告
        """
        logger.info(f"开始验证 {symbol} ({interval}) 数据质量")
        
        report = {
            'symbol': symbol,
            'interval': interval,
            'check_period_days': days_back,
            'total_records': 0,
            'missing_records': 0,
            'duplicate_records': 0,
            'anomaly_records': 0,
            'quality_score': 0.0,
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 获取最近数据
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days_back)
            
            df = self.storage.get_klines(symbol, interval, start_time, end_time)
            
            if df.empty:
                report['issues'].append("没有找到数据")
                report['recommendations'].append("执行数据同步")
                return report
            
            report['total_records'] = len(df)
            
            # 检查重复记录
            duplicates = df.index.duplicated().sum()
            report['duplicate_records'] = duplicates
            if duplicates > 0:
                report['issues'].append(f"发现 {duplicates} 条重复记录")
                report['recommendations'].append("清理重复数据")
            
            # 检查缺失记录
            missing_periods = self.storage.get_missing_periods(symbol, interval, start_time, end_time)
            report['missing_records'] = len(missing_periods)
            if missing_periods:
                report['issues'].append(f"发现 {len(missing_periods)} 个缺失时间段")
                report['recommendations'].append("补充缺失数据")
            
            # 检查价格异常
            anomalies = self._detect_price_anomalies(df)
            report['anomaly_records'] = len(anomalies)
            if anomalies:
                report['issues'].append(f"发现 {len(anomalies)} 条异常价格记录")
                report['recommendations'].append("检查价格数据的合理性")
            
            # 计算质量评分 (0-100)
            expected_records = self._calculate_expected_records(days_back, interval)
            completeness_score = min(report['total_records'] / expected_records * 100, 100) if expected_records > 0 else 0
            
            duplicate_penalty = (duplicates / report['total_records'] * 100) if report['total_records'] > 0 else 0
            missing_penalty = (report['missing_records'] / expected_records * 100) if expected_records > 0 else 0
            anomaly_penalty = (report['anomaly_records'] / report['total_records'] * 100) if report['total_records'] > 0 else 0
            
            quality_score = max(0, completeness_score - duplicate_penalty - missing_penalty - anomaly_penalty)
            report['quality_score'] = round(quality_score, 2)
            
            # 质量评级
            if quality_score >= 95:
                report['quality_level'] = "优秀"
            elif quality_score >= 85:
                report['quality_level'] = "良好"
            elif quality_score >= 70:
                report['quality_level'] = "一般"
            else:
                report['quality_level'] = "较差"
            
            logger.info(f"✅ {symbol} 数据质量验证完成: {report['quality_level']} ({quality_score:.1f}分)")
            
        except Exception as e:
            report['issues'].append(f"验证过程出错: {str(e)}")
            logger.error(f"❌ {symbol} 数据质量验证失败: {e}")
        
        return report
    
    def _detect_price_anomalies(self, df: pd.DataFrame, threshold: float = 0.1) -> List[dict]:
        """
        检测价格异常
        
        Args:
            df: K线数据
            threshold: 异常阈值（默认10%）
            
        Returns:
            List[dict]: 异常记录列表
        """
        anomalies = []
        
        if df.empty or len(df) < 2:
            return anomalies
        
        try:
            # 计算价格变化率
            df['price_change'] = df['close'].pct_change().abs()
            
            # 检查极端价格变化
            extreme_changes = df[df['price_change'] > threshold]
            
            for idx, row in extreme_changes.iterrows():
                anomalies.append({
                    'timestamp': idx,
                    'type': 'extreme_price_change',
                    'value': row['price_change'],
                    'details': f"价格变化 {row['price_change']:.2%}"
                })
            
            # 检查零价格
            zero_prices = df[(df['open'] <= 0) | (df['high'] <= 0) | 
                           (df['low'] <= 0) | (df['close'] <= 0)]
            
            for idx, row in zero_prices.iterrows():
                anomalies.append({
                    'timestamp': idx,
                    'type': 'zero_price',
                    'details': f"价格为零或负数: O={row['open']}, H={row['high']}, L={row['low']}, C={row['close']}"
                })
            
            # 检查OHLC逻辑错误
            ohlc_errors = df[~(
                (df['high'] >= df['low']) &
                (df['high'] >= df['open']) &
                (df['high'] >= df['close']) &
                (df['low'] <= df['open']) &
                (df['low'] <= df['close'])
            )]
            
            for idx, row in ohlc_errors.iterrows():
                anomalies.append({
                    'timestamp': idx,
                    'type': 'ohlc_logic_error',
                    'details': f"OHLC逻辑错误: O={row['open']}, H={row['high']}, L={row['low']}, C={row['close']}"
                })
        
        except Exception as e:
            logger.error(f"检测价格异常失败: {e}")
        
        return anomalies
    
    def get_statistics(self, symbol: str, interval: str, days: int = 30) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 获取数据
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            df = self.storage.get_klines(symbol, interval, start_time, end_time)
            
            if df.empty:
                return {'error': '没有找到数据'}
            
            stats = {
                'symbol': symbol,
                'interval': interval,
                'period_days': days,
                'total_records': len(df),
                'date_range': {
                    'start': df.index.min().strftime('%Y-%m-%d %H:%M:%S'),
                    'end': df.index.max().strftime('%Y-%m-%d %H:%M:%S')
                },
                'price_stats': {
                    'highest': float(df['high'].max()),
                    'lowest': float(df['low'].min()),
                    'latest_close': float(df['close'].iloc[-1]),
                    'price_change_%': float((df['close'].iloc[-1] / df['close'].iloc[0] - 1) * 100),
                    'volatility_%': float(df['close'].pct_change().std() * 100)
                },
                'volume_stats': {
                    'total_volume': float(df['volume'].sum()),
                    'avg_volume': float(df['volume'].mean()),
                    'max_volume': float(df['volume'].max()),
                    'total_quote_volume': float(df['quote_volume'].sum())
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取 {symbol} 统计信息失败: {e}")
            return {'error': str(e)}
    
    def export_data(self, symbol: str, interval: str, 
                   output_path: str, days: int = 30, format: str = 'csv') -> bool:
        """
        导出数据到文件
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            output_path: 输出路径
            days: 导出天数
            format: 导出格式 ('csv', 'json', 'parquet')
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取数据
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            df = self.storage.get_klines(symbol, interval, start_time, end_time)
            
            if df.empty:
                logger.warning(f"没有数据可导出: {symbol}")
                return False
            
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 重置索引以包含datetime列
            export_df = df.reset_index()
            
            if format.lower() == 'csv':
                export_df.to_csv(output_path, index=False)
            elif format.lower() == 'json':
                export_df.to_json(output_path, orient='records', date_format='iso')
            elif format.lower() == 'parquet':
                export_df.to_parquet(output_path, index=False)
            else:
                raise ValueError(f"不支持的导出格式: {format}")
            
            logger.info(f"✅ {symbol} 数据导出成功: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ {symbol} 数据导出失败: {e}")
            return False
    
    def cleanup_old_data(self, days_to_keep: int = 365):
        """清理旧数据"""
        logger.info(f"开始清理 {days_to_keep} 天之前的旧数据")
        self.storage.cleanup_old_data(days_to_keep)
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            Dict[str, Any]: 系统状态信息
        """
        try:
            db_stats = self.storage.get_database_stats()
            fetcher_stats = self.fetcher.get_request_stats()
            
            status = {
                'database': db_stats,
                'fetcher': fetcher_stats,
                'system_time': datetime.now().isoformat(),
                'status': 'healthy' if db_stats.get('total_records', 0) > 0 else 'no_data'
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {'status': 'error', 'error': str(e)}


# 便利函数
def get_default_manager() -> DataManager:
    """获取默认的数据管理器实例"""
    return DataManager()


def quick_sync_symbol(symbol: str, days: int = 30, interval: str = "4h") -> Dict[str, Any]:
    """
    便利函数：快速同步单个交易对
    
    Args:
        symbol: 交易对符号
        days: 同步天数
        interval: 时间间隔
        
    Returns:
        Dict[str, Any]: 同步结果
    """
    manager = get_default_manager()
    return manager.sync_symbol_data(symbol, interval, days)


def quick_get_data(symbol: str, days: int = 30, interval: str = "4h") -> pd.DataFrame:
    """
    便利函数：快速获取数据
    
    Args:
        symbol: 交易对符号
        days: 获取天数
        interval: 时间间隔
        
    Returns:
        pd.DataFrame: K线数据
    """
    manager = get_default_manager()
    
    # 先尝试从本地获取
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    df = manager.storage.get_klines(symbol, interval, start_time, end_time)
    
    # 如果没有数据，自动同步
    if df.empty:
        logger.info(f"本地无数据，开始同步 {symbol}")
        manager.sync_symbol_data(symbol, interval, days)
        df = manager.storage.get_klines(symbol, interval, start_time, end_time)
    
    return df