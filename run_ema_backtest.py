#!/usr/bin/env python3
"""
运行EMA策略全面回测
Run EMA Strategy Comprehensive Backtest

执行完整的回测流程，包括数据分析、策略回测、结果可视化
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime
import os
import argparse

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from backtest_ema_comprehensive import EMAComprehensiveBacktest
from backtest_visualization import BacktestVisualizer

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='EMA策略全面回测系统')
    parser.add_argument('--no-charts', action='store_true', default=True,
                       help='不生成图表（避免显示图片影响观看）')
    parser.add_argument('--output-dir', type=str, default=None,
                       help='指定输出目录（默认自动生成）')
    args = parser.parse_args()

    print("🚀 启动EMA策略全面回测系统（优化版）")
    print("=" * 60)

    if args.no_charts:
        print("📝 注意：已禁用图表生成")
    else:
        print("🎨 将生成完整的可视化图表")
    
    # 1. 运行全面回测
    print("📊 第一阶段：运行全面回测...")
    backtest = EMAComprehensiveBacktest()
    results = backtest.run_comprehensive_backtest()
    
    if not results:
        print("❌ 回测失败，无结果可分析")
        return
    
    # 创建输出目录
    if args.output_dir:
        output_dir = args.output_dir
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"backtest_results/ema_optimized_backtest_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)

    # 2. 生成可视化图表（可选）
    if not args.no_charts:
        print("\n🎨 第二阶段：生成可视化图表...")
        visualizer = BacktestVisualizer()

        # 2.1 生成汇总图表
        summary_chart_path = f"{output_dir}/summary_charts.png"
        visualizer.plot_backtest_summary(results, summary_chart_path)

        # 2.2 为表现最好的几个币种生成详细图表
        print("\n📈 生成详细K线图表...")

        # 按收益率排序，选择前5个和后5个
        sorted_results = sorted(results.items(), key=lambda x: x[1]['total_return'], reverse=True)

        # 最佳表现币种
        print("  🏆 最佳表现币种:")
        for i, (symbol, result) in enumerate(sorted_results[:5]):
            print(f"    {i+1}. {symbol}: {result['total_return']:.2f}%")

            # 生成K线图
            kline_path = f"{output_dir}/kline_{symbol}_best.png"
            visualizer.plot_kline_with_signals(result['data'], symbol, kline_path)

            # 生成信号详细图
            signal_indices = result['data'][result['data']['signal'] != 0].index.tolist()[:6]  # 前6个信号
            if signal_indices:
                signal_path = f"{output_dir}/signals_{symbol}_best.png"
                visualizer.plot_signal_details(result['data'], symbol, signal_indices, signal_path)

        # 最差表现币种
        print("\n  📉 最差表现币种:")
        for i, (symbol, result) in enumerate(sorted_results[-5:]):
            print(f"    {i+1}. {symbol}: {result['total_return']:.2f}%")

            # 生成K线图
            kline_path = f"{output_dir}/kline_{symbol}_worst.png"
            visualizer.plot_kline_with_signals(result['data'], symbol, kline_path)
    else:
        print("\n📊 跳过图表生成，仅生成数据分析...")
        # 按收益率排序用于报告
        sorted_results = sorted(results.items(), key=lambda x: x[1]['total_return'], reverse=True)
    
    # 3. 生成详细分析报告
    print(f"\n📋 第三阶段：生成详细分析报告...")
    generate_detailed_report(results, output_dir, args.no_charts)

    print(f"\n✅ 回测完成！所有结果已保存到: {output_dir}")
    print("\n📁 输出文件:")
    if not args.no_charts:
        print(f"  📊 汇总图表: summary_charts.png")
        print(f"  📈 K线图表: kline_*.png")
        print(f"  🎯 信号详图: signals_*.png")
    print(f"  📋 详细报告: detailed_report.txt")
    print(f"  💾 原始数据: ema_optimized_backtest_summary_*.json")
    print(f"  📝 交易记录: ema_optimized_trades_detail_*.csv")

def generate_detailed_report(results, output_dir, no_charts=False):
    """生成详细分析报告"""
    report_path = f"{output_dir}/detailed_report.txt"

    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("EMA突破策略回测详细报告（时间冷却期版）\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"策略版本: EMABreakoutStrategy v3.0 (时间冷却期版)\n")
        f.write(f"图表生成: {'否' if no_charts else '是'}\n\n")

        # 整体统计
        total_symbols = len(results)
        returns = [r['total_return'] for r in results.values()]
        win_rates = [r['win_rate'] for r in results.values()]
        trade_counts = [r['total_trades'] for r in results.values()]

        # 计算总收益率：每次交易头寸算1，累计所有交易的收益率
        total_trades_all = sum(trade_counts)
        total_return_by_unit = 0

        # 从每个币种的详细交易记录中累计收益率
        for symbol, result in results.items():
            if 'trades' in result and result['trades']:
                # 累计该币种所有交易的收益率
                for trade in result['trades']:
                    if 'return_pct' in trade:
                        total_return_by_unit += trade['return_pct']
                    elif 'pnl_pct' in trade:  # 备用字段名
                        total_return_by_unit += trade['pnl_pct']

        profitable_symbols = sum(1 for r in returns if r > 0)

        f.write("📊 整体统计\n")
        f.write("-" * 30 + "\n")
        f.write(f"回测币种数量: {total_symbols}\n")
        f.write(f"盈利币种数量: {profitable_symbols} ({profitable_symbols/total_symbols*100:.1f}%)\n")
        f.write(f"总交易次数: {total_trades_all}\n")
        f.write(f"总收益率(按单位头寸): {total_return_by_unit:.2f}% (每笔交易头寸=1，累计所有交易收益率)\n")
        f.write(f"平均每币种交易次数: {np.mean(trade_counts):.1f}\n\n")
        
        # 收益率分析
        f.write("💰 收益率分析\n")
        f.write("-" * 30 + "\n")
        f.write(f"平均收益率: {np.mean(returns):.2f}%\n")
        f.write(f"中位数收益率: {np.median(returns):.2f}%\n")
        f.write(f"最大收益率: {np.max(returns):.2f}%\n")
        f.write(f"最小收益率: {np.min(returns):.2f}%\n")
        f.write(f"收益率标准差: {np.std(returns):.2f}%\n")
        f.write(f"正收益币种比例: {profitable_symbols/total_symbols*100:.1f}%\n\n")
        
        # 胜率分析
        f.write("🎯 胜率分析\n")
        f.write("-" * 30 + "\n")
        f.write(f"平均胜率: {np.mean(win_rates):.1f}%\n")
        f.write(f"中位数胜率: {np.median(win_rates):.1f}%\n")
        f.write(f"最高胜率: {np.max(win_rates):.1f}%\n")
        f.write(f"最低胜率: {np.min(win_rates):.1f}%\n")
        f.write(f"胜率标准差: {np.std(win_rates):.1f}%\n\n")
        
        # 交易频率分析
        f.write("📈 交易频率分析\n")
        f.write("-" * 30 + "\n")
        f.write(f"平均交易次数: {np.mean(trade_counts):.1f}\n")
        f.write(f"中位数交易次数: {np.median(trade_counts):.1f}\n")
        f.write(f"最多交易次数: {np.max(trade_counts)}\n")
        f.write(f"最少交易次数: {np.min(trade_counts)}\n\n")
        
        # 所有币种详细数据
        sorted_results = sorted(results.items(), key=lambda x: x[1]['total_return'], reverse=True)

        f.write("📋 所有币种详细数据 (按收益率排序)\n")
        f.write("-" * 70 + "\n")
        f.write(f"{'排名':<4} {'币种':<12} {'收益率':<10} {'胜率':<8} {'交易次数':<8} {'最大回撤':<10} {'夏普比率':<10}\n")
        f.write("-" * 70 + "\n")

        for i, (symbol, result) in enumerate(sorted_results):
            f.write(f"{i+1:<4} {symbol:<12} {result['total_return']:>7.2f}% "
                   f"{result['win_rate']:>6.1f}% {result['total_trades']:>6} "
                   f"{result.get('max_drawdown', 0):>8.2f}% {result.get('sharpe_ratio', 0):>8.3f}\n")

        f.write("\n")
        
        # 策略参数
        f.write("⚙️ 策略参数（时间冷却期版）\n")
        f.write("-" * 30 + "\n")
        f.write("策略版本: EMABreakoutStrategy v3.0\n")
        f.write("主要特性: 时间冷却期 + 15%止损\n\n")

        f.write("入场条件:\n")
        f.write("1. 技术条件: EMA21 > EMA200 且 EMA21 > EMA55\n")
        f.write("2. 时间冷却: 距离上次入场信号 > 60天\n")
        f.write("3. 信号触发: 条件刚满足时触发\n\n")

        f.write("退出条件:\n")
        f.write("1. 固定止损: 亏损大于15%（每4小时检查，优先级最高）\n")
        f.write("2. 技术退出: EMA21斜率连续3个周期为负\n\n")

        f.write("数据处理:\n")
        f.write("- 数据来源: 4小时K线重采样为日线\n")
        f.write("- 日线收盘: 早8点\n")
        f.write("- EMA200最少数据: 200个周期\n")
        f.write("- 不考虑手续费和滑点\n\n")

        f.write("核心特性:\n")
        f.write("✅ 时间冷却期: 避免频繁交易，确保每次开仓都是新趋势周期\n")
        f.write("✅ 15%固定止损: 严格风险控制，优先级最高\n")
        f.write("✅ 多重EMA确认: EMA21>EMA55>EMA200层次化确认\n")
        f.write("✅ 斜率退出: EMA21斜率连续为负时技术退出\n")
    
    print(f"📋 详细报告已保存: {report_path}")

if __name__ == "__main__":
    main()
