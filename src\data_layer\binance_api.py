"""
Handles all interactions with the Binance API.
"""
import requests
from datetime import datetime
from src.config_manager import config_manager
from src.logger_config import get_logger

logger = get_logger(__name__)

def _get_request_config():
    """获取请求配置，包括代理设置"""
    proxies = config_manager.get_proxies()
    
    # 如果代理配置存在但无法连接，则不使用代理
    if proxies:
        try:
            # 测试代理连接
            test_response = requests.get(
                "https://httpbin.org/ip", 
                proxies=proxies, 
                timeout=3
            )
            if test_response.status_code == 200:
                logger.debug("代理连接正常")
                return proxies
        except Exception as e:
            logger.warning(f"代理连接失败，将使用直连: {e}")
    
    return None

def get_klines_info(symbol, start_time, end_time, interval):
    """Fetches K-line data from Binance."""
    try:
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
        
        params = {
            "symbol": symbol,
            "interval": interval,
            "startTime": start_ts,
            "endTime": end_ts
        }
        
        # 智能代理配置
        proxies = _get_request_config()
        kline_url = config_manager.get('binance.perp_kline_url')
        
        logger.debug(f"获取 {symbol} K线数据，时间范围: {start_time} - {end_time}")
        
        response = requests.get(kline_url, params=params, proxies=proxies, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        logger.info(f"成功获取 {symbol} K线数据: {len(data)} 条记录")
        return data

    except requests.exceptions.RequestException as e:
        logger.error(f"获取 {symbol} K线数据失败: {e}")
        return None

def get_open_interest_hist(symbol, period, start_time, end_time):
    """Fetches historical open interest data."""
    try:
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)

        params = {
            "symbol": symbol,
            "period": period,
            "startTime": start_ts,
            "endTime": end_ts,
            "limit": 500  # Max limit
        }
        
        # 智能代理配置
        proxies = _get_request_config()
        oi_url = config_manager.get('binance.oi_url')
        
        logger.debug(f"获取 {symbol} 持仓量历史数据，周期: {period}")
        
        response = requests.get(oi_url, params=params, proxies=proxies, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        logger.info(f"成功获取 {symbol} 持仓量数据: {len(data)} 条记录")
        return data
        
    except requests.exceptions.RequestException as e:
        logger.error(f"获取 {symbol} 持仓量数据失败: {e}")
        return None 