#!/usr/bin/env python3
"""
EMA策略全面回测
Comprehensive EMA Strategy Backtest

对数据库中所有币种进行全面回测，生成详细报告和可视化图表
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import json
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 导入策略和数据处理模块
from src.strategies.ema_breakout_strategy import EMABreakoutStrategy
from src.strategies import BacktestEngine
from src.data_layer.historical_data_fetcher import scan_local_databases
from src.core.config_manager import ConfigManager

class EMAComprehensiveBacktest:
    """EMA策略全面回测类"""
    
    def __init__(self):
        self.strategy = EMABreakoutStrategy({}, {})
        self.backtest_engine = BacktestEngine(initial_capital=10000, commission=0.0)  # 不考虑手续费
        self.results = {}
        self.trade_records = []
        # 加载配置管理器
        self.config = ConfigManager("./config")
        
    def scan_all_data(self):
        """扫描数据库中的所有数据"""
        print("🔍 扫描数据库中的所有数据...")

        try:
            databases = scan_local_databases()
            print(f"✅ 发现 {len(databases)} 个数据表")

            all_symbols = set()
            db_info = {}

            for db_info_item in databases:
                db_path = str(db_info_item['db_file'])  # 修正：使用正确的键名
                table_name = db_info_item['table_name']  # 修正：使用正确的键名
                symbols = db_info_item['symbols']  # 直接使用已获取的币种列表

                all_symbols.update(symbols)
                db_info[db_path] = {
                    'table': table_name,
                    'symbols': symbols,
                    'time_range': (db_info_item['earliest_date'], db_info_item['latest_date']),
                    'total_records': db_info_item['total_klines']
                }

                print(f"  📊 {Path(db_path).name}: {len(symbols)} 个币种, {db_info_item['total_klines']} 条记录")
                print(f"     时间范围: {db_info_item['earliest_date']} ~ {db_info_item['latest_date']}")

            print(f"\n📈 总计: {len(all_symbols)} 个唯一币种")
            print(f"币种列表: {sorted(list(all_symbols))[:10]}{'...' if len(all_symbols) > 10 else ''}")

            return list(all_symbols), db_info

        except Exception as e:
            import traceback
            print(f"❌ 扫描数据库失败: {e}")
            print(f"详细错误: {traceback.format_exc()}")
            return [], {}
    
    def load_symbol_data(self, symbol, db_info):
        """加载指定币种的所有数据"""
        all_data = []
        
        for db_path, info in db_info.items():
            if symbol in info['symbols']:
                try:
                    conn = sqlite3.connect(db_path)
                    
                    query = f"""
                    SELECT datetime_str, open_price as open, high_price as high,
                           low_price as low, close_price as close, volume
                    FROM {info['table']}
                    WHERE symbol = ?
                    ORDER BY datetime_str
                    """
                    
                    df = pd.read_sql_query(query, conn, params=[symbol])
                    conn.close()
                    
                    if not df.empty:
                        # 转换数据类型
                        df['datetime'] = pd.to_datetime(df['datetime_str'])
                        for col in ['open', 'high', 'low', 'close', 'volume']:
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                        
                        all_data.append(df)
                        
                except Exception as e:
                    print(f"  ⚠️ 加载 {symbol} 从 {db_path} 失败: {e}")
        
        if all_data:
            # 合并所有数据并去重
            combined_data = pd.concat(all_data, ignore_index=True)
            combined_data = combined_data.drop_duplicates(subset=['datetime_str']).sort_values('datetime')
            combined_data = combined_data.reset_index(drop=True)
            return combined_data
        
        return pd.DataFrame()
    
    def run_single_symbol_backtest(self, symbol, data):
        """运行单个币种的回测"""
        if len(data) < 300:  # 至少需要300个数据点
            return None
        
        print(f"  🚀 回测 {symbol}: {len(data)} 条数据")
        print(f"     时间范围: {data['datetime'].min()} ~ {data['datetime'].max()}")
        
        try:
            # 计算指标
            df_with_indicators = self.strategy.calculate_indicators(data)
            
            # 生成信号
            df_with_signals = self.strategy.generate_signals(df_with_indicators)
            
            # 统计信号
            buy_signals = (df_with_signals['signal'] == 1).sum()
            sell_signals = (df_with_signals['signal'] == -1).sum()
            
            print(f"     📊 买入信号: {buy_signals}, 卖出信号: {sell_signals}")
            
            # 模拟交易
            trades = self.simulate_trading(df_with_signals, symbol)
            
            # 计算收益
            if trades:
                total_return = self.calculate_returns(trades)
                win_rate = self.calculate_win_rate(trades)
                max_drawdown = self.calculate_max_drawdown(trades, data)
                sharpe_ratio = self.calculate_sharpe_ratio(trades, data)

                result = {
                    'symbol': symbol,
                    'data_points': len(data),
                    'time_range': (str(data['datetime'].min()), str(data['datetime'].max())),
                    'buy_signals': buy_signals,
                    'sell_signals': sell_signals,
                    'total_trades': len(trades),
                    'total_return': total_return,
                    'win_rate': win_rate,
                    'max_drawdown': max_drawdown,
                    'sharpe_ratio': sharpe_ratio,
                    'trades': trades,
                    'data': df_with_signals
                }
                
                print(f"     💰 总收益: {total_return:.2f}%, 胜率: {win_rate:.1f}%, 交易次数: {len(trades)}")
                return result
            else:
                print(f"     ⚠️ 无有效交易")
                return None
                
        except Exception as e:
            print(f"     ❌ 回测失败: {e}")
            return None
    
    def simulate_trading(self, data, symbol):
        """模拟交易过程（适配新的动态退出条件）"""
        trades = []
        position = None  # None: 无仓位, 'LONG': 多头仓位
        entry_price = 0
        entry_time = None

        for i, row in data.iterrows():
            if row['signal'] == 1 and position is None:  # 买入信号且无仓位
                position = 'LONG'
                entry_price = row['close']
                entry_time = row['datetime']

                trade_record = {
                    'symbol': symbol,
                    'entry_time': entry_time,
                    'entry_price': entry_price,
                    'entry_reason': row['reason'],
                    'entry_confidence': row['confidence'],
                    'ema21': row.get('ema21', np.nan),
                    'ema50': row.get('ema50', np.nan),
                    'ema200': row.get('ema200', np.nan),
                    'ema21_slope': row.get('ema21_slope', np.nan),
                    'ema50_slope': row.get('ema50_slope', np.nan)
                }

            elif row['signal'] == -1 and position == 'LONG':  # 卖出信号且有多头仓位
                position = None
                exit_price = row['close']
                exit_time = row['datetime']

                # 计算收益
                return_pct = (exit_price - entry_price) / entry_price * 100
                hold_days = (exit_time - entry_time).days
                trading_days = row.get('trading_days', 0)  # 获取交易日天数

                # 完善交易记录
                trade_record.update({
                    'exit_time': exit_time,
                    'exit_price': exit_price,
                    'exit_reason': row['reason'],
                    'exit_confidence': row['confidence'],
                    'exit_type': row.get('exit_type', ''),
                    'return_pct': return_pct,
                    'hold_days': hold_days,
                    'trading_days': trading_days,
                    'profit': return_pct > 0
                })

                trades.append(trade_record)

        return trades
    
    def calculate_returns(self, trades):
        """计算总收益率"""
        if not trades:
            return 0
        
        total_return = 1
        for trade in trades:
            total_return *= (1 + trade['return_pct'] / 100)
        
        return (total_return - 1) * 100
    
    def calculate_win_rate(self, trades):
        """计算胜率"""
        if not trades:
            return 0

        winning_trades = sum(1 for trade in trades if trade['profit'])
        return winning_trades / len(trades) * 100

    def calculate_max_drawdown(self, trades, data):
        """计算最大回撤"""
        if not trades:
            return 0

        # 构建权益曲线
        equity_curve = [10000]  # 初始资金
        current_equity = 10000

        # 按时间排序交易
        sorted_trades = sorted(trades, key=lambda x: x['exit_time'])

        for trade in sorted_trades:
            # 计算交易后的权益
            return_ratio = trade['return_pct'] / 100
            current_equity = current_equity * (1 + return_ratio)
            equity_curve.append(current_equity)

        # 计算最大回撤
        peak = equity_curve[0]
        max_drawdown = 0

        for equity in equity_curve:
            if equity > peak:
                peak = equity
            if peak > 0:
                drawdown = (peak - equity) / peak * 100
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

        return max_drawdown

    def calculate_sharpe_ratio(self, trades, data):
        """计算夏普比率"""
        if not trades or len(trades) < 2:
            return 0

        # 计算每笔交易的收益率
        returns = [trade['return_pct'] / 100 for trade in trades]

        if len(returns) < 2:
            return 0

        # 计算平均收益率和标准差
        mean_return = np.mean(returns)
        std_return = np.std(returns)

        if std_return == 0:
            return 0

        # 年化夏普比率（假设每年约有100笔交易）
        sharpe_ratio = (mean_return / std_return) * np.sqrt(100)

        # 限制在合理范围内
        return min(max(sharpe_ratio, -10), 10)
    
    def run_comprehensive_backtest(self):
        """运行全面回测"""
        print("🧪 开始EMA策略全面回测")
        print("=" * 60)
        
        # 1. 扫描所有数据
        all_symbols, db_info = self.scan_all_data()
        
        if not all_symbols:
            print("❌ 未找到任何数据")
            return
        
        # 2. 应用黑名单过滤
        monitoring_config = self.config.get_monitoring_config()
        blacklist = monitoring_config.get("blacklist", [])

        if blacklist:
            original_count = len(all_symbols)
            all_symbols = [symbol for symbol in all_symbols if symbol not in blacklist]
            filtered_count = original_count - len(all_symbols)
            print(f"🚫 黑名单过滤: 排除了 {filtered_count} 个币种")
            print(f"   黑名单: {', '.join(blacklist)}")

        print(f"\n🎯 开始回测 {len(all_symbols)} 个币种...")

        # 3. 逐个币种回测
        successful_backtests = 0
        failed_backtests = 0
        
        for i, symbol in enumerate(all_symbols, 1):
            print(f"\n[{i}/{len(all_symbols)}] 回测 {symbol}")
            
            # 加载数据
            data = self.load_symbol_data(symbol, db_info)
            
            if data.empty:
                print(f"  ❌ {symbol} 无数据")
                failed_backtests += 1
                continue
            
            # 运行回测
            result = self.run_single_symbol_backtest(symbol, data)
            
            if result:
                self.results[symbol] = result
                successful_backtests += 1
            else:
                failed_backtests += 1
        
        print(f"\n✅ 回测完成!")
        print(f"成功: {successful_backtests} 个币种")
        print(f"失败: {failed_backtests} 个币种")
        
        # 3. 生成汇总报告
        self.generate_summary_report()
        
        return self.results

    def generate_summary_report(self):
        """生成汇总报告"""
        if not self.results:
            print("❌ 无回测结果")
            return

        print("\n📊 回测汇总报告")
        print("=" * 60)

        # 统计数据
        total_symbols = len(self.results)
        total_trades = sum(len(result['trades']) for result in self.results.values())

        # 收益统计
        returns = [result['total_return'] for result in self.results.values()]
        win_rates = [result['win_rate'] for result in self.results.values()]

        profitable_symbols = sum(1 for r in returns if r > 0)

        print(f"回测币种数量: {total_symbols}")
        print(f"总交易次数: {total_trades}")
        print(f"盈利币种数量: {profitable_symbols} ({profitable_symbols/total_symbols*100:.1f}%)")

        if returns:
            print(f"\n收益率统计:")
            print(f"  平均收益率: {np.mean(returns):.2f}%")
            print(f"  最大收益率: {np.max(returns):.2f}%")
            print(f"  最小收益率: {np.min(returns):.2f}%")
            print(f"  收益率标准差: {np.std(returns):.2f}%")

        if win_rates:
            print(f"\n胜率统计:")
            print(f"  平均胜率: {np.mean(win_rates):.1f}%")
            print(f"  最高胜率: {np.max(win_rates):.1f}%")
            print(f"  最低胜率: {np.min(win_rates):.1f}%")

        # 最佳表现币种
        if returns:
            best_symbol = max(self.results.keys(), key=lambda x: self.results[x]['total_return'])
            worst_symbol = min(self.results.keys(), key=lambda x: self.results[x]['total_return'])

            print(f"\n🏆 最佳表现: {best_symbol}")
            print(f"  收益率: {self.results[best_symbol]['total_return']:.2f}%")
            print(f"  胜率: {self.results[best_symbol]['win_rate']:.1f}%")
            print(f"  交易次数: {self.results[best_symbol]['total_trades']}")

            print(f"\n📉 最差表现: {worst_symbol}")
            print(f"  收益率: {self.results[worst_symbol]['total_return']:.2f}%")
            print(f"  胜率: {self.results[worst_symbol]['win_rate']:.1f}%")
            print(f"  交易次数: {self.results[worst_symbol]['total_trades']}")

        # 保存详细结果
        self.save_results_to_file()

    def save_results_to_file(self):
        """保存结果到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存汇总结果
        summary_file = f"backtest_results/ema_backtest_summary_{timestamp}.json"
        os.makedirs("backtest_results", exist_ok=True)

        # 准备可序列化的数据
        serializable_results = {}
        for symbol, result in self.results.items():
            serializable_results[symbol] = {
                'symbol': result['symbol'],
                'data_points': result['data_points'],
                'time_range': result['time_range'],
                'buy_signals': result['buy_signals'],
                'sell_signals': result['sell_signals'],
                'total_trades': result['total_trades'],
                'total_return': result['total_return'],
                'win_rate': result['win_rate'],
                'trades': result['trades']  # 交易记录
            }

        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False, default=str)

        print(f"\n💾 结果已保存到: {summary_file}")

        # 保存详细交易记录
        trades_file = f"backtest_results/ema_trades_detail_{timestamp}.csv"
        all_trades = []

        for symbol, result in self.results.items():
            for trade in result['trades']:
                trade_record = trade.copy()
                all_trades.append(trade_record)

        if all_trades:
            trades_df = pd.DataFrame(all_trades)
            trades_df.to_csv(trades_file, index=False, encoding='utf-8')
            print(f"💾 交易记录已保存到: {trades_file}")

        return summary_file, trades_file

if __name__ == "__main__":
    backtest = EMAComprehensiveBacktest()
    results = backtest.run_comprehensive_backtest()
