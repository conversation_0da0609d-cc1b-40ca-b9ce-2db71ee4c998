"""
通知服务模块
Notification Service

统一管理所有的通知功能，包括邮件和Discord通知。
"""

from noticeMesg import func_notice_with_send_email, func_notice_with_send_email_beta
from discord import func_send_message_to_discord, func_send_message_to_discord_beta


class NotificationService:
    """通知服务类"""
    
    def __init__(self, use_beta=False):
        """
        初始化通知服务
        
        Args:
            use_beta (bool): 是否使用beta版本的通知功能
        """
        self.use_beta = use_beta
    
    def send_email(self, subject, content, attachment=""):
        """
        发送邮件通知
        
        Args:
            subject (str): 邮件主题
            content (str): 邮件内容
            attachment (str): 附件路径（可选）
        """
        try:
            if self.use_beta:
                func_notice_with_send_email_beta(subject, content, attachment)
            else:
                func_notice_with_send_email(subject, content, attachment)
            print(f"邮件通知已发送: {subject}")
        except Exception as e:
            print(f"发送邮件失败: {e}")
    
    def send_discord(self, message):
        """
        发送Discord通知
        
        Args:
            message (str): Discord消息内容
        """
        try:
            if self.use_beta:
                func_send_message_to_discord_beta(message)
            else:
                func_send_message_to_discord(message)
            print(f"Discord通知已发送")
        except Exception as e:
            print(f"发送Discord消息失败: {e}")
    
    def send_alert(self, alert_type, message, send_email=True, send_discord=True):
        """
        发送综合警报（同时发送邮件和Discord）
        
        Args:
            alert_type (str): 警报类型，用作邮件主题
            message (str): 警报内容
            send_email (bool): 是否发送邮件
            send_discord (bool): 是否发送Discord消息
        """
        if send_email:
            self.send_email(alert_type, message)
        
        if send_discord:
            self.send_discord(message)
    
    def send_volume_alert(self, volume_results):
        """发送交易量异常警报"""
        if volume_results:
            self.send_alert(
                "交易量异常检测警报",
                volume_results,
                send_email=True,
                send_discord=True
            )
    
    def send_oi_alert(self, oi_results):
        """发送持仓量异常警报"""
        if oi_results:
            self.send_alert(
                "持仓量异常检测警报", 
                oi_results,
                send_email=True,
                send_discord=True
            )
    
    def send_ema_alert(self, ema_results):
        """发送EMA趋势警报"""
        if ema_results:
            self.send_alert(
                "EMA趋势检测警报",
                ema_results,
                send_email=True,
                send_discord=True
            )
    
    def send_error_alert(self, error_msg, component="系统"):
        """发送错误警报"""
        subject = f"{component}运行错误"
        self.send_alert(
            subject,
            f"错误信息: {error_msg}",
            send_email=True,
            send_discord=False  # 错误消息通常只发邮件
        )


# 创建默认的通知服务实例
default_notification_service = NotificationService(use_beta=False)

# 提供便捷的函数接口，保持向后兼容
def send_volume_alert(message):
    """发送交易量异常警报的便捷函数"""
    default_notification_service.send_volume_alert(message)

def send_oi_alert(message):
    """发送持仓量异常警报的便捷函数"""
    default_notification_service.send_oi_alert(message)

def send_ema_alert(message):
    """发送EMA趋势警报的便捷函数"""
    default_notification_service.send_ema_alert(message)

def send_error_alert(error_msg, component="系统"):
    """发送错误警报的便捷函数"""
    default_notification_service.send_error_alert(error_msg, component) 