"""
实时交易监控系统
Real-time Trading Monitor System

集成实时数据处理、信号生成和执行管理的完整监控系统
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime, timedelta
from typing import Dict, Any

from src.core.config_manager import ConfigManager
from src.services.real_time_data_processor import RealTimeDataProcessor
from src.services.signal_generator import StrategySignalGenerator
from src.services.execution_manager import (
    ExecutionManager, DiscordNotifier, EmailNotifier, LogNotifier
)
from src.strategies import get_strategy

class RealTimeMonitor:
    """实时交易监控系统"""
    
    def __init__(self, config_path: str = "./config"):
        """
        初始化监控系统
        
        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        self.config = ConfigManager(config_path)
        self.config.setup_logging()
        
        # 初始化组件
        self.data_processor = None
        self.signal_generator = None
        self.execution_manager = None
        
        # 系统状态
        self.is_running = False
        self.start_time = None
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"🛑 收到信号 {signum}，正在停止系统...")
        asyncio.create_task(self.stop())
    
    async def initialize(self):
        """初始化系统组件"""
        try:
            self.logger.info("🚀 初始化实时监控系统...")
            
            # 1. 初始化数据处理器
            binance_config = self.config.get_binance_client_config()
            monitoring_config = self.config.get_monitoring_config()
            
            self.data_processor = RealTimeDataProcessor(
                api_key=binance_config.get("api_key"),
                api_secret=binance_config.get("api_secret"),
                interval=monitoring_config["interval"]
            )
            
            # 2. 初始化信号生成器
            trading_config = self.config.get_trading_config()
            self.signal_generator = StrategySignalGenerator(
                initial_capital=trading_config["initial_capital"]
            )
            
            # 配置信号生成器参数
            self.signal_generator.min_confidence = trading_config["min_confidence"]
            self.signal_generator.signal_cooldown = timedelta(hours=trading_config["signal_cooldown_hours"])
            self.signal_generator.max_signals_per_day = trading_config["max_signals_per_day"]
            
            # 3. 初始化执行管理器
            self.execution_manager = ExecutionManager(self.signal_generator)
            self.execution_manager.simulation_mode = trading_config["simulation_mode"]
            self.execution_manager.auto_execution = trading_config["auto_execution"]
            
            # 4. 配置通知渠道
            await self._setup_notifications()
            
            # 5. 配置策略和交易对
            await self._setup_strategies()
            
            # 6. 设置回调函数
            self._setup_callbacks()
            
            self.logger.info("✅ 系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 系统初始化失败: {e}")
            raise
    
    async def _setup_notifications(self):
        """设置通知渠道"""
        # 日志通知（始终启用）
        log_notifier = LogNotifier()
        self.execution_manager.add_notifier(log_notifier)
        
        # Discord通知
        discord_config = self.config.get_discord_config()
        if discord_config["enabled"]:
            discord_notifier = DiscordNotifier(
                webhook_url=discord_config.get("webhook_url"),
                bot_token=discord_config.get("bot_token"),
                channel_id=discord_config.get("channel_id")
            )
            self.execution_manager.add_notifier(discord_notifier)
            self.logger.info("✅ Discord通知已启用")
        
        # 邮件通知
        email_config = self.config.get_email_config()
        if email_config["enabled"]:
            email_notifier = EmailNotifier(
                smtp_server=email_config["smtp_server"],
                smtp_port=email_config["smtp_port"],
                username=email_config["username"],
                password=email_config["password"],
                recipients=email_config["recipients"]
            )
            self.execution_manager.add_notifier(email_notifier)
            self.logger.info("✅ 邮件通知已启用")
    
    async def _setup_strategies(self):
        """设置策略和交易对"""
        strategy_config = self.config.get_strategy_config()
        monitoring_config = self.config.get_monitoring_config()
        
        # 为每个交易对添加策略
        for symbol in monitoring_config["symbols"]:
            # 添加交易对到数据处理器
            self.data_processor.add_symbol(symbol)
            
            # 为每个启用的策略添加到信号生成器
            for strategy_name in strategy_config["enabled_strategies"]:
                # 获取自定义参数
                custom_params = strategy_config["custom_params"].get(strategy_name, {})
                
                # 添加策略
                self.signal_generator.add_strategy(symbol, strategy_name, custom_params)
                
                self.logger.info(f"🎯 为 {symbol} 添加策略: {strategy_name}")
        
        # 更新策略权重
        self.signal_generator.strategy_weights.update(strategy_config["strategy_weights"])
    
    def _setup_callbacks(self):
        """设置回调函数"""
        # 数据更新回调
        self.data_processor.add_data_callback(self._on_data_update)
        
        # 信号生成回调
        self.data_processor.add_signal_callback(self._on_signal_generated)
    
    def _on_data_update(self, symbol: str, kline: Dict[str, Any]):
        """数据更新回调"""
        self.logger.debug(f"📊 {symbol} 数据更新: {kline['close']:.4f}")
        
        # 检查止损止盈
        stop_signal = self.signal_generator.check_stop_loss_take_profit(
            symbol, kline['close']
        )
        
        if stop_signal:
            # 异步处理止损止盈信号
            asyncio.create_task(self.execution_manager.process_signal(stop_signal))
    
    def _on_signal_generated(self, symbol: str, signal_data: Dict[str, Any]):
        """信号生成回调"""
        self.logger.info(f"🚨 {symbol} 原始信号: {signal_data['signal']} (置信度: {signal_data['confidence']})")
        
        # 获取最新数据
        latest_data = self.data_processor.get_latest_data(symbol, limit=100)
        
        if not latest_data.empty:
            # 处理信号融合
            final_signal = self.signal_generator.process_signal(symbol, latest_data)
            
            if final_signal:
                # 异步处理最终信号
                asyncio.create_task(self.execution_manager.process_signal(final_signal))
    
    async def start(self):
        """启动监控系统"""
        if self.is_running:
            self.logger.warning("⚠️ 系统已在运行中")
            return
        
        try:
            # 初始化系统
            await self.initialize()
            
            # 启动各个组件
            self.data_processor.start_monitoring()
            self.execution_manager.start()
            
            self.is_running = True
            self.start_time = datetime.now()
            
            self.logger.info("🚀 实时监控系统已启动")
            
            # 发送启动通知
            await self._send_startup_notification()
            
            # 开始监控循环
            await self._monitoring_loop()
            
        except Exception as e:
            self.logger.error(f"❌ 启动失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止监控系统"""
        if not self.is_running:
            return
        
        try:
            self.logger.info("🛑 正在停止监控系统...")
            
            # 停止各个组件
            if self.data_processor:
                self.data_processor.stop_monitoring()
            
            if self.execution_manager:
                self.execution_manager.stop()
            
            self.is_running = False
            
            # 发送停止通知
            await self._send_shutdown_notification()
            
            self.logger.info("✅ 监控系统已停止")
            
        except Exception as e:
            self.logger.error(f"❌ 停止过程中出错: {e}")
    
    async def _monitoring_loop(self):
        """监控主循环"""
        health_check_interval = timedelta(
            minutes=self.config.monitoring.health_check_interval_minutes
        )
        last_health_check = datetime.now()
        
        while self.is_running:
            try:
                # 定期健康检查
                if datetime.now() - last_health_check >= health_check_interval:
                    await self.execution_manager.send_health_report()
                    last_health_check = datetime.now()
                
                # 检查系统状态
                await self._check_system_status()
                
                # 等待一段时间
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"❌ 监控循环出错: {e}")
                await asyncio.sleep(30)  # 出错后等待30秒再继续
    
    async def _check_system_status(self):
        """检查系统状态"""
        try:
            # 检查数据处理器状态
            data_status = self.data_processor.get_status()
            if not data_status["is_running"]:
                self.logger.warning("⚠️ 数据处理器已停止")
            
            # 检查连接状态
            disconnected_symbols = [
                symbol for symbol, connected in data_status["connection_status"].items()
                if not connected
            ]
            
            if disconnected_symbols:
                self.logger.warning(f"⚠️ 连接断开的交易对: {', '.join(disconnected_symbols)}")
            
        except Exception as e:
            self.logger.error(f"❌ 状态检查失败: {e}")
    
    async def _send_startup_notification(self):
        """发送启动通知"""
        monitoring_config = self.config.get_monitoring_config()
        strategy_config = self.config.get_strategy_config()
        
        message = f"""
🚀 **实时监控系统已启动**

**配置信息**:
• 监控间隔: {monitoring_config['interval']}
• 监控交易对: {', '.join(monitoring_config['symbols'])}
• 启用策略: {', '.join(strategy_config['enabled_strategies'])}
• 交易模式: {'🎮 模拟' if self.config.trading.simulation_mode else '💰 实盘'}

**系统状态**:
• 启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
• 初始资金: ${self.config.trading.initial_capital:,.2f}
• 最小置信度: {self.config.trading.min_confidence}%

系统正在监控市场数据，准备生成交易信号...
        """.strip()
        
        await self.execution_manager._send_notifications_direct(
            message, "🚀 系统启动通知"
        )
    
    async def _send_shutdown_notification(self):
        """发送停止通知"""
        if self.start_time:
            runtime = datetime.now() - self.start_time
            runtime_str = str(runtime).split('.')[0]  # 去掉微秒
        else:
            runtime_str = "未知"
        
        # 获取统计信息
        stats = self.execution_manager.get_statistics()
        portfolio = self.signal_generator.get_portfolio_status()
        
        message = f"""
🛑 **实时监控系统已停止**

**运行统计**:
• 运行时间: {runtime_str}
• 总信号数: {stats['stats']['total_signals']}
• 通知成功: {stats['stats']['notifications_sent']}
• 执行成功: {stats['stats']['executions_success']}

**投资组合**:
• 最终价值: ${portfolio['total_value']:.2f}
• 总收益率: {portfolio['total_return_pct']:.2f}%
• 持仓数量: {portfolio['positions_count']}

感谢使用交易监控系统！
        """.strip()
        
        await self.execution_manager._send_notifications_direct(
            message, "🛑 系统停止通知"
        )
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "is_running": self.is_running,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "runtime": str(datetime.now() - self.start_time).split('.')[0] if self.start_time else None,
            "config": self.config.get_all_config(),
            "data_processor_status": self.data_processor.get_status() if self.data_processor else None,
            "signal_generator_stats": self.signal_generator.get_statistics() if self.signal_generator else None,
            "execution_manager_stats": self.execution_manager.get_statistics() if self.execution_manager else None
        }

async def main():
    """主函数"""
    print("🚀 启动实时交易监控系统")
    print("=" * 50)
    
    # 创建监控系统
    monitor = RealTimeMonitor()
    
    try:
        # 启动系统
        await monitor.start()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在停止系统...")
    except Exception as e:
        print(f"❌ 系统运行出错: {e}")
    finally:
        await monitor.stop()
        print("👋 系统已退出")

if __name__ == "__main__":
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 程序异常退出: {e}")
        sys.exit(1) 