#!/usr/bin/env python3
"""
测试 auto_4h_monitor.py 的功能
Test Auto 4H Monitor Functions
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from auto_4h_monitor import Auto4HMonitor

async def test_config_loading():
    """测试配置加载功能"""
    print("🧪 测试配置加载功能")
    print("=" * 40)
    
    monitor = Auto4HMonitor()
    
    # 测试默认配置
    print("\n1. 测试默认配置加载:")
    config = monitor._load_data_fetcher_config()
    print(f"  时间范围: {config['days']} 天")
    print(f"  币种选择: {config['selection_mode']} ({config.get('top_n', config.get('symbols', 'N/A'))})")
    print(f"  K线间隔: {config['interval']}")
    print(f"  使用代理: {config['use_proxy']}")
    
    # 测试预设配置
    print("\n2. 测试实时更新预设:")
    config = monitor._load_data_fetcher_config("real_time_update")
    print(f"  时间范围: {config['days']} 天")
    print(f"  币种选择: {config['selection_mode']} ({config.get('top_n', config.get('symbols', 'N/A'))})")
    
    print("\n3. 测试历史验证预设:")
    config = monitor._load_data_fetcher_config("historical_validation")
    print(f"  时间范围: {config['days']} 天")
    print(f"  币种选择: {config['selection_mode']} ({config.get('top_n', config.get('symbols', 'N/A'))})")
    
    print("\n✅ 配置加载测试完成")

async def test_data_update():
    """测试数据更新功能"""
    print("\n🧪 测试数据更新功能")
    print("=" * 40)
    
    monitor = Auto4HMonitor()
    
    try:
        # 初始化系统
        await monitor.initialize()
        
        print("\n1. 测试实时数据更新:")
        success = await monitor.update_data_using_fetcher("real_time_update")
        print(f"  更新结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            print(f"  监控交易对: {len(monitor.symbols)} 个")
            print(f"  交易对列表: {', '.join(monitor.symbols[:5])}{'...' if len(monitor.symbols) > 5 else ''}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n✅ 数据更新测试完成")

async def test_historical_validation():
    """测试历史数据验证功能"""
    print("\n🧪 测试历史数据验证功能")
    print("=" * 40)
    
    monitor = Auto4HMonitor()
    
    try:
        # 初始化系统
        await monitor.initialize()
        
        print("\n1. 测试历史验证 (最近7天):")
        success = await monitor.run_historical_validation("2025-06-21", "2025-06-28")
        print(f"  验证结果: {'✅ 成功' if success else '❌ 失败'}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n✅ 历史验证测试完成")

async def test_symbol_selection():
    """测试交易对选择功能"""
    print("\n🧪 测试交易对选择功能")
    print("=" * 40)
    
    monitor = Auto4HMonitor()
    
    try:
        # 测试从数据库获取现有交易对
        existing_symbols = monitor._get_existing_symbols_from_db()
        print(f"\n1. 数据库中现有交易对: {len(existing_symbols)} 个")
        if existing_symbols:
            print(f"  前10个: {', '.join(existing_symbols[:10])}")
        
        # 测试智能选择
        if existing_symbols:
            selected = monitor._select_optimal_symbols(existing_symbols)
            print(f"\n2. 智能选择结果: {len(selected)} 个")
            print(f"  选择的交易对: {', '.join(selected[:10])}{'...' if len(selected) > 10 else ''}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n✅ 交易对选择测试完成")

async def test_data_statistics():
    """测试数据统计功能"""
    print("\n🧪 测试数据统计功能")
    print("=" * 40)
    
    monitor = Auto4HMonitor()
    
    try:
        stats = monitor._get_data_statistics()
        print(f"\n数据统计:")
        print(f"  总记录数: {stats['total_records']:,}")
        print(f"  数据范围: {stats['date_range']}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n✅ 数据统计测试完成")

async def main():
    """主测试函数"""
    print("🚀 Auto4HMonitor 功能测试")
    print("=" * 50)
    
    # 运行所有测试
    await test_config_loading()
    await test_symbol_selection()
    await test_data_statistics()
    await test_data_update()
    # await test_historical_validation()  # 注释掉，因为可能需要较长时间
    
    print("\n🎉 所有测试完成!")
    print("=" * 50)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试中断")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
