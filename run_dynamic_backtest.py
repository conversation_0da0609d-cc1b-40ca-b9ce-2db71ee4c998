#!/usr/bin/env python3
"""
动态仓位管理回测系统
Dynamic Position Management Backtest System

基于现有EMA策略，实现全仓动态仓位管理的回测系统
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime
import os
import argparse
import json

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.strategies.ema_dynamic_strategy import EMADynamicStrategy
from src.data_layer.historical_data_fetcher import scan_local_databases
from backtest_visualization import BacktestVisualizer
from src.utils.output_formatter import LogCapture


class DynamicBacktestRunner:
    """动态仓位管理回测运行器"""
    
    def __init__(self, initial_capital: float = 10000):
        self.initial_capital = initial_capital
        self.strategy = None
        self.results = {}
        
    def load_symbol_data(self, symbol: str, db_info: dict,
                        start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        加载单个币种的数据（复用现有逻辑）
        """
        try:
            import sqlite3

            # 连接数据库
            conn = sqlite3.connect(str(db_info['db_file']))

            # 构建查询
            query = f"""
            SELECT datetime_str as datetime, open_price as open, high_price as high,
                   low_price as low, close_price as close, volume
            FROM {db_info['table_name']}
            WHERE symbol = '{symbol}'
            """

            if start_date:
                query += f" AND datetime_str >= '{start_date}'"
            if end_date:
                query += f" AND datetime_str <= '{end_date}'"

            query += " ORDER BY datetime_str"

            # 读取数据
            df = pd.read_sql_query(query, conn)
            conn.close()

            if df.empty:
                return pd.DataFrame()

            # 数据预处理
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            # 不删除datetime列，因为EMA策略可能需要它

            # 数据质量检查
            df = df.dropna()
            df = df[df['close'] > 0]

            return df

        except Exception as e:
            print(f"❌ 加载 {symbol} 数据失败: {e}")
            return pd.DataFrame()
    
    def run_backtest(self, symbols: list = None, start_date: str = None, 
                    end_date: str = None) -> dict:
        """
        运行动态仓位管理回测
        
        Args:
            symbols: 币种列表，None表示使用所有可用币种
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            dict: 回测结果
        """
        print("🚀 启动动态仓位管理回测系统")
        print("=" * 60)
        
        # 1. 扫描数据库
        print("🔍 扫描本地数据库...")
        databases = scan_local_databases()
        
        if not databases:
            print("❌ 未找到数据库文件")
            return {}
        
        # 选择第一个数据库（通常只有一个）
        db_info = databases[0]
        print(f"📊 使用数据库: {db_info.get('name', 'unknown')}")
        
        # 2. 获取可用币种
        if symbols is None:
            # 获取所有币种（复用现有逻辑）
            try:
                import sqlite3
                conn = sqlite3.connect(str(db_info['db_file']))
                cursor = conn.cursor()
                cursor.execute(f"SELECT DISTINCT symbol FROM {db_info['table_name']} ORDER BY symbol")
                all_symbols = [row[0] for row in cursor.fetchall()]
                conn.close()

                # 使用所有USDT币种
                symbols = [s for s in all_symbols if 'USDT' in s and len(s) <= 15]  # 使用所有USDT币种

            except Exception as e:
                print(f"❌ 获取币种列表失败: {e}")
                return {}
        
        print(f"📈 回测币种数量: {len(symbols)}")
        
        # 3. 加载数据
        print("📥 加载历史数据...")
        symbol_data = {}
        
        for i, symbol in enumerate(symbols, 1):
            print(f"  [{i}/{len(symbols)}] 加载 {symbol}...")
            
            data = self.load_symbol_data(symbol, db_info, start_date, end_date)
            
            if not data.empty and len(data) >= 300:  # 至少需要300个数据点
                symbol_data[symbol] = data
                print(f"    ✅ 数据点: {len(data)}")
            else:
                print(f"    ⚠️ 数据不足，跳过")
        
        if not symbol_data:
            print("❌ 无有效数据")
            return {}
        
        print(f"✅ 成功加载 {len(symbol_data)} 个币种的数据")
        
        # 4. 创建策略并运行时间序列模拟
        print("\n🎯 开始动态仓位管理时间序列模拟...")

        # 可以通过参数控制分批止盈和输出详细程度
        enable_partial_profit = getattr(self, 'enable_partial_profit', False)
        verbose_level = getattr(self, 'verbose_level', 1)
        self.strategy = EMADynamicStrategy(
            initial_capital=self.initial_capital,
            enable_partial_profit=enable_partial_profit,
            verbose_level=verbose_level
        )
        results = self.strategy.simulate_real_time_trading(symbol_data)
        
        if 'error' in results:
            print(f"❌ 回测失败: {results['error']}")
            return {}
        
        # 5. 添加额外信息
        results.update({
            'strategy_name': 'EMA Dynamic Position Management',
            'initial_capital': self.initial_capital,
            'symbols_count': len(symbol_data),
            'symbols': list(symbol_data.keys()),
            'backtest_period': {
                'start': min(data.index[0] for data in symbol_data.values()).strftime('%Y-%m-%d'),
                'end': max(data.index[-1] for data in symbol_data.values()).strftime('%Y-%m-%d')
            }
        })
        
        self.results = results
        return results
    
    def generate_report(self, output_dir: str):
        """生成详细报告"""
        if not self.results:
            print("❌ 无回测结果")
            return
        
        report_path = f"{output_dir}/dynamic_backtest_report.txt"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("动态仓位管理回测报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"策略名称: {self.results['strategy_name']}\n")
            f.write(f"初始资金: {self.results['initial_capital']:,.0f} 元\n\n")
            
            # 回测概况
            f.write("📊 回测概况\n")
            f.write("-" * 30 + "\n")
            f.write(f"回测币种: {self.results['symbols_count']} 个\n")
            f.write(f"回测期间: {self.results['backtest_period']['start']} 至 {self.results['backtest_period']['end']}\n")
            f.write(f"总交易次数: {self.results['total_trades']}\n")
            f.write(f"胜率: {self.results['win_rate']:.1f}%\n\n")
            
            # 收益分析
            f.write("💰 收益分析\n")
            f.write("-" * 30 + "\n")
            f.write(f"总收益率: {self.results['total_return']:.2f}%\n")
            f.write(f"总收益率(按单位): {self.results['total_return_by_unit']:.2f}%\n")
            f.write(f"最终权益: {self.results['final_equity']:,.0f} 元\n")
            f.write(f"总盈亏: {self.results['total_pnl']:,.0f} 元\n\n")
            
            # 风险分析
            f.write("📉 风险分析\n")
            f.write("-" * 30 + "\n")
            f.write(f"最大回撤: {self.results['max_drawdown']:.2f}%\n")
            f.write(f"夏普比率: {self.results['sharpe_ratio']:.3f}\n")
            f.write(f"平均盈利: {self.results['avg_win']:,.0f} 元\n")
            f.write(f"平均亏损: {self.results['avg_loss']:,.0f} 元\n\n")
            
            # 交易明细
            f.write("📋 交易明细\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'序号':<4} {'币种':<12} {'入场时间':<12} {'出场时间':<12} {'收益率':<8} {'杠杆':<6} {'原因':<20}\n")
            f.write("-" * 80 + "\n")
            
            for i, trade in enumerate(self.results['trades'], 1):
                f.write(f"{i:<4} {trade['symbol']:<12} "
                       f"{trade['entry_time'].strftime('%m-%d %H:%M'):<12} "
                       f"{trade['exit_time'].strftime('%m-%d %H:%M'):<12} "
                       f"{trade['pnl_pct']:>6.1f}% {trade['leverage']:>4.1f}x "
                       f"{trade['exit_reason'][:18]:<20}\n")
            
            # 策略参数
            f.write("\n⚙️ 策略参数\n")
            f.write("-" * 30 + "\n")
            f.write("仓位管理:\n")
            f.write("- 仓位比例: 0.2倍杠杆\n")
            f.write("- 最低交易额: 2000元\n")
            f.write("- 最大杠杆: 5倍\n\n")
            
            f.write("EMA策略参数:\n")
            if hasattr(self.strategy, 'ema_strategy'):
                for key, value in self.strategy.ema_strategy.params.items():
                    f.write(f"- {key}: {value}\n")
        
        print(f"📋 详细报告已保存: {report_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='动态仓位管理回测系统')
    parser.add_argument('--capital', type=float, default=10000, help='初始资金（默认1万元）')
    parser.add_argument('--symbols', nargs='+', help='指定回测币种')
    parser.add_argument('--start-date', type=str, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--output-dir', type=str, default=None, help='输出目录')
    parser.add_argument('--enable-partial-profit', action='store_true', help='启用分批止盈（50%）')
    parser.add_argument('--verbose', type=int, default=1, choices=[0, 1, 2],
                       help='输出详细程度 (0=简洁, 1=标准, 2=详细)')
    
    args = parser.parse_args()
    
    # 创建输出目录
    if args.output_dir:
        output_dir = args.output_dir
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"backtest_results/dynamic_backtest_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)

    # 设置完整日志文件
    full_log_file = os.path.join(output_dir, 'full_backtest_log.txt')

    # 使用日志捕获运行回测
    with LogCapture(full_log_file):
        # 运行回测
        runner = DynamicBacktestRunner(initial_capital=args.capital)
        runner.enable_partial_profit = args.enable_partial_profit  # 传递分批止盈配置
        runner.verbose_level = args.verbose  # 传递输出详细程度配置
        results = runner.run_backtest(
            symbols=args.symbols,
            start_date=args.start_date,
            end_date=args.end_date
        )

        if results:
            # 合并策略统计数据到结果中
            if hasattr(runner.strategy, 'stats'):
                results.update(runner.strategy.stats)

            # 使用新的格式化器显示最终总结
            runner.strategy.formatter.print_final_summary(results, runner.strategy.trade_history)

            # 显示收益率图表
            if hasattr(runner.strategy, 'trade_history') and runner.strategy.trade_history:
                returns = [t.get('return', 0) for t in runner.strategy.trade_history if t.get('action') == '平仓']
                if returns:
                    runner.strategy.formatter.print_performance_chart(returns)

            # 保存详细总结到文件和生成图表
            summary_file = os.path.join(output_dir, 'detailed_summary.txt')
            runner.strategy.formatter.save_final_summary_to_file(results, runner.strategy.trade_history, summary_file)

            # 生成时间序列图表
            chart_file = os.path.join(output_dir, 'time_series_chart.png')
            print(f"\n📊 正在生成时间序列图表...")
            runner.strategy.formatter.create_time_series_chart(runner.strategy.trade_history, chart_file)

            # 生成报告
            runner.generate_report(output_dir)

            # 保存原始数据
            results_file = f"{output_dir}/dynamic_backtest_results.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                # 处理datetime对象
                serializable_results = results.copy()
                if 'trades' in serializable_results:
                    for trade in serializable_results['trades']:
                        if 'entry_time' in trade:
                            trade['entry_time'] = trade['entry_time'].isoformat()
                        if 'exit_time' in trade:
                            trade['exit_time'] = trade['exit_time'].isoformat()

                json.dump(serializable_results, f, indent=2, ensure_ascii=False)

            print(f"\n📁 所有结果已保存到: {output_dir}")
        else:
            print("❌ 回测失败")


if __name__ == "__main__":
    main()
