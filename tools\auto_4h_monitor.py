#!/usr/bin/env python3
"""
4小时K线自动监控系统
4-Hour Kline Auto Monitor System

在每个4小时K线结束后立即更新数据并进行策略验证
"""

import asyncio
import json
import logging
import sqlite3
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import pandas as pd

# 添加项目路径
import sys
sys.path.append(str(Path(__file__).parent))

from src.core.config_manager import ConfigManager
from src.strategies import get_strategy, list_strategies
from src.services.signal_generator import StrategySignalGenerator
from src.services.execution_manager import ExecutionManager, LogNotifier
from src.data_layer.historical_data_fetcher import (
    USDTHistoricalFetcher,
    save_klines,
    scan_local_databases,
    analyze_data_gaps
)

class Auto4HMonitor:
    """4小时K线自动监控系统"""
    
    def __init__(self, config_path: str = "./config", db_path: str = "./data/usdt_historical_data.db"):
        """
        初始化监控系统
        
        Args:
            config_path: 配置文件路径
            db_path: 本地数据库路径
        """
        # 加载配置
        self.config = ConfigManager(config_path)
        self.config.setup_logging()
        
        # 数据库路径
        self.db_path = Path(db_path)
        self.table_name = "usdt_klines_historical_4h"
        
        # 初始化组件
        self.signal_generator = None
        self.execution_manager = None
        self.data_fetcher = None
        
        # 系统状态
        self.is_running = False
        self.last_update_time = None
        self.last_kline_time = None
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
        # 监控的交易对（动态获取）
        self.symbols = []

        # 加载监控配置
        self.monitor_config = self._load_monitor_config()
        self.top_n_symbols = self.monitor_config.get("max_symbols", 50)
        self.min_volume = self.monitor_config.get("min_volume_usdt", 1000000)
        self.priority_symbols = self.monitor_config.get("priority_symbols", [])
        self.exclude_symbols = set(self.monitor_config.get("exclude_symbols", []))
        
    async def initialize(self):
        """初始化系统组件"""
        try:
            self.logger.info("🚀 初始化4小时K线监控系统...")
            
            # 1. 检查数据库
            if not self.db_path.exists():
                self.logger.error(f"❌ 数据库文件不存在: {self.db_path}")
                raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
            
            # 2. 初始化信号生成器
            self.signal_generator = StrategySignalGenerator()
            
            # 3. 初始化执行管理器（仅日志模式）
            self.execution_manager = ExecutionManager(self.signal_generator)
            self.execution_manager.simulation_mode = True
            self.execution_manager.auto_execution = False
            
            # 添加日志通知器
            log_notifier = LogNotifier()
            self.execution_manager.add_notifier(log_notifier)
            
            # 4. 初始化数据获取器
            self.data_fetcher = USDTHistoricalFetcher(use_proxy=False, batch_delay=0.3)
            
            # 5. 设置策略
            await self._setup_strategies()
            
            # 6. 设置回调函数
            self._setup_callbacks()
            
            # 7. 获取监控交易对
            await self._setup_symbols()

            # 8. 获取当前K线时间
            self.last_kline_time = self._get_current_4h_kline_time()

            self.logger.info("✅ 系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 系统初始化失败: {e}")
            raise

    def _load_monitor_config(self) -> dict:
        """加载监控配置"""
        try:
            config_file = Path("./config/auto_monitor.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return config.get("monitor_settings", {})
            else:
                self.logger.warning("⚠️ 监控配置文件不存在，使用默认配置")
                return {}
        except Exception as e:
            self.logger.error(f"❌ 加载监控配置失败: {e}")
            return {}

    def _load_data_fetcher_config(self, preset_name: str = None) -> dict:
        """
        加载数据获取器配置，构造与 get_user_input() 相同格式的config字典

        Args:
            preset_name: 预设名称 ('real_time_update', 'historical_validation', 等)

        Returns:
            dict: 与 historical_data_fetcher.py 中 get_user_input() 相同格式的配置
        """
        try:
            config_file = Path("./config/data_fetcher_config.json")
            if not config_file.exists():
                self.logger.error("❌ 数据获取器配置文件不存在")
                return self._get_default_fetcher_config()

            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)

            # 如果指定了预设，使用预设配置
            if preset_name and preset_name in file_config.get("presets", {}):
                preset_config = file_config["presets"][preset_name]
                self.logger.info(f"📋 使用预设配置: {preset_name}")

                # 创建合并后的配置
                merged_config = {}

                # 1. 先复制基础配置
                for key in ["time_range", "symbol_selection", "kline_settings", "fetcher_settings", "auto_mode_settings"]:
                    if key in file_config:
                        merged_config[key] = file_config[key].copy()

                # 2. 用预设配置覆盖
                for key, value in preset_config.items():
                    if key in merged_config and isinstance(merged_config[key], dict) and isinstance(value, dict):
                        merged_config[key].update(value)
                    else:
                        merged_config[key] = value

                file_config = merged_config

            # 构造与 get_user_input() 相同格式的配置
            config = {}

            # 1. 时间范围配置
            time_config = file_config.get("time_range", {})

            if time_config.get("mode") == "custom_range":
                # 自定义日期范围
                start_date = time_config.get("start_date")
                end_date = time_config.get("end_date")

                config['start_time'] = datetime.strptime(start_date, '%Y-%m-%d')
                config['end_time'] = datetime.strptime(end_date, '%Y-%m-%d')
                config['days'] = (config['end_time'] - config['start_time']).days
                config['time_label'] = time_config.get("time_label", f"{start_date}_to_{end_date}")
                config['custom_range'] = True
            else:
                # 基于天数的配置
                config['days'] = time_config.get("days", 2)
                config['time_label'] = time_config.get("time_label", f"{config['days']}days")
                config['end_time'] = datetime.now()
                config['start_time'] = config['end_time'] - timedelta(days=config['days'])
                config['custom_range'] = False

            # 2. 币种选择配置
            symbol_config = file_config.get("symbol_selection", {})
            config['selection_mode'] = symbol_config.get("mode", "top_n")

            if config['selection_mode'] == 'top_n':
                config['top_n'] = symbol_config.get("top_n", 50)
            elif config['selection_mode'] == 'specific':
                config['symbols'] = symbol_config.get("symbols", "BTC,ETH,BNB")

            config['min_volume'] = symbol_config.get("min_volume", 1000000)

            # 3. K线配置
            kline_config = file_config.get("kline_settings", {})
            config['interval'] = kline_config.get("interval", "4h")

            # 4. 获取器配置
            fetcher_config = file_config.get("fetcher_settings", {})
            config['use_proxy'] = fetcher_config.get("use_proxy", False)
            config['proxy_port'] = fetcher_config.get("proxy_port", 6754)
            config['output_dir'] = fetcher_config.get("output_dir", "./data")

            # 5. 扫描本地数据库
            config['local_databases'] = scan_local_databases()

            self.logger.info(f"✅ 加载数据获取器配置成功")
            self.logger.info(f"  时间范围: {config['days']} 天 ({config['start_time'].strftime('%Y-%m-%d')} 到 {config['end_time'].strftime('%Y-%m-%d')})")
            self.logger.info(f"  币种选择: {config['selection_mode']} ({config.get('top_n', config.get('symbols', 'N/A'))})")
            self.logger.info(f"  K线间隔: {config['interval']}")

            return config

        except Exception as e:
            self.logger.error(f"❌ 加载数据获取器配置失败: {e}")
            return self._get_default_fetcher_config()

    def _get_default_fetcher_config(self) -> dict:
        """获取默认的数据获取器配置"""
        return {
            'days': 2,
            'time_label': '2days',
            'end_time': datetime.now(),
            'start_time': datetime.now() - timedelta(days=2),
            'custom_range': False,
            'selection_mode': 'top_n',
            'top_n': 30,
            'min_volume': 1000000,
            'interval': '4h',
            'use_proxy': False,
            'proxy_port': 6754,
            'output_dir': './data',
            'local_databases': scan_local_databases()
        }
    
    async def _setup_symbols(self):
        """设置监控交易对"""
        try:
            self.logger.info("🔍 获取监控交易对...")

            # 1. 首先检查数据库中已有的交易对
            existing_symbols = self._get_existing_symbols_from_db()

            if existing_symbols and len(existing_symbols) >= 10:
                # 如果数据库中有足够的交易对，智能选择
                self.symbols = self._select_optimal_symbols(existing_symbols)
                self.logger.info(f"✅ 从数据库中选择 {len(self.symbols)} 个交易对")
            else:
                # 如果数据库中交易对不足，获取交易量前N的交易对
                self.logger.info(f"📊 数据库中交易对不足，获取交易量前{self.top_n_symbols}的USDT交易对...")

                # 获取实时交易对数据
                usdt_pairs = self.data_fetcher.get_usdt_pairs_current(min_volume=self.min_volume)

                if usdt_pairs:
                    # 选择前N个交易对，但排除稳定币
                    filtered_pairs = {k: v for k, v in usdt_pairs.items()
                                    if k not in self.exclude_symbols}

                    # 优先选择配置中的优先交易对
                    selected_symbols = []

                    # 首先添加优先交易对（如果存在）
                    for symbol in self.priority_symbols:
                        if symbol in filtered_pairs and len(selected_symbols) < self.top_n_symbols:
                            selected_symbols.append(symbol)

                    # 然后按交易量添加其他交易对
                    remaining_pairs = {k: v for k, v in filtered_pairs.items()
                                     if k not in selected_symbols}

                    for symbol in list(remaining_pairs.keys())[:self.top_n_symbols - len(selected_symbols)]:
                        selected_symbols.append(symbol)

                    self.symbols = selected_symbols

                    self.logger.info(f"✅ 获取到 {len(self.symbols)} 个高交易量USDT交易对")

                    # 显示前10个交易对
                    for i, symbol in enumerate(self.symbols[:10], 1):
                        if symbol in filtered_pairs:
                            volume = filtered_pairs[symbol]['quoteVolume']
                            priority_mark = "⭐" if symbol in self.priority_symbols else "  "
                            self.logger.info(f"  {priority_mark}{i:2d}. {symbol:<12} (24h交易量: {volume:>12,.0f} USDT)")

                    if len(self.symbols) > 10:
                        self.logger.info(f"  ... 和其他 {len(self.symbols)-10} 个交易对")
                else:
                    # 如果获取失败，使用优先交易对
                    self.symbols = self.priority_symbols[:self.top_n_symbols]
                    self.logger.warning(f"⚠️ 获取实时数据失败，使用优先交易对 {len(self.symbols)} 个")

            # 显示最终监控的交易对
            self.logger.info(f"📋 最终监控交易对: {', '.join(self.symbols[:5])}{'...' if len(self.symbols) > 5 else ''} (共{len(self.symbols)}个)")

        except Exception as e:
            self.logger.error(f"❌ 设置监控交易对失败: {e}")
            # 使用默认交易对
            self.symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "XRPUSDT", "SOLUSDT"]
            self.logger.warning(f"⚠️ 使用默认交易对: {', '.join(self.symbols)}")

    def _get_existing_symbols_from_db(self) -> List[str]:
        """从数据库获取已有的交易对"""
        try:
            conn = sqlite3.connect(str(self.db_path))

            query = f"""
            SELECT DISTINCT symbol
            FROM {self.table_name}
            ORDER BY symbol
            """

            cursor = conn.execute(query)
            symbols = [row[0] for row in cursor.fetchall()]
            conn.close()

            return symbols

        except Exception as e:
            self.logger.error(f"❌ 获取数据库交易对失败: {e}")
            return []

    def _select_optimal_symbols(self, existing_symbols: List[str]) -> List[str]:
        """从现有交易对中智能选择最优的交易对"""
        try:
            # 1. 优先选择配置中的优先交易对
            selected_symbols = []

            for symbol in self.priority_symbols:
                if symbol in existing_symbols and len(selected_symbols) < self.top_n_symbols:
                    selected_symbols.append(symbol)

            # 2. 排除稳定币
            filtered_symbols = [s for s in existing_symbols
                              if s not in self.exclude_symbols and s not in selected_symbols]

            # 3. 获取每个交易对的数据量和最新时间
            conn = sqlite3.connect(str(self.db_path))

            symbol_stats = []
            for symbol in filtered_symbols:
                query = f"""
                SELECT COUNT(*) as count, MAX(open_time) as latest_time
                FROM {self.table_name}
                WHERE symbol = ?
                """
                cursor = conn.execute(query, (symbol,))
                result = cursor.fetchone()

                if result[0] > 0:  # 有数据的交易对
                    symbol_stats.append({
                        'symbol': symbol,
                        'count': result[0],
                        'latest_time': result[1] or 0
                    })

            conn.close()

            # 4. 按数据量和新鲜度排序
            symbol_stats.sort(key=lambda x: (x['count'], x['latest_time']), reverse=True)

            # 5. 选择剩余的交易对
            remaining_slots = self.top_n_symbols - len(selected_symbols)
            for stat in symbol_stats[:remaining_slots]:
                selected_symbols.append(stat['symbol'])

            self.logger.info(f"📊 智能选择: 优先交易对 {len([s for s in selected_symbols if s in self.priority_symbols])} 个, "
                           f"其他交易对 {len(selected_symbols) - len([s for s in selected_symbols if s in self.priority_symbols])} 个")

            return selected_symbols

        except Exception as e:
            self.logger.error(f"❌ 智能选择交易对失败: {e}")
            # 如果失败，返回前N个现有交易对
            return existing_symbols[:self.top_n_symbols]

    async def _setup_strategies(self):
        """设置策略"""
        strategy_config = self.config.get_strategy_config()

        # 为每个交易对添加策略
        for symbol in self.symbols:
            # 为每个启用的策略添加到信号生成器
            for strategy_name in strategy_config["enabled_strategies"]:
                # 获取自定义参数
                custom_params = strategy_config["custom_params"].get(strategy_name, {})

                # 添加策略
                self.signal_generator.add_strategy(symbol, strategy_name, custom_params)

                self.logger.info(f"🎯 为 {symbol} 添加策略: {strategy_name}")

        # 更新策略权重
        self.signal_generator.strategy_weights.update(strategy_config["strategy_weights"])
    
    def _setup_callbacks(self):
        """设置回调函数"""
        # 设置信号回调
        self.signal_generator.on_signal = self._on_signal_generated
    
    async def _on_signal_generated(self, signal: Dict[str, Any]):
        """信号生成回调"""
        self.logger.info(f"📈 生成信号: {signal['symbol']} - {signal['action']} (置信度: {signal['confidence']:.1f}%)")
        
        # 发送给执行管理器
        await self.execution_manager.process_signal(signal)
    
    def _get_current_4h_kline_time(self) -> datetime:
        """获取当前4小时K线的开始时间"""
        now = datetime.now()
        
        # 4小时K线的开始时间：0, 4, 8, 12, 16, 20
        hour = now.hour
        kline_start_hour = (hour // 4) * 4
        
        kline_time = now.replace(hour=kline_start_hour, minute=0, second=0, microsecond=0)
        return kline_time
    
    def _get_next_4h_kline_time(self) -> datetime:
        """获取下一个4小时K线的开始时间"""
        current_kline = self._get_current_4h_kline_time()
        return current_kline + timedelta(hours=4)
    
    def _seconds_until_next_kline(self) -> int:
        """计算距离下一个4小时K线开始的秒数"""
        next_kline = self._get_next_4h_kline_time()
        now = datetime.now()
        return int((next_kline - now).total_seconds())
    
    def get_latest_data(self, symbol: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """从本地数据库获取最新数据"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            
            query = f"""
            SELECT symbol, open_time, open_price, high_price, low_price, close_price, volume, datetime_str
            FROM {self.table_name}
            WHERE symbol = ?
            ORDER BY open_time DESC
            LIMIT ?
            """
            
            df = pd.read_sql_query(query, conn, params=(symbol, limit))
            conn.close()
            
            if df.empty:
                return None
            
            # 转换数据格式
            df = df.rename(columns={
                'open_price': 'open',
                'high_price': 'high', 
                'low_price': 'low',
                'close_price': 'close'
            })
            
            # 转换时间
            df['datetime'] = pd.to_datetime(df['datetime_str'])
            df = df.sort_values('open_time').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 获取 {symbol} 数据失败: {e}")
            return None
    
    async def update_data_using_fetcher(self, preset_name: str = "real_time_update"):
        """
        使用 historical_data_fetcher.py 的功能更新数据

        Args:
            preset_name: 使用的配置预设名称
        """
        try:
            self.logger.info("🔄 使用数据获取器更新数据...")

            # 1. 加载配置
            config = self._load_data_fetcher_config(preset_name)

            # 2. 分析数据缺口
            gap_analysis = analyze_data_gaps(config, config['local_databases'])

            # 3. 初始化获取器
            fetcher = USDTHistoricalFetcher(
                proxy_port=config.get('proxy_port', 6754),
                use_proxy=config['use_proxy'],
                batch_delay=0.5
            )

            # 4. 获取USDT交易对实时数据
            self.logger.info("📊 获取USDT交易对实时数据...")
            usdt_pairs = fetcher.get_usdt_pairs_current(config['min_volume'])

            if not usdt_pairs:
                self.logger.error("❌ 无法获取USDT交易对数据")
                return False

            # 5. 选择币种
            selected_pairs = fetcher.select_symbols(usdt_pairs, config)

            if not selected_pairs:
                self.logger.error("❌ 没有选择到任何币种")
                return False

            self.logger.info(f"✅ 选择了 {len(selected_pairs)} 个交易对进行更新")

            # 6. 获取历史数据
            self.logger.info("📈 开始获取历史K线数据...")

            all_klines = fetcher.batch_get_historical_data(
                list(selected_pairs.keys()),
                interval=config['interval'],
                start_time=config['start_time'],
                end_time=config['end_time']
            )

            if all_klines:
                # 7. 保存到数据库
                self.logger.info(f"💾 保存 {len(all_klines)} 条K线数据到数据库...")

                conn = sqlite3.connect(str(self.db_path))
                save_klines(conn, all_klines, self.table_name)
                conn.close()

                self.logger.info(f"✅ 数据更新完成")
                self.last_update_time = datetime.now()

                # 8. 更新监控的交易对列表
                self.symbols = list(selected_pairs.keys())

                return True
            else:
                self.logger.warning("⚠️ 未获取到新数据")
                return False

        except Exception as e:
            self.logger.error(f"❌ 使用数据获取器更新失败: {e}")
            return False

    async def update_data(self):
        """更新最新的4小时K线数据（简化版本，用于定期更新）"""
        try:
            self.logger.info("🔄 更新最新4小时K线数据...")

            # 检查哪些交易对需要更新
            symbols_need_update = self._check_symbols_need_update()

            if not symbols_need_update:
                self.logger.info("✅ 所有交易对数据都是最新的")
                return True

            self.logger.info(f"📋 需要更新的交易对: {len(symbols_need_update)} 个")

            # 获取最近2个4小时周期的数据（确保完整性）
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=8)

            # 分批获取数据，避免API限制
            all_klines = []
            batch_size = 10

            for i in range(0, len(symbols_need_update), batch_size):
                batch_symbols = symbols_need_update[i:i+batch_size]

                self.logger.info(f"🔄 获取批次 {i//batch_size + 1}: {', '.join(batch_symbols)}")

                batch_klines = self.data_fetcher.batch_get_historical_data(
                    batch_symbols,
                    interval='4h',
                    start_time=start_time,
                    end_time=end_time
                )

                if batch_klines:
                    all_klines.extend(batch_klines)

                # 批次间延迟
                if i + batch_size < len(symbols_need_update):
                    await asyncio.sleep(2)

            if all_klines:
                # 保存到数据库
                conn = sqlite3.connect(str(self.db_path))
                save_klines(conn, all_klines, self.table_name)
                conn.close()

                self.logger.info(f"✅ 数据更新完成，处理 {len(all_klines)} 条记录")
                self.last_update_time = datetime.now()
                return True
            else:
                self.logger.warning("⚠️ 未获取到新数据")
                return False

        except Exception as e:
            self.logger.error(f"❌ 数据更新失败: {e}")
            return False

    def _check_symbols_need_update(self) -> List[str]:
        """检查哪些交易对需要更新"""
        try:
            conn = sqlite3.connect(str(self.db_path))

            symbols_need_update = []
            current_time = datetime.now()

            for symbol in self.symbols:
                # 获取该交易对的最新数据时间
                query = f"""
                SELECT MAX(open_time) as latest_time
                FROM {self.table_name}
                WHERE symbol = ?
                """

                cursor = conn.execute(query, (symbol,))
                result = cursor.fetchone()

                if result[0]:
                    latest_timestamp = result[0]
                    latest_time = datetime.fromtimestamp(latest_timestamp / 1000)
                    time_diff = current_time - latest_time

                    # 如果超过5小时没有更新，则需要更新
                    if time_diff > timedelta(hours=5):
                        symbols_need_update.append(symbol)
                else:
                    # 没有数据的交易对需要更新
                    symbols_need_update.append(symbol)

            conn.close()
            return symbols_need_update

        except Exception as e:
            self.logger.error(f"❌ 检查更新需求失败: {e}")
            return self.symbols  # 如果检查失败，更新所有交易对
    
    async def validate_strategies(self):
        """验证策略并生成信号"""
        try:
            self.logger.info("🧪 开始策略验证...")
            
            signal_count = 0
            
            for symbol in self.symbols:
                # 获取最新数据
                df = self.get_latest_data(symbol, limit=200)
                
                if df is None or len(df) < 50:
                    self.logger.warning(f"⚠️ {symbol} 数据不足，跳过验证")
                    continue
                
                self.logger.info(f"📊 验证 {symbol} 策略 (数据量: {len(df)} 条)")
                
                # 手动调用策略验证（因为process_data方法可能不存在）
                try:
                    # 获取该交易对的策略
                    symbol_strategies = self.signal_generator.strategies.get(symbol, [])
                    
                    for strategy_info in symbol_strategies:
                        strategy = strategy_info['strategy']
                        weight = strategy_info['weight']
                        
                        # 生成信号
                        signal = strategy.generate_signal(df)
                        
                        if signal and signal.get('action') != 'HOLD':
                            # 应用权重
                            signal['confidence'] *= weight
                            signal['symbol'] = symbol
                            signal['timestamp'] = datetime.now()
                            signal['strategy'] = strategy.__class__.__name__
                            
                            # 触发信号回调
                            await self._on_signal_generated(signal)
                            signal_count += 1
                            
                except Exception as e:
                    self.logger.error(f"❌ {symbol} 策略验证失败: {e}")
                    continue
            
            self.logger.info(f"✅ 策略验证完成，生成 {signal_count} 个信号")
            return signal_count
            
        except Exception as e:
            self.logger.error(f"❌ 策略验证失败: {e}")
            return 0
    
    async def process_new_kline(self):
        """处理新的4小时K线"""
        current_kline_time = self._get_current_4h_kline_time()

        if self.last_kline_time and current_kline_time <= self.last_kline_time:
            # 还在同一个K线周期内
            return

        self.logger.info(f"🕐 检测到新的4小时K线: {current_kline_time.strftime('%Y-%m-%d %H:%M')}")

        # 更新最后K线时间
        self.last_kline_time = current_kline_time

        # 1. 使用数据获取器更新数据
        update_success = await self.update_data_using_fetcher("real_time_update")

        if update_success:
            # 2. 验证策略
            signal_count = await self.validate_strategies()

            # 3. 生成报告
            await self.generate_kline_report(signal_count)
        else:
            self.logger.warning("⚠️ 数据更新失败，跳过策略验证")

    async def run_historical_validation(self, start_date: str = None, end_date: str = None):
        """
        运行历史数据验证

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
        """
        try:
            self.logger.info("🧪 开始历史数据验证...")

            if start_date and end_date:
                # 创建自定义配置
                custom_config = {
                    "time_range": {
                        "mode": "custom_range",
                        "start_date": start_date,
                        "end_date": end_date,
                        "time_label": f"{start_date}_to_{end_date}"
                    },
                    "symbol_selection": {
                        "mode": "top_n",
                        "top_n": 100,
                        "min_volume": 500000
                    }
                }

                # 临时保存自定义配置
                temp_config_file = Path("./config/temp_validation_config.json")
                with open(temp_config_file, 'w', encoding='utf-8') as f:
                    json.dump({"presets": {"temp_validation": custom_config}}, f, indent=2)

                # 使用自定义配置更新数据
                update_success = await self.update_data_using_fetcher("temp_validation")

                # 清理临时配置文件
                if temp_config_file.exists():
                    temp_config_file.unlink()
            else:
                # 使用历史验证预设
                update_success = await self.update_data_using_fetcher("historical_validation")

            if update_success:
                # 运行策略验证
                signal_count = await self.validate_strategies()

                self.logger.info(f"✅ 历史验证完成，生成 {signal_count} 个信号")

                # 生成详细报告
                await self.generate_validation_report(start_date, end_date, signal_count)

                return True
            else:
                self.logger.error("❌ 历史数据获取失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 历史验证失败: {e}")
            return False
    
    async def generate_kline_report(self, signal_count: int):
        """生成K线处理报告"""
        try:
            current_time = datetime.now()
            kline_time = self._get_current_4h_kline_time()
            
            report = f"""
📊 **4小时K线处理报告** - {current_time.strftime('%Y-%m-%d %H:%M:%S')}

**K线信息**:
• K线时间: {kline_time.strftime('%Y-%m-%d %H:%M')} - {(kline_time + timedelta(hours=4)).strftime('%H:%M')}
• 处理时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}
• 监控交易对: {len(self.symbols)} 个

**处理结果**:
• 数据更新: {'✅ 成功' if self.last_update_time else '❌ 失败'}
• 生成信号: {signal_count} 个
• 最后更新: {self.last_update_time.strftime('%H:%M:%S') if self.last_update_time else '未更新'}

**下次处理**:
• 下个K线: {self._get_next_4h_kline_time().strftime('%Y-%m-%d %H:%M')}
• 等待时间: {self._seconds_until_next_kline() // 60} 分钟
"""
            
            self.logger.info(report)
            
        except Exception as e:
            self.logger.error(f"❌ 生成报告失败: {e}")

    async def generate_validation_report(self, start_date: str, end_date: str, signal_count: int):
        """生成历史验证报告"""
        try:
            current_time = datetime.now()

            # 获取数据统计
            data_stats = self._get_data_statistics()

            report = f"""
📊 **历史数据验证报告** - {current_time.strftime('%Y-%m-%d %H:%M:%S')}

**验证范围**:
• 开始日期: {start_date or '使用预设配置'}
• 结束日期: {end_date or '使用预设配置'}
• 验证时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}

**数据统计**:
• 监控交易对: {len(self.symbols)} 个
• 总数据量: {data_stats.get('total_records', 0):,} 条K线记录
• 数据覆盖: {data_stats.get('date_range', 'N/A')}

**验证结果**:
• 生成信号: {signal_count} 个
• 策略覆盖: {len(self.symbols)} 个交易对
• 验证状态: {'✅ 成功' if signal_count >= 0 else '❌ 失败'}

**交易对列表**:
{self._format_symbols_list()}
"""

            self.logger.info(report)

        except Exception as e:
            self.logger.error(f"❌ 生成验证报告失败: {e}")

    def _get_data_statistics(self) -> dict:
        """获取数据统计信息"""
        try:
            conn = sqlite3.connect(str(self.db_path))

            # 获取总记录数
            cursor = conn.execute(f"SELECT COUNT(*) FROM {self.table_name}")
            total_records = cursor.fetchone()[0]

            # 获取日期范围
            cursor = conn.execute(f"""
                SELECT MIN(datetime_str), MAX(datetime_str)
                FROM {self.table_name}
            """)
            date_range_result = cursor.fetchone()

            if date_range_result[0] and date_range_result[1]:
                date_range = f"{date_range_result[0][:10]} 到 {date_range_result[1][:10]}"
            else:
                date_range = "无数据"

            conn.close()

            return {
                'total_records': total_records,
                'date_range': date_range
            }

        except Exception as e:
            self.logger.error(f"❌ 获取数据统计失败: {e}")
            return {'total_records': 0, 'date_range': 'N/A'}

    def _format_symbols_list(self) -> str:
        """格式化交易对列表"""
        if not self.symbols:
            return "• 无监控交易对"

        # 每行显示5个交易对
        lines = []
        for i in range(0, len(self.symbols), 5):
            batch = self.symbols[i:i+5]
            lines.append("• " + ", ".join(batch))

        return "\n".join(lines)
    
    async def start(self):
        """启动监控系统"""
        if self.is_running:
            self.logger.warning("⚠️ 系统已在运行中")
            return
        
        try:
            # 初始化系统
            await self.initialize()
            
            # 启动执行管理器
            self.execution_manager.start()
            
            self.is_running = True
            
            self.logger.info("🚀 4小时K线监控系统已启动")
            
            # 显示当前状态
            current_kline = self._get_current_4h_kline_time()
            next_kline = self._get_next_4h_kline_time()
            wait_seconds = self._seconds_until_next_kline()
            
            self.logger.info(f"📅 当前K线: {current_kline.strftime('%Y-%m-%d %H:%M')}")
            self.logger.info(f"📅 下个K线: {next_kline.strftime('%Y-%m-%d %H:%M')}")
            self.logger.info(f"⏰ 等待时间: {wait_seconds // 60} 分 {wait_seconds % 60} 秒")
            
            # 执行初始处理
            await self.process_new_kline()
            
            # 开始监控循环
            await self._monitoring_loop()
            
        except Exception as e:
            self.logger.error(f"❌ 启动失败: {e}")
            await self.stop()
            raise
    
    async def _monitoring_loop(self):
        """监控循环"""
        self.logger.info("⏰ 开始4小时K线监控循环")
        
        try:
            while self.is_running:
                # 检查是否有新的K线
                await self.process_new_kline()
                
                # 每分钟检查一次
                await asyncio.sleep(60)
                
        except Exception as e:
            self.logger.error(f"❌ 监控循环错误: {e}")
    
    async def stop(self):
        """停止监控系统"""
        if not self.is_running:
            return
        
        self.logger.info("🛑 正在停止监控系统...")
        
        self.is_running = False
        
        if self.execution_manager:
            self.execution_manager.stop()
        
        self.logger.info("✅ 监控系统已停止")

async def main():
    """主函数"""
    print("🚀 启动4小时K线自动监控系统")
    print("=" * 50)
    
    # 创建监控系统
    monitor = Auto4HMonitor()
    
    try:
        # 启动系统
        await monitor.start()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在停止系统...")
    except Exception as e:
        print(f"❌ 系统运行出错: {e}")
    finally:
        await monitor.stop()
        print("👋 系统已退出")

if __name__ == "__main__":
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 程序异常退出: {e}")
