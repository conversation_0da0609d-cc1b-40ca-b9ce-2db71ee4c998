"""
标准化日志配置模块
替代print语句，提供结构化的日志记录
"""
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional
from src.config_manager import config_manager


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
    }
    RESET = '\033[0m'
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.RESET}"
        
        return super().format(record)


def setup_logger(
    name: str = 'TradeApiAlert',
    log_file: Optional[str] = None,
    console_output: bool = True
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径（可选）
        console_output: 是否输出到控制台
    
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复配置
    if logger.handlers:
        return logger
    
    # 设置日志级别
    log_level = getattr(logging, config_manager.get_log_level(), logging.INFO)
    logger.setLevel(log_level)
    
    # 日志格式
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 彩色格式化器（仅用于控制台）
    colored_formatter = ColoredFormatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(colored_formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str = None) -> logging.Logger:
    """
    获取日志记录器实例
    
    Args:
        name: 模块名称，如果为None则使用调用者的模块名
    
    Returns:
        日志记录器实例
    """
    if name is None:
        # 获取调用者的模块名
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'TradeApiAlert')
    
    return logging.getLogger(name)


# 设置主日志记录器
main_logger = setup_logger(
    name='TradeApiAlert',
    log_file='logs/trade_api_alert.log',
    console_output=True
)


# 为不同模块设置专用日志记录器
def setup_module_loggers():
    """设置各模块的专用日志记录器"""
    modules = [
        'TradeApiAlert.volume_monitor',
        'TradeApiAlert.ema_trend',
        'TradeApiAlert.position_tracker',
        'TradeApiAlert.notification',
        'TradeApiAlert.trading',
        'TradeApiAlert.data_fetcher',
    ]
    
    for module_name in modules:
        setup_logger(
            name=module_name,
            log_file=f'logs/{module_name.split(".")[-1]}.log',
            console_output=config_manager.is_debug()
        )


# 日志记录装饰器
def log_function_call(logger: logging.Logger = None):
    """
    装饰器：记录函数调用
    
    Args:
        logger: 指定的日志记录器，如果为None则使用默认记录器
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            nonlocal logger
            if logger is None:
                logger = get_logger(func.__module__)
            
            func_name = func.__name__
            logger.debug(f"调用函数: {func_name}")
            
            try:
                result = func(*args, **kwargs)
                logger.debug(f"函数 {func_name} 执行成功")
                return result
            except Exception as e:
                logger.error(f"函数 {func_name} 执行失败: {str(e)}")
                raise
        
        return wrapper
    return decorator


# 错误处理装饰器
def handle_exceptions(logger: logging.Logger = None, reraise: bool = True):
    """
    装饰器：统一异常处理
    
    Args:
        logger: 指定的日志记录器
        reraise: 是否重新抛出异常
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            nonlocal logger
            if logger is None:
                logger = get_logger(func.__module__)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"函数 {func.__name__} 发生异常: {str(e)}", exc_info=True)
                if reraise:
                    raise
                return None
        
        return wrapper
    return decorator


# 初始化模块日志记录器
setup_module_loggers()

# 导出常用的日志记录器
logger = main_logger 