#!/usr/bin/env python3
"""
综合策略测试脚本
Comprehensive Strategy Testing Script

测试所有策略的功能和回测性能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.strategies import (
    get_strategy, list_strategies, BacktestEngine,
    CRSIStrategy, EMAStrategy, RSIStrategy, BollingerStrategy
)

def generate_sample_data(days=365, interval_hours=4):
    """生成示例数据用于测试"""
    print("📊 生成示例数据...")
    
    # 生成时间序列
    start_date = datetime.now() - timedelta(days=days)
    periods = days * 24 // interval_hours
    dates = pd.date_range(start=start_date, periods=periods, freq=f'{interval_hours}H')
    
    # 生成价格数据（随机游走 + 趋势）
    np.random.seed(42)  # 确保可重复性
    
    # 基础价格
    base_price = 100.0
    
    # 生成收益率（带趋势和波动）
    trend = np.linspace(0, 0.5, periods)  # 轻微上升趋势
    noise = np.random.normal(0, 0.02, periods)  # 2%的随机波动
    returns = trend + noise
    
    # 计算价格
    prices = [base_price]
    for i in range(1, periods):
        new_price = prices[-1] * (1 + returns[i])
        prices.append(new_price)
    
    # 生成OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # 生成高低价
        volatility = abs(returns[i]) * 2
        high = close * (1 + volatility * np.random.uniform(0, 1))
        low = close * (1 - volatility * np.random.uniform(0, 1))
        
        # 生成开盘价
        if i == 0:
            open_price = close
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.005))
        
        # 生成成交量
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'datetime': date,
            'open': open_price,
            'high': max(open_price, high, close),
            'low': min(open_price, low, close),
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    print(f"✅ 生成了 {len(df)} 个数据点，时间范围: {df.index[0]} 到 {df.index[-1]}")
    return df

def test_strategy_basic_functionality(strategy_class, strategy_name):
    """测试策略基本功能"""
    print(f"\n🔧 测试 {strategy_name} 策略基本功能...")
    
    try:
        # 创建策略实例
        strategy = strategy_class({}, None)
        
        # 测试默认参数
        default_params = strategy_class.get_default_parameters()
        print(f"  ✅ 默认参数: {len(default_params)} 个参数")
        
        # 测试必需列
        required_columns = strategy_class.get_required_columns()
        print(f"  ✅ 必需列: {required_columns}")
        
        # 测试策略描述
        description = strategy.get_strategy_description()
        print(f"  ✅ 策略描述长度: {len(description)} 字符")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 基本功能测试失败: {e}")
        return False

def test_strategy_with_data(strategy_class, strategy_name, data):
    """使用数据测试策略"""
    print(f"\n📈 测试 {strategy_name} 策略数据处理...")
    
    try:
        # 创建策略实例
        strategy = strategy_class({}, None)
        
        # 加载数据
        strategy.load_data(data)
        
        # 检查指标计算
        if not strategy.indicators.empty:
            print(f"  ✅ 指标计算: {len(strategy.indicators.columns)} 个指标")
        else:
            print(f"  ⚠️  指标为空")
            return False
        
        # 检查信号生成
        if not strategy.signals.empty:
            signal_counts = strategy.signals['signal'].value_counts()
            print(f"  ✅ 信号生成: {len(strategy.signals)} 个信号")
            print(f"     信号分布: {dict(signal_counts)}")
        else:
            print(f"  ⚠️  信号为空")
            return False
        
        # 测试获取当前信号
        current_signal = strategy.get_signal()
        print(f"  ✅ 当前信号: {current_signal['signal']} (置信度: {current_signal['confidence']})")
        
        # 测试获取当前指标
        current_indicators = strategy.get_current_indicators(strategy.indicators)
        print(f"  ✅ 当前指标: {len(current_indicators)} 个")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_strategy_backtest(strategy_class, strategy_name, data):
    """运行策略回测"""
    print(f"\n💰 运行 {strategy_name} 策略回测...")
    
    try:
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=10000, commission=0.001)
        
        # 创建策略实例
        strategy = strategy_class({}, None)
        
        # 运行回测
        results = engine.run_single_backtest(strategy, data, strategy_name)
        
        if 'error' in results:
            print(f"  ❌ 回测失败: {results['error']}")
            return False
        
        # 显示关键指标
        print(f"  📊 回测结果:")
        print(f"     总收益率: {results['total_return_pct']:.2f}%")
        print(f"     总交易次数: {results['total_trades']}")
        print(f"     胜率: {results['win_rate']:.1f}%")
        print(f"     最大回撤: {results['max_drawdown']:.2f}%")
        print(f"     夏普比率: {results['sharpe_ratio']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_all_strategies(data):
    """比较所有策略性能"""
    print(f"\n🏆 策略性能比较...")
    
    try:
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=10000, commission=0.001)
        
        # 创建所有策略实例
        strategies = []
        for name in list_strategies():
            try:
                strategy = get_strategy(name)
                strategies.append(strategy)
            except Exception as e:
                print(f"  ⚠️  跳过策略 {name}: {e}")
        
        if not strategies:
            print("  ❌ 没有可用的策略进行比较")
            return False
        
        # 运行比较
        comparison_results = engine.compare_strategies(strategies, data, "综合测试")
        
        if 'error' in comparison_results:
            print(f"  ❌ 策略比较失败: {comparison_results['error']}")
            return False
        
        # 显示比较结果
        summary = comparison_results['comparison_summary']
        print(f"  📈 最佳收益率策略: {summary['best_return']['strategy']} ({summary['best_return']['value']:.2f}%)")
        print(f"  🎯 最高胜率策略: {summary['best_win_rate']['strategy']} ({summary['best_win_rate']['value']:.1f}%)")
        print(f"  ⚡ 最佳夏普比率策略: {summary['best_sharpe']['strategy']} ({summary['best_sharpe']['value']:.3f})")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 策略比较失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始综合策略测试")
    print("=" * 60)
    
    # 生成测试数据
    test_data = generate_sample_data(days=180, interval_hours=4)  # 6个月的4小时数据
    
    # 获取所有策略
    strategy_classes = [
        (CRSIStrategy, "CRSI"),
        (EMAStrategy, "EMA"), 
        (RSIStrategy, "RSI"),
        (BollingerStrategy, "Bollinger")
    ]
    
    print(f"\n📋 发现 {len(strategy_classes)} 个策略待测试")
    
    # 测试结果统计
    test_results = {
        'basic_tests': 0,
        'data_tests': 0, 
        'backtest_tests': 0,
        'total_strategies': len(strategy_classes)
    }
    
    # 逐个测试策略
    for strategy_class, strategy_name in strategy_classes:
        print(f"\n{'='*20} 测试 {strategy_name} 策略 {'='*20}")
        
        # 基本功能测试
        if test_strategy_basic_functionality(strategy_class, strategy_name):
            test_results['basic_tests'] += 1
        
        # 数据处理测试
        if test_strategy_with_data(strategy_class, strategy_name, test_data):
            test_results['data_tests'] += 1
        
        # 回测测试
        if run_strategy_backtest(strategy_class, strategy_name, test_data):
            test_results['backtest_tests'] += 1
    
    # 策略比较测试
    print(f"\n{'='*20} 策略比较测试 {'='*20}")
    comparison_success = compare_all_strategies(test_data)
    
    # 显示测试总结
    print(f"\n{'='*20} 测试总结 {'='*20}")
    print(f"📊 测试统计:")
    print(f"  总策略数: {test_results['total_strategies']}")
    print(f"  基本功能测试通过: {test_results['basic_tests']}/{test_results['total_strategies']}")
    print(f"  数据处理测试通过: {test_results['data_tests']}/{test_results['total_strategies']}")
    print(f"  回测测试通过: {test_results['backtest_tests']}/{test_results['total_strategies']}")
    print(f"  策略比较测试: {'✅ 通过' if comparison_success else '❌ 失败'}")
    
    # 计算总体成功率
    total_tests = test_results['basic_tests'] + test_results['data_tests'] + test_results['backtest_tests']
    max_tests = test_results['total_strategies'] * 3
    success_rate = (total_tests / max_tests) * 100 if max_tests > 0 else 0
    
    print(f"\n🎯 总体成功率: {success_rate:.1f}% ({total_tests}/{max_tests})")
    
    if success_rate >= 80:
        print("🎉 测试结果优秀！所有策略基本可用。")
    elif success_rate >= 60:
        print("✅ 测试结果良好，大部分策略可用。")
    else:
        print("⚠️  测试结果需要改进，部分策略存在问题。")
    
    print("\n🏁 综合策略测试完成！")

if __name__ == "__main__":
    main() 