"""
格式化工具
提供数据格式化和显示功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

def format_currency(value: float, currency: str = "¥") -> str:
    """
    格式化货币显示
    
    Args:
        value: 数值
        currency: 货币符号
    
    Returns:
        格式化后的货币字符串
    """
    if pd.isna(value):
        return "N/A"
    
    if abs(value) >= 1e9:
        return f"{currency}{value/1e9:.2f}B"
    elif abs(value) >= 1e6:
        return f"{currency}{value/1e6:.2f}M"
    elif abs(value) >= 1e3:
        return f"{currency}{value/1e3:.2f}K"
    else:
        return f"{currency}{value:,.2f}"

def format_percentage(value: float, decimal_places: int = 2) -> str:
    """
    格式化百分比显示
    
    Args:
        value: 数值（0.1 表示 10%）
        decimal_places: 小数位数
    
    Returns:
        格式化后的百分比字符串
    """
    if pd.isna(value):
        return "N/A"
    
    return f"{value * 100:.{decimal_places}f}%"

def format_number(value: float, decimal_places: int = 2) -> str:
    """
    格式化数字显示
    
    Args:
        value: 数值
        decimal_places: 小数位数
    
    Returns:
        格式化后的数字字符串
    """
    if pd.isna(value):
        return "N/A"
    
    return f"{value:,.{decimal_places}f}"

def format_datetime(dt: Union[datetime, pd.Timestamp, str], format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化日期时间显示
    
    Args:
        dt: 日期时间对象
        format_str: 格式字符串
    
    Returns:
        格式化后的日期时间字符串
    """
    if pd.isna(dt):
        return "N/A"
    
    if isinstance(dt, str):
        try:
            dt = pd.to_datetime(dt)
        except:
            return dt
    
    if isinstance(dt, (datetime, pd.Timestamp)):
        return dt.strftime(format_str)
    
    return str(dt)

def format_duration(seconds: float) -> str:
    """
    格式化时间长度显示
    
    Args:
        seconds: 秒数
    
    Returns:
        格式化后的时间长度字符串
    """
    if pd.isna(seconds) or seconds < 0:
        return "N/A"
    
    if seconds < 60:
        return f"{seconds:.0f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.1f}小时"
    else:
        days = seconds / 86400
        return f"{days:.1f}天"

def format_trade_status(status: str) -> str:
    """
    格式化交易状态显示
    
    Args:
        status: 状态字符串
    
    Returns:
        格式化后的状态字符串
    """
    status_map = {
        'open': '🟢 持仓中',
        'closed': '🔴 已平仓',
        'pending': '🟡 待成交',
        'cancelled': '⚫ 已取消',
        'filled': '✅ 已成交',
        'partial': '🟠 部分成交'
    }
    
    return status_map.get(status.lower(), status)

def format_signal_type(signal: Union[int, str]) -> str:
    """
    格式化信号类型显示
    
    Args:
        signal: 信号值
    
    Returns:
        格式化后的信号字符串
    """
    if isinstance(signal, str):
        signal_map = {
            'buy': '🟢 买入',
            'sell': '🔴 卖出',
            'hold': '🟡 持有',
            'neutral': '⚫ 中性'
        }
        return signal_map.get(signal.lower(), signal)
    
    elif isinstance(signal, (int, float)):
        if signal > 0:
            return '🟢 买入'
        elif signal < 0:
            return '🔴 卖出'
        else:
            return '⚫ 中性'
    
    return str(signal)

def format_risk_level(risk: float) -> str:
    """
    格式化风险等级显示
    
    Args:
        risk: 风险值（0-1）
    
    Returns:
        格式化后的风险等级字符串
    """
    if pd.isna(risk):
        return "N/A"
    
    if risk <= 0.3:
        return "🟢 低风险"
    elif risk <= 0.6:
        return "🟡 中风险"
    else:
        return "🔴 高风险"

def format_market_cap(value: float) -> str:
    """
    格式化市值显示
    
    Args:
        value: 市值数值
    
    Returns:
        格式化后的市值字符串
    """
    if pd.isna(value):
        return "N/A"
    
    if value >= 1e12:
        return f"${value/1e12:.2f}T"
    elif value >= 1e9:
        return f"${value/1e9:.2f}B"
    elif value >= 1e6:
        return f"${value/1e6:.2f}M"
    else:
        return f"${value:,.0f}"

def format_volume(value: float) -> str:
    """
    格式化成交量显示
    
    Args:
        value: 成交量数值
    
    Returns:
        格式化后的成交量字符串
    """
    if pd.isna(value):
        return "N/A"
    
    if value >= 1e9:
        return f"{value/1e9:.2f}B"
    elif value >= 1e6:
        return f"{value/1e6:.2f}M"
    elif value >= 1e3:
        return f"{value/1e3:.2f}K"
    else:
        return f"{value:,.0f}"

def format_price(value: float, symbol: str = "") -> str:
    """
    格式化价格显示
    
    Args:
        value: 价格数值
        symbol: 交易对符号
    
    Returns:
        格式化后的价格字符串
    """
    if pd.isna(value):
        return "N/A"
    
    # 根据价格大小决定小数位数
    if value >= 1000:
        return f"${value:,.2f}"
    elif value >= 1:
        return f"${value:.4f}"
    else:
        return f"${value:.6f}"

def format_change(current: float, previous: float) -> Dict[str, Any]:
    """
    格式化变化显示
    
    Args:
        current: 当前值
        previous: 之前值
    
    Returns:
        包含变化信息的字典
    """
    if pd.isna(current) or pd.isna(previous) or previous == 0:
        return {
            'absolute': "N/A",
            'percentage': "N/A",
            'direction': 'neutral',
            'color': 'gray'
        }
    
    absolute_change = current - previous
    percentage_change = (absolute_change / previous) * 100
    
    direction = 'up' if absolute_change > 0 else 'down' if absolute_change < 0 else 'neutral'
    color = 'green' if absolute_change > 0 else 'red' if absolute_change < 0 else 'gray'
    
    return {
        'absolute': f"{absolute_change:+,.2f}",
        'percentage': f"{percentage_change:+.2f}%",
        'direction': direction,
        'color': color
    }

def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    截断文本显示
    
    Args:
        text: 原始文本
        max_length: 最大长度
        suffix: 截断后缀
    
    Returns:
        截断后的文本
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix
