"""
技术指标计算模块
Technical Indicators Calculation Module
"""

import pandas as pd
import numpy as np
from typing import Union, Tuple, Optional

def calculate_sma(data: pd.Series, period: int) -> pd.Series:
    """计算简单移动平均线"""
    return data.rolling(window=period).mean()

def calculate_ema(data: pd.Series, period: int) -> pd.Series:
    """计算指数移动平均线"""
    return data.ewm(span=period, adjust=False).mean()

def calculate_rsi(data: pd.Series, period: int = 14) -> pd.Series:
    """计算相对强弱指标"""
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
    """计算MACD指标"""
    ema_fast = calculate_ema(data, fast)
    ema_slow = calculate_ema(data, slow)
    macd_line = ema_fast - ema_slow
    signal_line = calculate_ema(macd_line, signal)
    histogram = macd_line - signal_line
    
    return pd.DataFrame({
        'macd': macd_line,
        'signal': signal_line,
        'histogram': histogram
    })

def calculate_bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> pd.DataFrame:
    """计算布林带"""
    sma = calculate_sma(data, period)
    std = data.rolling(window=period).std()
    
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    
    return pd.DataFrame({
        'upper': upper_band,
        'middle': sma,
        'lower': lower_band
    })

def calculate_stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                        k_period: int = 14, d_period: int = 3) -> pd.DataFrame:
    """计算随机指标"""
    lowest_low = low.rolling(window=k_period).min()
    highest_high = high.rolling(window=k_period).max()
    
    k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
    d_percent = k_percent.rolling(window=d_period).mean()
    
    return pd.DataFrame({
        'k': k_percent,
        'd': d_percent
    })

def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
    """计算平均真实波幅"""
    high_low = high - low
    high_close = np.abs(high - close.shift())
    low_close = np.abs(low - close.shift())
    
    true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    atr = true_range.rolling(window=period).mean()
    
    return atr

def calculate_cci(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
    """计算商品通道指标"""
    typical_price = (high + low + close) / 3
    sma_tp = typical_price.rolling(window=period).mean()
    mean_deviation = typical_price.rolling(window=period).apply(
        lambda x: np.mean(np.abs(x - x.mean()))
    )
    cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
    return cci

def calculate_williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
    """计算威廉指标"""
    highest_high = high.rolling(window=period).max()
    lowest_low = low.rolling(window=period).min()
    
    williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
    return williams_r

def calculate_momentum(data: pd.Series, period: int = 10) -> pd.Series:
    """计算动量指标"""
    return data.diff(period)

def calculate_roc(data: pd.Series, period: int = 10) -> pd.Series:
    """计算变化率指标"""
    return ((data - data.shift(period)) / data.shift(period)) * 100

def calculate_obv(close: pd.Series, volume: pd.Series) -> pd.Series:
    """计算能量潮指标"""
    obv = pd.Series(index=close.index, dtype=float)
    obv.iloc[0] = volume.iloc[0]
    
    for i in range(1, len(close)):
        if close.iloc[i] > close.iloc[i-1]:
            obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
        elif close.iloc[i] < close.iloc[i-1]:
            obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
        else:
            obv.iloc[i] = obv.iloc[i-1]
    
    return obv

def calculate_vwap(high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
    """计算成交量加权平均价格"""
    typical_price = (high + low + close) / 3
    vwap = (typical_price * volume).cumsum() / volume.cumsum()
    return vwap

def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.DataFrame:
    """计算平均趋向指标"""
    # 计算真实波幅
    atr = calculate_atr(high, low, close, period)
    
    # 计算方向移动
    plus_dm = high.diff()
    minus_dm = -low.diff()
    
    plus_dm[plus_dm < 0] = 0
    minus_dm[minus_dm < 0] = 0
    
    # 平滑处理
    plus_dm_smooth = plus_dm.rolling(window=period).mean()
    minus_dm_smooth = minus_dm.rolling(window=period).mean()
    
    # 计算方向指标
    plus_di = 100 * (plus_dm_smooth / atr)
    minus_di = 100 * (minus_dm_smooth / atr)
    
    # 计算ADX
    dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
    adx = dx.rolling(window=period).mean()
    
    return pd.DataFrame({
        'adx': adx,
        'plus_di': plus_di,
        'minus_di': minus_di
    })

def calculate_ichimoku(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.DataFrame:
    """计算一目均衡表"""
    # 转换线 (9日)
    tenkan_sen = (high.rolling(9).max() + low.rolling(9).min()) / 2
    
    # 基准线 (26日)
    kijun_sen = (high.rolling(26).max() + low.rolling(26).min()) / 2
    
    # 先行带A
    senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(26)
    
    # 先行带B
    senkou_span_b = ((high.rolling(52).max() + low.rolling(52).min()) / 2).shift(26)
    
    # 滞后线
    chikou_span = close.shift(-26)
    
    return pd.DataFrame({
        'tenkan_sen': tenkan_sen,
        'kijun_sen': kijun_sen,
        'senkou_span_a': senkou_span_a,
        'senkou_span_b': senkou_span_b,
        'chikou_span': chikou_span
    })

def calculate_pivot_points(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.DataFrame:
    """计算枢轴点"""
    pivot = (high + low + close) / 3
    
    r1 = 2 * pivot - low
    s1 = 2 * pivot - high
    r2 = pivot + (high - low)
    s2 = pivot - (high - low)
    r3 = high + 2 * (pivot - low)
    s3 = low - 2 * (high - pivot)
    
    return pd.DataFrame({
        'pivot': pivot,
        'r1': r1, 'r2': r2, 'r3': r3,
        's1': s1, 's2': s2, 's3': s3
    })

def calculate_volume_profile(close: pd.Series, volume: pd.Series, bins: int = 20) -> pd.DataFrame:
    """计算成交量分布"""
    price_min = close.min()
    price_max = close.max()
    price_bins = np.linspace(price_min, price_max, bins + 1)
    
    volume_profile = []
    for i in range(len(price_bins) - 1):
        mask = (close >= price_bins[i]) & (close < price_bins[i + 1])
        vol_at_price = volume[mask].sum()
        volume_profile.append({
            'price_level': (price_bins[i] + price_bins[i + 1]) / 2,
            'volume': vol_at_price
        })
    
    return pd.DataFrame(volume_profile)

# 复合指标计算函数
def calculate_crsi(close: pd.Series, high: pd.Series, low: pd.Series, 
                  rsi_period: int = 14, ma_period: int = 20) -> pd.Series:
    """计算复合RSI指标"""
    # 基础RSI
    rsi = calculate_rsi(close, rsi_period)
    
    # 价格动量
    momentum = close.pct_change(ma_period) * 100
    
    # 波动率
    volatility = (high / low - 1) * 100
    
    # 合成CRSI指标 (权重可调整)
    crsi = (rsi * 0.4) + (momentum * 0.4) + (volatility * 0.2)
    return crsi

def detect_divergence(price: pd.Series, indicator: pd.Series, window: int = 5) -> pd.Series:
    """检测价格与指标的背离"""
    price_peaks = price.rolling(window=window, center=True).max() == price
    price_troughs = price.rolling(window=window, center=True).min() == price
    
    indicator_peaks = indicator.rolling(window=window, center=True).max() == indicator
    indicator_troughs = indicator.rolling(window=window, center=True).min() == indicator
    
    # 简化的背离检测
    bullish_divergence = price_troughs & ~indicator_troughs
    bearish_divergence = price_peaks & ~indicator_peaks
    
    divergence = pd.Series(0, index=price.index)
    divergence[bullish_divergence] = 1  # 看涨背离
    divergence[bearish_divergence] = -1  # 看跌背离
    
    return divergence 