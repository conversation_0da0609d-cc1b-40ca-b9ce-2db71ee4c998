"""
Configuration settings for the trading bot.
In a real application, these might be loaded from environment variables or a secure vault.
"""

# Proxy settings
# If you don't need a proxy, set PROXIES to None
PROXIES = {
    "http": "http://127.0.0.1:7890",
    "https": "http://127.0.0.1:7890",
}

# Binance API settings
# IMPORTANT: Do not hardcode real API keys here.
# Use environment variables or a config file that is not checked into version control.
API_KEY = "YOUR_API_KEY"
API_SECRET = "YOUR_API_SECRET"

# Binance API URLs
PRICE_URL = "https://api.binance.com/api/v3/ticker/price"  # Spot price API
PERP_PRICE_URL = "https://fapi.binance.com/fapi/v1/ticker/price"  # Perpetual futures price API
KLINE_URL = "https://api.binance.com/api/v3/klines"  # Spot K-line data API
PERP_KLINE_URL = "https://fapi.binance.com/fapi/v1/klines"  # Perpetual futures K-line data API
HR_URL = "https://fapi.binance.com/fapi/v1/ticker/24hr"  # 24hr data API
OI_URL = "https://fapi.binance.com/futures/data/openInterestHist"  # Perpetual futures open interest API

# Trading parameters
DEFAULT_TIMEFRAME = "1h"
DEFAULT_WINDOW_SIZE = 60
DEFAULT_VOLUME_THRESHOLD = 300
DEFAULT_ALERTS_LIMIT = 5

# Main process parameters
TIME_GAP = "1h"  # Time interval for analysis
AVERAGE = 60     # Window size for average calculation
THRESHOLD = 300  # Volume threshold for anomaly detection
REQUIRE_LIMIT = 5  # Maximum number of alerts to process 