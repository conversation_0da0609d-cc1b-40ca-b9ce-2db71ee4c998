"""
回测引擎
Backtesting Engine

支持单策略和多策略回测，提供详细的性能分析
"""

import pandas as pd
import numpy as np
import sqlite3
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
# import matplotlib.pyplot as plt
# import seaborn as sns
from pathlib import Path

from .base import BaseStrategy, SignalType

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_capital: float = 10000, commission: float = 0.001):
        """
        初始化回测引擎
        :param initial_capital: 初始资金
        :param commission: 手续费率
        """
        self.initial_capital = initial_capital
        self.commission = commission
        self.results = {}
        
    def load_data_from_db(self, db_path: str, table_name: str, 
                         symbol: str, start_date: Optional[str] = None, 
                         end_date: Optional[str] = None) -> pd.DataFrame:
        """从数据库加载数据"""
        conn = sqlite3.connect(db_path)
        
        query = f"SELECT * FROM {table_name} WHERE symbol = ?"
        params = [symbol]
        
        if start_date:
            query += " AND datetime_str >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND datetime_str <= ?"
            params.append(end_date)
        
        query += " ORDER BY datetime_str"
        
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()
        
        if df.empty:
            raise ValueError(f"未找到符号 {symbol} 的数据")
        
        # 转换时间索引
        df['datetime'] = pd.to_datetime(df['datetime_str'])
        df.set_index('datetime', inplace=True)
        
        # 确保数据类型正确
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        if 'volume' in df.columns:
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
        
        return df
    
    def run_single_backtest(self, strategy: BaseStrategy, data: pd.DataFrame,
                           symbol: str = "Unknown") -> Dict[str, Any]:
        """运行单策略回测"""
        print(f"🚀 开始回测策略: {strategy.name} - {symbol}")
        
        # 执行回测
        results = strategy.backtest(data, self.initial_capital, self.commission)
        
        if 'error' in results:
            print(f"❌ 回测失败: {results['error']}")
            return results
        
        # 添加额外信息
        results.update({
            'strategy_name': strategy.name,
            'symbol': symbol,
            'data_period': {
                'start': data.index[0].strftime('%Y-%m-%d'),
                'end': data.index[-1].strftime('%Y-%m-%d'),
                'total_days': (data.index[-1] - data.index[0]).days
            },
            'strategy_params': strategy.params,
            'data_points': len(data)
        })
        
        # 存储结果
        result_key = f"{strategy.name}_{symbol}"
        self.results[result_key] = results
        
        # 打印结果摘要
        self._print_backtest_summary(results)
        
        return results
    
    def run_multi_symbol_backtest(self, strategy: BaseStrategy, 
                                 symbols: List[str], db_path: str, 
                                 table_name: str, start_date: Optional[str] = None,
                                 end_date: Optional[str] = None) -> Dict[str, Any]:
        """运行多币种回测"""
        print(f"🚀 开始多币种回测: {strategy.name}")
        print(f"📊 币种数量: {len(symbols)}")
        
        all_results = {}
        successful_backtests = 0
        
        for i, symbol in enumerate(symbols, 1):
            print(f"\n[{i}/{len(symbols)}] 回测 {symbol}...")
            
            try:
                # 加载数据
                data = self.load_data_from_db(db_path, table_name, symbol, start_date, end_date)
                
                # 运行回测
                result = self.run_single_backtest(strategy, data, symbol)
                
                if 'error' not in result:
                    all_results[symbol] = result
                    successful_backtests += 1
                else:
                    print(f"⚠️  {symbol} 回测失败: {result['error']}")
                    
            except Exception as e:
                print(f"❌ {symbol} 数据加载失败: {e}")
        
        # 汇总结果
        summary = self._summarize_multi_backtest(all_results, strategy.name)
        summary['successful_backtests'] = successful_backtests
        summary['total_symbols'] = len(symbols)
        
        print(f"\n📈 多币种回测完成!")
        print(f"✅ 成功: {successful_backtests}/{len(symbols)} 个币种")
        
        return {
            'summary': summary,
            'individual_results': all_results
        }
    
    def compare_strategies(self, strategies: List[BaseStrategy], 
                          data: pd.DataFrame, symbol: str = "Unknown") -> Dict[str, Any]:
        """比较多个策略"""
        print(f"🔄 策略比较: {len(strategies)} 个策略 - {symbol}")
        
        comparison_results = {}
        
        for strategy in strategies:
            result = self.run_single_backtest(strategy, data, symbol)
            if 'error' not in result:
                comparison_results[strategy.name] = result
        
        # 生成比较报告
        comparison_summary = self._generate_strategy_comparison(comparison_results)
        
        return {
            'comparison_summary': comparison_summary,
            'individual_results': comparison_results
        }
    
    def _print_backtest_summary(self, results: Dict[str, Any]):
        """打印回测结果摘要"""
        print(f"\n📊 回测结果摘要:")
        print(f"  💰 总收益率: {results['total_return_pct']:.2f}%")
        print(f"  📈 总交易次数: {results['total_trades']}")
        print(f"  🎯 胜率: {results['win_rate']:.1f}%")
        print(f"  📉 最大回撤: {results['max_drawdown']:.2f}%")
        print(f"  ⚡ 夏普比率: {results['sharpe_ratio']:.3f}")
        print(f"  💎 盈亏比: {results['profit_factor']:.2f}")
        
        if results['total_trades'] > 0:
            print(f"  💚 平均盈利: ${results['avg_win']:.2f}")
            print(f"  💔 平均亏损: ${results['avg_loss']:.2f}")
    
    def _summarize_multi_backtest(self, all_results: Dict[str, Any], 
                                 strategy_name: str) -> Dict[str, Any]:
        """汇总多币种回测结果"""
        if not all_results:
            return {'error': '无有效回测结果'}
        
        # 提取关键指标
        returns = [r['total_return_pct'] for r in all_results.values()]
        win_rates = [r['win_rate'] for r in all_results.values()]
        max_drawdowns = [r['max_drawdown'] for r in all_results.values()]
        sharpe_ratios = [r['sharpe_ratio'] for r in all_results.values() if not np.isnan(r['sharpe_ratio'])]
        total_trades = [r['total_trades'] for r in all_results.values()]
        
        # 计算统计指标
        summary = {
            'strategy_name': strategy_name,
            'total_symbols': len(all_results),
            'returns': {
                'mean': np.mean(returns),
                'median': np.median(returns),
                'std': np.std(returns),
                'min': np.min(returns),
                'max': np.max(returns),
                'positive_count': len([r for r in returns if r > 0])
            },
            'win_rates': {
                'mean': np.mean(win_rates),
                'median': np.median(win_rates),
                'min': np.min(win_rates),
                'max': np.max(win_rates)
            },
            'max_drawdowns': {
                'mean': np.mean(max_drawdowns),
                'median': np.median(max_drawdowns),
                'min': np.min(max_drawdowns),
                'max': np.max(max_drawdowns)
            },
            'sharpe_ratios': {
                'mean': np.mean(sharpe_ratios) if sharpe_ratios else 0,
                'median': np.median(sharpe_ratios) if sharpe_ratios else 0,
                'count': len(sharpe_ratios)
            },
            'trading_activity': {
                'total_trades': sum(total_trades),
                'avg_trades_per_symbol': np.mean(total_trades),
                'symbols_with_trades': len([t for t in total_trades if t > 0])
            }
        }
        
        # 找出最佳和最差表现
        best_symbol = max(all_results.keys(), key=lambda k: all_results[k]['total_return_pct'])
        worst_symbol = min(all_results.keys(), key=lambda k: all_results[k]['total_return_pct'])
        
        summary['best_performer'] = {
            'symbol': best_symbol,
            'return_pct': all_results[best_symbol]['total_return_pct']
        }
        summary['worst_performer'] = {
            'symbol': worst_symbol,
            'return_pct': all_results[worst_symbol]['total_return_pct']
        }
        
        return summary
    
    def _generate_strategy_comparison(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成策略比较报告"""
        if not results:
            return {'error': '无有效策略结果'}
        
        # 按收益率排序
        sorted_strategies = sorted(
            results.items(), 
            key=lambda x: x[1]['total_return_pct'], 
            reverse=True
        )
        
        comparison = {
            'ranking': [],
            'metrics_comparison': {},
            'best_return': {},
            'best_win_rate': {},
            'best_sharpe': {}
        }
        
        # 生成排名
        for rank, (strategy_name, result) in enumerate(sorted_strategies, 1):
            comparison['ranking'].append({
                'rank': rank,
                'strategy': strategy_name,
                'return_pct': result['total_return_pct'],
                'win_rate': result['win_rate'],
                'max_drawdown': result['max_drawdown'],
                'sharpe_ratio': result['sharpe_ratio'],
                'total_trades': result['total_trades']
            })
        
        # 找出各项最佳指标
        best_return_strategy = max(results.items(), key=lambda x: x[1]['total_return_pct'])
        best_win_rate_strategy = max(results.items(), key=lambda x: x[1]['win_rate'])
        best_sharpe_strategy = max(results.items(), key=lambda x: x[1]['sharpe_ratio'] if not np.isnan(x[1]['sharpe_ratio']) else -999)
        
        comparison['best_return'] = {
            'strategy': best_return_strategy[0],
            'value': best_return_strategy[1]['total_return_pct']
        }
        comparison['best_win_rate'] = {
            'strategy': best_win_rate_strategy[0],
            'value': best_win_rate_strategy[1]['win_rate']
        }
        comparison['best_sharpe'] = {
            'strategy': best_sharpe_strategy[0],
            'value': best_sharpe_strategy[1]['sharpe_ratio']
        }
        
        # 指标对比
        metrics = ['total_return_pct', 'win_rate', 'max_drawdown', 'sharpe_ratio', 'total_trades']
        for metric in metrics:
            values = [result[metric] for result in results.values()]
            comparison['metrics_comparison'][metric] = {
                'best': max(values),
                'worst': min(values),
                'average': np.mean(values)
            }
        
        return comparison
    
    def generate_report(self, output_dir: str = "./test_reports"):
        """生成详细的回测报告"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 生成文本报告
        report_file = output_path / f"backtest_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("回测报告 - Backtest Report\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for key, result in self.results.items():
                if 'error' not in result:
                    f.write(f"\n策略: {result['strategy_name']}\n")
                    f.write(f"币种: {result['symbol']}\n")
                    f.write(f"数据期间: {result['data_period']['start']} ~ {result['data_period']['end']}\n")
                    f.write(f"总收益率: {result['total_return_pct']:.2f}%\n")
                    f.write(f"总交易次数: {result['total_trades']}\n")
                    f.write(f"胜率: {result['win_rate']:.1f}%\n")
                    f.write(f"最大回撤: {result['max_drawdown']:.2f}%\n")
                    f.write(f"夏普比率: {result['sharpe_ratio']:.3f}\n")
                    f.write("-" * 40 + "\n")
        
        print(f"📄 回测报告已生成: {report_file}")
        return str(report_file)
    
    def plot_equity_curve(self, strategy_name: str, symbol: str = None, 
                         save_path: str = None):
        """绘制权益曲线 (需要安装matplotlib)"""
        try:
            import matplotlib.pyplot as plt
        except ImportError:
            print("❌ 需要安装matplotlib才能绘制图表: pip install matplotlib")
            return
        
        # 查找匹配的结果
        result_key = None
        for key in self.results.keys():
            if strategy_name in key and (symbol is None or symbol in key):
                result_key = key
                break
        
        if not result_key or 'equity_curve' not in self.results[result_key]:
            print(f"❌ 未找到 {strategy_name} 的权益曲线数据")
            return
        
        equity_curve = self.results[result_key]['equity_curve']
        
        # 转换为DataFrame
        df = pd.DataFrame(equity_curve)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 绘图
        plt.figure(figsize=(12, 8))
        
        # 权益曲线
        plt.subplot(2, 1, 1)
        plt.plot(df['timestamp'], df['equity'], label='Portfolio Value', linewidth=2)
        plt.axhline(y=self.initial_capital, color='r', linestyle='--', alpha=0.7, label='Initial Capital')
        plt.title(f'Equity Curve - {strategy_name}')
        plt.ylabel('Portfolio Value ($)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 价格曲线
        plt.subplot(2, 1, 2)
        plt.plot(df['timestamp'], df['price'], label='Price', color='orange', linewidth=1)
        plt.title('Price Chart')
        plt.ylabel('Price')
        plt.xlabel('Time')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 权益曲线图已保存: {save_path}")
        else:
            plt.show()
        
        plt.close()

# 使用示例
if __name__ == "__main__":
    # 这里可以添加一些测试代码
    pass 