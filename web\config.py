"""
TBTrade Web应用配置文件
"""

import os
from pathlib import Path

# 项目路径配置
PROJECT_ROOT = Path(__file__).parent.parent
WEB_ROOT = Path(__file__).parent
TBTRADE_SRC = PROJECT_ROOT / "src"
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
CONFIG_DIR = PROJECT_ROOT / "config"
BACKTEST_RESULTS_DIR = PROJECT_ROOT / "backtest_results"

# Streamlit应用配置
STREAMLIT_CONFIG = {
    "page_title": "TBTrade 量化交易系统",
    "page_icon": "📈",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# 数据库配置
DATABASE_CONFIG = {
    "market_data_db": DATA_DIR / "market_data.db",
    "usdt_historical_db": DATA_DIR / "usdt_historical_data.db",
    "alerts_db": DATA_DIR / "alerts.db"
}

# 图表配置
CHART_CONFIG = {
    "theme": "plotly_white",
    "height": 500,
    "margin": {"l": 50, "r": 50, "t": 50, "b": 50},
    "colors": {
        "primary": "#1f77b4",
        "success": "#28a745",
        "warning": "#ffc107",
        "danger": "#dc3545",
        "info": "#17a2b8"
    }
}

# 回测配置
BACKTEST_CONFIG = {
    "default_symbols": ["BTCUSDT", "ETHUSDT", "ADAUSDT"],
    "default_capital": 10000,
    "max_symbols": 50,
    "date_range_limit": 365 * 3  # 3年
}

# 实时数据配置
REALTIME_CONFIG = {
    "refresh_interval": 5,  # 秒
    "max_data_points": 1000,
    "websocket_timeout": 30
}

# 系统监控配置
MONITORING_CONFIG = {
    "cpu_warning_threshold": 80,
    "memory_warning_threshold": 85,
    "disk_warning_threshold": 90,
    "log_lines_display": 100
}

# 数据管理配置
DATA_MANAGEMENT_CONFIG = {
    "batch_size": 1000,
    "max_concurrent_downloads": 5,
    "data_quality_checks": True,
    "auto_backup": True
}

# 安全配置
SECURITY_CONFIG = {
    "enable_authentication": False,  # 开发阶段关闭
    "session_timeout": 3600,  # 1小时
    "max_login_attempts": 3
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_handler": True,
    "console_handler": True
}

# 环境变量
def get_env_var(key: str, default=None):
    """获取环境变量"""
    return os.getenv(key, default)

# API配置（如果需要外部API）
API_CONFIG = {
    "binance_api_key": get_env_var("BINANCE_API_KEY"),
    "binance_secret_key": get_env_var("BINANCE_SECRET_KEY"),
    "request_timeout": 30,
    "rate_limit": 1200  # 每分钟请求数
}
