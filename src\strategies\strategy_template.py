"""
策略模板
Strategy Template

这是一个策略模板，用于快速创建新的交易策略
复制此文件并修改为您的策略实现
"""

from typing import Dict, Any, List
import pandas as pd
import numpy as np
from .base import BaseStrategy, SignalType

class TemplateStrategy(BaseStrategy):
    """
    策略模板类
    
    使用说明：
    1. 复制此文件并重命名为您的策略名称
    2. 修改类名为您的策略名称
    3. 实现所有抽象方法
    4. 在 __init__.py 中注册您的策略
    """
    
    @classmethod
    def get_default_parameters(cls) -> Dict[str, Any]:
        """
        获取策略默认参数
        
        Returns:
            Dict[str, Any]: 默认参数字典
        """
        return {
            # 在这里定义您的策略参数
            'example_period': 20,
            'example_threshold': 0.02,
            'stop_loss_pct': 0.05,
            'take_profit_pct': 0.10
        }
    
    @classmethod
    def get_required_columns(cls) -> List[str]:
        """
        获取策略需要的数据列
        
        Returns:
            List[str]: 必需的数据列名列表
        """
        return ['open', 'high', 'low', 'close', 'volume']
    
    def _validate_parameters(self):
        """
        验证策略参数有效性
        
        在这里添加参数验证逻辑
        如果参数无效，抛出 ValueError
        """
        if not 5 <= self.params['example_period'] <= 100:
            raise ValueError("example_period 应在5-100之间")
        
        if not 0.001 <= self.params['example_threshold'] <= 0.1:
            raise ValueError("example_threshold 应在0.001-0.1之间")
        
        if not 0.01 <= self.params['stop_loss_pct'] <= 0.2:
            raise ValueError("stop_loss_pct 应在0.01-0.2之间")

    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标
        
        Args:
            data: 输入的K线数据，包含 open, high, low, close, volume 列
            
        Returns:
            pd.DataFrame: 包含技术指标的数据框
        """
        df = data.copy()
        
        # 示例：计算简单移动平均线
        df['sma'] = df['close'].rolling(window=self.params['example_period']).mean()
        
        # 示例：计算价格变化率
        df['price_change'] = df['close'].pct_change()
        
        # 示例：计算波动率
        df['volatility'] = df['price_change'].rolling(window=self.params['example_period']).std()
        
        # 在这里添加更多技术指标计算
        # 例如：RSI, MACD, 布林带等
        
        return df

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Args:
            data: 包含价格和指标数据的数据框
            
        Returns:
            pd.DataFrame: 包含信号的数据框
        """
        df = data.copy()
        
        # 初始化信号列
        df['signal'] = SignalType.NEUTRAL.value
        df['confidence'] = 0.0
        df['reason'] = ''
        
        # 示例信号逻辑：价格突破移动平均线
        for i in range(1, len(df)):
            current_price = df.iloc[i]['close']
            current_sma = df.iloc[i]['sma']
            prev_price = df.iloc[i-1]['close']
            prev_sma = df.iloc[i-1]['sma']
            
            # 跳过无效数据
            if pd.isna(current_sma) or pd.isna(prev_sma):
                continue
            
            # 买入信号：价格向上突破移动平均线
            if prev_price <= prev_sma and current_price > current_sma:
                df.iloc[i, df.columns.get_loc('signal')] = SignalType.BUY.value
                df.iloc[i, df.columns.get_loc('confidence')] = 0.7
                df.iloc[i, df.columns.get_loc('reason')] = '价格向上突破SMA'
            
            # 卖出信号：价格向下跌破移动平均线
            elif prev_price >= prev_sma and current_price < current_sma:
                df.iloc[i, df.columns.get_loc('signal')] = SignalType.SELL.value
                df.iloc[i, df.columns.get_loc('confidence')] = 0.7
                df.iloc[i, df.columns.get_loc('reason')] = '价格向下跌破SMA'
        
        return df

    def generate_signal(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        生成单个交易信号（用于实时交易）
        
        Args:
            data: 最新的K线数据
            
        Returns:
            Dict[str, Any]: 信号字典
        """
        if len(data) < self.params['example_period']:
            return {
                'action': 'HOLD',
                'confidence': 0,
                'reason': '数据不足',
                'timestamp': pd.Timestamp.now()
            }
        
        # 计算指标
        df_with_indicators = self.calculate_indicators(data)
        
        # 生成信号
        df_with_signals = self.generate_signals(df_with_indicators)
        
        # 获取最新信号
        latest_signal = df_with_signals.iloc[-1]
        
        # 转换信号类型
        signal_map = {
            SignalType.BUY.value: 'BUY',
            SignalType.SELL.value: 'SELL',
            SignalType.STRONG_BUY.value: 'STRONG_BUY',
            SignalType.STRONG_SELL.value: 'STRONG_SELL',
            SignalType.NEUTRAL.value: 'HOLD'
        }
        
        return {
            'action': signal_map.get(latest_signal['signal'], 'HOLD'),
            'confidence': latest_signal['confidence'] * 100,  # 转换为百分比
            'reason': latest_signal['reason'],
            'timestamp': pd.Timestamp.now(),
            'price': latest_signal['close']
        }

# 注册策略示例（在实际策略文件中取消注释）
# from . import register_strategy
# register_strategy('Template', TemplateStrategy)
