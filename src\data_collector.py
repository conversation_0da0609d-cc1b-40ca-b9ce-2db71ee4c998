#!/usr/bin/env python3
"""
数据收集器模块
Data Collector Module

用于抓取Binance历史K线数据到本地进行分析
"""

import os
import json
import time
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import concurrent.futures
from pathlib import Path

from .config_manager import config_manager
from .logger_config import setup_logger
from .data_layer import binance_api
from .utils.time_utils import get_whattime_now

# 设置日志
logger = setup_logger(__name__)


class CryptoDataCollector:
    """加密货币数据收集器"""
    
    def __init__(self, data_dir: str = "./data"):
        """
        初始化数据收集器
        
        Args:
            data_dir: 数据存储目录
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.klines_dir = self.data_dir / "klines"
        self.analysis_dir = self.data_dir / "analysis"
        self.reports_dir = self.data_dir / "reports"
        
        for dir_path in [self.klines_dir, self.analysis_dir, self.reports_dir]:
            dir_path.mkdir(exist_ok=True)
        
        logger.info(f"数据收集器初始化完成，数据目录: {self.data_dir}")
    
    def get_available_symbols(self) -> List[str]:
        """
        获取可用的交易对列表
        
        Returns:
            List[str]: 交易对符号列表
        """
        try:
            from .getCoinPrice import get_binance_all_coin
            
            all_coins = get_binance_all_coin()
            if not all_coins:
                logger.warning("无法获取交易对列表，使用默认列表")
                return [
                    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
                    'XRPUSDT', 'DOGEUSDT', 'AVAXUSDT', 'DOTUSDT', 'MATICUSDT',
                    'LINKUSDT', 'LTCUSDT', 'ATOMUSDT', 'ETCUSDT', 'XLMUSDT'
                ]
            
            # 提取USDT交易对
            usdt_pairs = [
                coin['symbol'] for coin in all_coins 
                if coin.get('symbol', '').endswith('USDT')
            ]
            
            logger.info(f"获取到 {len(usdt_pairs)} 个USDT交易对")
            return usdt_pairs[:100]  # 限制前100个热门交易对
            
        except Exception as e:
            logger.error(f"获取交易对列表失败: {e}")
            return ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
    
    def collect_klines_data(self, symbol: str, start_date: str, end_date: str, 
                           interval: str = "1h") -> Optional[pd.DataFrame]:
        """
        收集单个交易对的K线数据
        
        Args:
            symbol: 交易对符号
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            interval: 时间间隔 (1m, 5m, 15m, 1h, 4h, 1d)
            
        Returns:
            Optional[pd.DataFrame]: K线数据DataFrame
        """
        try:
            # 解析日期
            start_time = datetime.strptime(start_date, "%Y-%m-%d")
            end_time = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            
            logger.info(f"开始收集 {symbol} 的K线数据: {start_date} 到 {end_date} ({interval})")
            
            # 获取K线数据
            klines_data = binance_api.get_klines_info(symbol, start_time, end_time, interval)
            
            if not klines_data:
                logger.warning(f"{symbol}: 未获取到K线数据")
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(klines_data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'count', 'taker_buy_volume',
                'taker_buy_quote_volume', 'ignore'
            ])
            
            # 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'quote_volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 时间戳转换
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('datetime', inplace=True)
            
            # 保存到本地
            filename = f"{symbol}_{start_date}_{end_date}_{interval}.csv"
            filepath = self.klines_dir / filename
            df.to_csv(filepath)
            
            logger.info(f"✅ {symbol}: 收集到 {len(df)} 条K线数据，已保存到 {filename}")
            return df
            
        except Exception as e:
            logger.error(f"❌ {symbol}: 收集K线数据失败 - {e}")
            return None
    
    def batch_collect_data(self, symbols: List[str], start_date: str, 
                          end_date: str, interval: str = "1h", 
                          max_workers: int = 10) -> Dict[str, pd.DataFrame]:
        """
        批量收集多个交易对的数据
        
        Args:
            symbols: 交易对列表
            start_date: 开始日期
            end_date: 结束日期
            interval: 时间间隔
            max_workers: 最大并发数
            
        Returns:
            Dict[str, pd.DataFrame]: 符号到数据的映射
        """
        logger.info(f"开始批量收集 {len(symbols)} 个交易对的数据")
        
        collected_data = {}
        failed_symbols = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_symbol = {
                executor.submit(
                    self.collect_klines_data, symbol, start_date, end_date, interval
                ): symbol 
                for symbol in symbols
            }
            
            # 收集结果
            completed = 0
            for future in concurrent.futures.as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                completed += 1
                
                try:
                    df = future.result()
                    if df is not None:
                        collected_data[symbol] = df
                    else:
                        failed_symbols.append(symbol)
                        
                    if completed % 10 == 0:
                        logger.info(f"已完成 {completed}/{len(symbols)} 个交易对的数据收集")
                        
                except Exception as e:
                    logger.error(f"{symbol}: 数据收集异常 - {e}")
                    failed_symbols.append(symbol)
                
                # 添加延迟避免API限制
                time.sleep(0.1)
        
        success_count = len(collected_data)
        fail_count = len(failed_symbols)
        
        logger.info(f"✅ 批量收集完成: 成功 {success_count}, 失败 {fail_count}")
        
        if failed_symbols:
            logger.warning(f"失败的交易对: {failed_symbols[:10]}...")  # 只显示前10个
        
        return collected_data
    
    def analyze_volume_patterns(self, df: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """
        分析交易量模式
        
        Args:
            df: K线数据DataFrame
            symbol: 交易对符号
            
        Returns:
            Dict: 分析结果
        """
        try:
            # 基础统计
            volume_stats = {
                'symbol': symbol,
                'data_points': len(df),
                'date_range': f"{df.index.min().date()} to {df.index.max().date()}",
                'avg_volume': df['volume'].mean(),
                'avg_quote_volume': df['quote_volume'].mean(),
                'max_volume': df['volume'].max(),
                'min_volume': df['volume'].min(),
                'volume_std': df['volume'].std()
            }
            
            # 计算异常交易量（超过平均值2倍标准差）
            threshold = volume_stats['avg_quote_volume'] + 2 * df['quote_volume'].std()
            abnormal_volume_days = df[df['quote_volume'] > threshold]
            
            volume_stats['abnormal_volume_count'] = len(abnormal_volume_days)
            volume_stats['abnormal_volume_rate'] = len(abnormal_volume_days) / len(df) * 100
            
            # 价格涨跌分析
            df['price_change'] = df['close'].pct_change()
            df['volume_change'] = df['quote_volume'].pct_change()
            
            # 高交易量日期的价格表现
            if len(abnormal_volume_days) > 0:
                avg_price_change_on_high_volume = abnormal_volume_days['price_change'].mean()
                volume_stats['avg_price_change_on_high_volume'] = avg_price_change_on_high_volume * 100
                
                # 获取前5个最高交易量的日期
                top_volume_days = df.nlargest(5, 'quote_volume')[['quote_volume', 'close', 'price_change']]
                volume_stats['top_volume_days'] = top_volume_days.to_dict('records')
            
            return volume_stats
            
        except Exception as e:
            logger.error(f"分析 {symbol} 交易量模式失败: {e}")
            return {'symbol': symbol, 'error': str(e)}
    
    def analyze_price_patterns(self, df: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """
        分析价格模式
        
        Args:
            df: K线数据DataFrame
            symbol: 交易对符号
            
        Returns:
            Dict: 价格分析结果
        """
        try:
            # 计算技术指标
            df['sma_20'] = df['close'].rolling(window=20).mean()
            df['sma_50'] = df['close'].rolling(window=50).mean()
            df['price_change'] = df['close'].pct_change()
            df['volatility'] = df['price_change'].rolling(window=20).std()
            
            # 基础价格统计
            price_stats = {
                'symbol': symbol,
                'start_price': float(df['close'].iloc[0]),
                'end_price': float(df['close'].iloc[-1]),
                'max_price': float(df['high'].max()),
                'min_price': float(df['low'].min()),
                'avg_price': float(df['close'].mean()),
                'total_return': ((df['close'].iloc[-1] / df['close'].iloc[0]) - 1) * 100,
                'volatility': float(df['volatility'].mean()) * 100,
                'max_daily_gain': float(df['price_change'].max()) * 100,
                'max_daily_loss': float(df['price_change'].min()) * 100
            }
            
            # 趋势分析
            current_sma20 = df['sma_20'].iloc[-1]
            current_sma50 = df['sma_50'].iloc[-1]
            current_price = df['close'].iloc[-1]
            
            if pd.notna(current_sma20) and pd.notna(current_sma50):
                if current_price > current_sma20 > current_sma50:
                    trend = "强烈上涨"
                elif current_price > current_sma20:
                    trend = "上涨"
                elif current_price < current_sma20 < current_sma50:
                    trend = "强烈下跌"
                elif current_price < current_sma20:
                    trend = "下跌"
                else:
                    trend = "震荡"
            else:
                trend = "数据不足"
            
            price_stats['trend'] = trend
            
            # 支撑阻力位（简单计算）
            recent_highs = df['high'].rolling(window=20).max().dropna()
            recent_lows = df['low'].rolling(window=20).min().dropna()
            
            if len(recent_highs) > 0 and len(recent_lows) > 0:
                price_stats['resistance_level'] = float(recent_highs.iloc[-1])
                price_stats['support_level'] = float(recent_lows.iloc[-1])
            
            return price_stats
            
        except Exception as e:
            logger.error(f"分析 {symbol} 价格模式失败: {e}")
            return {'symbol': symbol, 'error': str(e)}
    
    def generate_analysis_report(self, start_date: str, end_date: str, 
                               interval: str = "1h") -> Dict[str, Any]:
        """
        生成分析报告
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            interval: 时间间隔
            
        Returns:
            Dict: 完整的分析报告
        """
        logger.info(f"开始生成分析报告: {start_date} 到 {end_date}")
        
        # 获取交易对列表
        symbols = self.get_available_symbols()[:50]  # 分析前50个热门交易对
        
        # 收集数据
        collected_data = self.batch_collect_data(symbols, start_date, end_date, interval)
        
        if not collected_data:
            logger.error("没有收集到任何数据")
            return {'error': '数据收集失败'}
        
        # 分析数据
        volume_analyses = []
        price_analyses = []
        
        logger.info("开始分析数据...")
        
        for symbol, df in collected_data.items():
            try:
                # 交易量分析
                volume_analysis = self.analyze_volume_patterns(df, symbol)
                volume_analyses.append(volume_analysis)
                
                # 价格分析
                price_analysis = self.analyze_price_patterns(df, symbol)
                price_analyses.append(price_analysis)
                
            except Exception as e:
                logger.error(f"分析 {symbol} 失败: {e}")
        
        # 生成报告
        report = {
            'metadata': {
                'generation_time': get_whattime_now().isoformat(),
                'date_range': f"{start_date} to {end_date}",
                'interval': interval,
                'analyzed_symbols': len(collected_data),
                'total_requested': len(symbols)
            },
            'volume_analysis': volume_analyses,
            'price_analysis': price_analyses,
            'summary': self._generate_summary(volume_analyses, price_analyses)
        }
        
        # 保存报告
        report_filename = f"analysis_report_{start_date}_{end_date}_{interval}.json"
        report_path = self.reports_dir / report_filename
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 分析报告已生成: {report_filename}")
        return report
    
    def _generate_summary(self, volume_analyses: List[Dict], 
                         price_analyses: List[Dict]) -> Dict[str, Any]:
        """
        生成分析摘要
        
        Args:
            volume_analyses: 交易量分析结果
            price_analyses: 价格分析结果
            
        Returns:
            Dict: 摘要信息
        """
        try:
            # 过滤掉错误的分析结果
            valid_volume = [v for v in volume_analyses if 'error' not in v]
            valid_price = [p for p in price_analyses if 'error' not in p]
            
            if not valid_volume or not valid_price:
                return {'error': '无有效数据用于生成摘要'}
            
            # 交易量摘要
            avg_abnormal_rate = sum(v.get('abnormal_volume_rate', 0) for v in valid_volume) / len(valid_volume)
            
            # 找出异常交易量最高的币种
            top_volume_anomaly = max(valid_volume, key=lambda x: x.get('abnormal_volume_rate', 0))
            
            # 价格表现摘要
            returns = [p.get('total_return', 0) for p in valid_price]
            avg_return = sum(returns) / len(returns)
            
            # 找出表现最好和最差的币种
            best_performer = max(valid_price, key=lambda x: x.get('total_return', 0))
            worst_performer = min(valid_price, key=lambda x: x.get('total_return', 0))
            
            summary = {
                'total_analyzed': len(valid_volume),
                'average_abnormal_volume_rate': round(avg_abnormal_rate, 2),
                'average_return': round(avg_return, 2),
                'top_volume_anomaly': {
                    'symbol': top_volume_anomaly.get('symbol'),
                    'abnormal_rate': top_volume_anomaly.get('abnormal_volume_rate')
                },
                'best_performer': {
                    'symbol': best_performer.get('symbol'),
                    'return': best_performer.get('total_return')
                },
                'worst_performer': {
                    'symbol': worst_performer.get('symbol'),
                    'return': worst_performer.get('total_return')
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"生成摘要失败: {e}")
            return {'error': str(e)}


def main():
    """主函数 - 演示数据收集和分析"""
    collector = CryptoDataCollector()
    
    # 获取最近一个月的数据进行分析
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    print(f"开始收集和分析数据: {start_date} 到 {end_date}")
    
    # 生成分析报告
    report = collector.generate_analysis_report(
        start_date.strftime("%Y-%m-%d"),
        end_date.strftime("%Y-%m-%d"),
        "1h"
    )
    
    if 'error' not in report:
        print("\n📊 分析摘要:")
        summary = report.get('summary', {})
        print(f"  分析币种数: {summary.get('total_analyzed', 0)}")
        print(f"  平均异常交易量率: {summary.get('average_abnormal_volume_rate', 0)}%")
        print(f"  平均收益率: {summary.get('average_return', 0)}%")
        
        if 'best_performer' in summary:
            best = summary['best_performer']
            print(f"  最佳表现: {best.get('symbol')} ({best.get('return', 0):.2f}%)")
        
        if 'worst_performer' in summary:
            worst = summary['worst_performer']
            print(f"  最差表现: {worst.get('symbol')} ({worst.get('return', 0):.2f}%)")
    else:
        print(f"❌ 分析失败: {report['error']}")


if __name__ == "__main__":
    main() 