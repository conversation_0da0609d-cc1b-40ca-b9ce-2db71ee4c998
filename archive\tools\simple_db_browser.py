#!/usr/bin/env python3
"""
简单数据库浏览器
Simple Database Browser

不依赖matplotlib的轻量级数据库查看工具
"""

import sqlite3
import pandas as pd
from pathlib import Path
import argparse
from datetime import datetime, timedelta
import json

class SimpleDBBrowser:
    """简单数据库浏览器"""
    
    def __init__(self, db_path: str):
        """
        初始化数据库浏览器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        if not self.db_path.exists():
            raise FileNotFoundError(f"数据库文件不存在: {db_path}")
        
        self.conn = sqlite3.connect(str(self.db_path))
        
    def __del__(self):
        """析构函数，关闭数据库连接"""
        if hasattr(self, 'conn'):
            self.conn.close()
    
    def list_tables(self):
        """列出数据库中的所有表"""
        cursor = self.conn.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        """)
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 数据库: {self.db_path.name}")
        print(f"📋 包含 {len(tables)} 个表:")
        for i, table in enumerate(tables, 1):
            # 获取表的记录数
            cursor = self.conn.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  {i}. {table} ({count:,} 条记录)")
        
        return tables
    
    def describe_table(self, table_name: str):
        """描述表结构和基本统计信息"""
        print(f"\n📋 表: {table_name}")
        print("=" * 60)
        
        # 表结构
        cursor = self.conn.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        print("📝 表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 基本统计
        cursor = self.conn.execute(f"SELECT COUNT(*) FROM {table_name}")
        row_count = cursor.fetchone()[0]
        print(f"\n📊 记录数: {row_count:,}")
        
        # 如果是K线数据表，显示更多信息
        column_names = [col[1] for col in columns]
        if 'symbol' in column_names and 'datetime' in column_names:
            self._describe_kline_table(table_name)
        
        # 显示前几条记录
        print(f"\n📄 前5条记录:")
        cursor = self.conn.execute(f"SELECT * FROM {table_name} LIMIT 5")
        rows = cursor.fetchall()
        
        if rows:
            # 打印表头
            headers = [col[1] for col in columns]
            print("  " + " | ".join(f"{h:>12}" for h in headers))
            print("  " + "-" * (len(headers) * 15))
            
            # 打印数据行
            for row in rows:
                formatted_row = []
                for val in row:
                    if isinstance(val, float):
                        formatted_row.append(f"{val:>12.4f}")
                    elif isinstance(val, str) and len(val) > 12:
                        formatted_row.append(f"{val[:9]:>12}...")
                    else:
                        formatted_row.append(f"{str(val):>12}")
                print("  " + " | ".join(formatted_row))
    
    def _describe_kline_table(self, table_name: str):
        """描述K线数据表的详细信息"""
        try:
            # 交易对数量
            cursor = self.conn.execute(f"SELECT COUNT(DISTINCT symbol) FROM {table_name}")
            symbol_count = cursor.fetchone()[0]
            print(f"📈 交易对数: {symbol_count}")
            
            # 时间范围
            cursor = self.conn.execute(f"""
                SELECT MIN(datetime), MAX(datetime) 
                FROM {table_name}
                WHERE datetime IS NOT NULL
            """)
            time_range = cursor.fetchone()
            if time_range[0]:
                print(f"⏰ 时间范围: {time_range[0]} ~ {time_range[1]}")
            
            # 热门交易对
            cursor = self.conn.execute(f"""
                SELECT symbol, COUNT(*) as count 
                FROM {table_name} 
                GROUP BY symbol 
                ORDER BY count DESC 
                LIMIT 10
            """)
            top_symbols = cursor.fetchall()
            print(f"\n🔥 热门交易对 (Top 10):")
            for i, (symbol, count) in enumerate(top_symbols, 1):
                print(f"  {i:2d}. {symbol:12} {count:>8,} 条记录")
                
        except Exception as e:
            print(f"⚠️ 无法获取K线表详细信息: {e}")
    
    def query_symbol_data(self, table_name: str, symbol: str, days: int = 7):
        """查询指定交易对的数据"""
        print(f"\n📈 {symbol} 最近 {days} 天数据")
        print("=" * 80)
        
        # 获取数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        query = f"""
            SELECT datetime, open, high, low, close, volume
            FROM {table_name}
            WHERE symbol = ? AND datetime >= ?
            ORDER BY datetime DESC
            LIMIT 20
        """
        
        cursor = self.conn.execute(query, [symbol, start_date.isoformat()])
        rows = cursor.fetchall()
        
        if not rows:
            print(f"❌ 未找到 {symbol} 的数据")
            return
        
        # 显示数据
        headers = ['时间', '开盘', '最高', '最低', '收盘', '成交量']
        print("  " + " | ".join(f"{h:>15}" for h in headers))
        print("  " + "-" * (len(headers) * 18))
        
        for row in rows:
            datetime_str = row[0][:16] if row[0] else "N/A"  # 只显示到分钟
            formatted_row = [
                f"{datetime_str:>15}",
                f"{row[1]:>15.4f}" if row[1] else "N/A",
                f"{row[2]:>15.4f}" if row[2] else "N/A", 
                f"{row[3]:>15.4f}" if row[3] else "N/A",
                f"{row[4]:>15.4f}" if row[4] else "N/A",
                f"{row[5]:>15,.0f}" if row[5] else "N/A"
            ]
            print("  " + " | ".join(formatted_row))
        
        # 计算基本统计
        closes = [row[4] for row in rows if row[4]]
        if closes:
            print(f"\n📊 统计信息:")
            print(f"  最新价格: {closes[0]:.4f}")
            print(f"  最高价格: {max(closes):.4f}")
            print(f"  最低价格: {min(closes):.4f}")
            print(f"  平均价格: {sum(closes)/len(closes):.4f}")
            if len(closes) > 1:
                change = (closes[0] - closes[-1]) / closes[-1] * 100
                print(f"  期间涨跌: {change:+.2f}%")
    
    def search_symbols(self, table_name: str, pattern: str = ""):
        """搜索交易对"""
        if pattern:
            query = f"""
                SELECT symbol, COUNT(*) as count, 
                       MIN(datetime) as first_date, 
                       MAX(datetime) as last_date
                FROM {table_name} 
                WHERE symbol LIKE ?
                GROUP BY symbol 
                ORDER BY count DESC
            """
            cursor = self.conn.execute(query, [f"%{pattern}%"])
            print(f"\n🔍 搜索结果 (包含 '{pattern}'):")
        else:
            query = f"""
                SELECT symbol, COUNT(*) as count, 
                       MIN(datetime) as first_date, 
                       MAX(datetime) as last_date
                FROM {table_name} 
                GROUP BY symbol 
                ORDER BY count DESC
                LIMIT 20
            """
            cursor = self.conn.execute(query)
            print(f"\n📋 所有交易对 (Top 20):")
        
        print("=" * 80)
        
        rows = cursor.fetchall()
        if not rows:
            print("❌ 未找到匹配的交易对")
            return
        
        headers = ['交易对', '记录数', '开始时间', '结束时间']
        print("  " + " | ".join(f"{h:>15}" for h in headers))
        print("  " + "-" * (len(headers) * 18))
        
        for row in rows:
            symbol, count, first_date, last_date = row
            first_str = first_date[:10] if first_date else "N/A"
            last_str = last_date[:10] if last_date else "N/A"
            formatted_row = [
                f"{symbol:>15}",
                f"{count:>15,}",
                f"{first_str:>15}",
                f"{last_str:>15}"
            ]
            print("  " + " | ".join(formatted_row))
    
    def export_data(self, table_name: str, symbol: str = None, output_format: str = 'csv', limit: int = 1000):
        """导出数据到文件"""
        if symbol:
            query = f"SELECT * FROM {table_name} WHERE symbol = ? ORDER BY datetime DESC LIMIT ?"
            params = [symbol, limit]
            filename = f"{table_name}_{symbol}.{output_format}"
        else:
            query = f"SELECT * FROM {table_name} ORDER BY datetime DESC LIMIT ?"
            params = [limit]
            filename = f"{table_name}_sample.{output_format}"
        
        try:
            df = pd.read_sql_query(query, self.conn, params=params)
            
            if df.empty:
                print("❌ 未找到数据")
                return
            
            if output_format.lower() == 'csv':
                df.to_csv(filename, index=False)
            elif output_format.lower() == 'json':
                df.to_json(filename, orient='records', date_format='iso', indent=2)
            
            print(f"📁 数据已导出: {filename} ({len(df)} 条记录)")
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简单数据库浏览器')
    parser.add_argument('db_path', help='数据库文件路径')
    parser.add_argument('--list', action='store_true', help='列出所有表')
    parser.add_argument('--describe', help='描述指定表')
    parser.add_argument('--query', help='查询指定交易对数据')
    parser.add_argument('--search', help='搜索交易对 (支持模糊匹配)')
    parser.add_argument('--table', default='klines', help='表名 (默认: klines)')
    parser.add_argument('--days', type=int, default=7, help='查询天数 (默认: 7)')
    parser.add_argument('--export', help='导出数据 (指定交易对或all)')
    parser.add_argument('--format', default='csv', choices=['csv', 'json'], help='导出格式')
    parser.add_argument('--limit', type=int, default=1000, help='导出记录数限制')
    
    args = parser.parse_args()
    
    try:
        browser = SimpleDBBrowser(args.db_path)
        
        if args.list:
            browser.list_tables()
        elif args.describe:
            browser.describe_table(args.describe)
        elif args.query:
            browser.query_symbol_data(args.table, args.query, args.days)
        elif args.search is not None:  # 允许空字符串搜索
            browser.search_symbols(args.table, args.search)
        elif args.export:
            symbol = None if args.export.lower() == 'all' else args.export
            browser.export_data(args.table, symbol, args.format, args.limit)
        else:
            # 交互式模式
            tables = browser.list_tables()
            if tables:
                print(f"\n💡 使用示例:")
                print(f"  python {Path(__file__).name} {args.db_path} --describe {tables[0]}")
                print(f"  python {Path(__file__).name} {args.db_path} --search BTC")
                print(f"  python {Path(__file__).name} {args.db_path} --query BTCUSDT --days 7")
                print(f"  python {Path(__file__).name} {args.db_path} --export BTCUSDT --format csv")
    
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
