import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
import os

cur_path = os.path.abspath(os.path.dirname(__file__))

def send_email(subject, body, img_path, recipient, sender_email, sender_password, attachment_path=None):
    try:
        # 创建SMTP客户端
        smtp_server = "smtp.qq.com"  # 设置你的SMTP服务器地址
        smtp_port = 587  # 设置SMTP服务器端口号
        smtp_client = smtplib.SMTP(smtp_server, smtp_port)
        smtp_client.starttls()
        
        # 登录到SMTP服务器
        smtp_client.login(sender_email, sender_password)

        # 构建邮件内容
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = recipient
        msg['Subject'] = subject
            
        # 发送plain格式
        body_text = MIMEText(body, 'plain')
        msg.attach(body_text)


        if img_path != "":
            # 读取图片并添加到正文中
            with open(img_path, 'rb') as f:
                img_data = f.read()
                image = MIMEImage(img_data)
                image.add_header('Content-ID', '<1>')
                msg.attach(image)
            
            # <p style="color: red;">{body}</p>
            # 设置正文HTML，包含图片的CID
            html = f"""
            <html>
            <body>
                <pre>{body}</pre>
                <img src="cid:1" alt="test1">
            </body>
            </html>
            """
            msg.attach(MIMEText(html, 'html'))

        if attachment_path:
            with open(attachment_path, "rb") as attachment_file:
                attachment = MIMEApplication(attachment_file.read(), Name=attachment_path)
            attachment['Content-Disposition'] = f'attachment; filename="{attachment_path}"'

            msg.attach(attachment)

        # 发送邮件
        smtp_client.sendmail(sender_email, recipient, msg.as_string())

        # 退出SMTP客户端
        smtp_client.quit()

        print("邮件发送成功！")
    except Exception as e:
        print("邮件发送失败:", e)

def func_notice_with_send_email(title, body, img_path):
    # 邮件信息
    # subject = "邮件通知测试"
    # body = "这是一个测试邮件通知功能的内容。"
    recipient = ['<EMAIL>']  # 设置收件人邮箱
    sender_email = "<EMAIL>"  # 设置发件人邮箱
    sender_password = "eoxofefderhzefhg"  # 设置发件人邮箱密码
    attachment_path = None  # 设置附件路径，如果不需要附件，设置为None

    # 发送邮件
    for recver in recipient:
        send_email(title, body, img_path, recver, sender_email, sender_password, attachment_path)

def func_notice_with_send_email_beta(title, body, img_path):
    """
    Beta version of email notification function.
    Uses a different recipient list for testing or alternative notifications.
    """
    # 邮件信息 - Beta版本使用不同的收件人列表
    recipient = ['<EMAIL>']  # Beta版本的收件人邮箱
    sender_email = "<EMAIL>"  # 设置发件人邮箱
    sender_password = "eoxofefderhzefhg"  # 设置发件人邮箱密码
    attachment_path = None  # 设置附件路径，如果不需要附件，设置为None

    # 发送邮件
    for recver in recipient:
        send_email(title, body, img_path, recver, sender_email, sender_password, attachment_path)

# def func_notice_with_send_email(title, body, img_path):
#     # 邮件信息
#     # subject = "邮件通知测试"
#     # body = "这是一个测试邮件通知功能的内容。"
#     # recipient = ['<EMAIL>', '<EMAIL>']  # 设置收件人邮箱
#     recipient = ['<EMAIL>']  # 设置收件人邮箱

#     sender_email = "<EMAIL>"  # 设置发件人邮箱
#     sender_password = "eoxofefderhzefhg"  # 设置发件人邮箱密码
#     attachment_path = None  # 设置附件路径，如果不需要附件，设置为None

#     # 发送邮件
#     for recver in recipient:
#         send_email(title, body, img_path, recver, sender_email, sender_password, attachment_path)

if __name__ == "__main__":
    func_notice_with_send_email("test", "test", "")
    # func_notice_with_send_email("test", "test", "")
#use wx_app ljjjjj
# import requests

# def send_subscribe_message(access_token, openid, template_id):
#     url = f"https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={access_token}"
    
#     payload = {
#         "touser": openid,
#         "template_id": template_id,
#         "page": "pages/index/index",  # 点击消息后跳转的页面路径，根据实际情况设置
#         "data": {
#             "content": {
#                 "value": "这是一条订阅消息通知",
#             }
#         }
#     }
    
#     try:
#         response = requests.post(url, json=payload)
#         response_data = response.json()
#         if response_data.get('errcode') == 0:
#             print("订阅消息发送成功！")
#         else:
#             print("订阅消息发送失败:", response_data.get('errmsg'))
#     except requests.exceptions.RequestException as e:
#         print("请求异常:", e)

# if __name__ == "__main__":
#     # 小程序配置信息
#     appid = "wxcee7399e5785e196"  # 小程序的AppID
#     appsecret = "81e1648257a177370d14de19c36724f1"  # 小程序的AppSecret

#     # 获取access_token
#     access_token_url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={appsecret}"
#     try:
#         response = requests.get(access_token_url)
#         access_token_data = response.json()
#         access_token = access_token_data.get('access_token')
#     except requests.exceptions.RequestException as e:
#         print("获取access_token失败:", e)
#         access_token = None

#     if access_token:
#         # 小程序模板消息ID
#         template_id = "id18"  # 将你的模板消息ID替换为实际的模板ID
#         openid = "user_openid"  # 用户的openid，接收订阅消息的用户openid

#         # 发送订阅消息
#         send_subscribe_message(access_token, openid, template_id)
