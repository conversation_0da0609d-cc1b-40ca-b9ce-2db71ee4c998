"""
时间工具模块
Time utilities module

从原main.py中提取的时间相关工具函数
"""
from datetime import datetime, timedelta, timezone
from typing import Optional

def get_whattime_now() -> datetime:
    """
    获取当前时间
    Get current time
    
    Returns:
        datetime: 当前北京时间
    """
    # 获取当前UTC时间
    utc_now = datetime.now(timezone.utc)
    # 转换为北京时间 (UTC+8)
    beijing_time = utc_now + timedelta(hours=8)
    return beijing_time


def gmt_to_utc(gmt_time: datetime) -> datetime:
    """
    将GMT时间转换为UTC时间
    Converts GMT time to UTC time
    
    Args:
        gmt_time: GMT时间
        
    Returns:
        datetime: UTC时间
    """
    return gmt_time - timedelta(hours=8)


def is_current_to_get_klines(time_gap: str, is_test: bool = False) -> bool:
    """
    检查当前时间是否适合获取K线数据
    Check if current time is suitable for getting klines data
    
    Args:
        time_gap: 时间间隔 ('1h', '4h', '1d' 等)
        is_test: 是否为测试模式
        
    Returns:
        bool: 是否可以获取K线数据
    """
    if is_test:
        return True
        
    current_time = get_whattime_now()
    minute = current_time.minute
    hour = current_time.hour
    
    # 根据时间间隔判断是否为合适的时间点
    if time_gap == "1h":
        # 每小时的第1-5分钟
        return 1 <= minute <= 5
    elif time_gap == "4h":
        # 每4小时的第1-5分钟 (0, 4, 8, 12, 16, 20点)
        return (hour % 4 == 0) and (1 <= minute <= 5)
    elif time_gap == "1d":
        # 每天0点的第1-5分钟
        return (hour == 0) and (1 <= minute <= 5)
    elif time_gap == "1w":
        # 每周一0点的第1-5分钟
        return (current_time.weekday() == 0) and (hour == 0) and (1 <= minute <= 5)
    else:
        # 默认情况，假设总是可以
        return True


def is_current_to_trace() -> bool:
    """
    检查当前时间是否适合进行追踪操作
    Check if current time is suitable for tracing operations
    
    Returns:
        bool: 是否可以进行追踪
    """
    current_time = get_whattime_now()
    minute = current_time.minute
    
    # 每小时的第6-10分钟进行追踪
    return 6 <= minute <= 10


def get_time_range(end_time: Optional[datetime] = None, 
                  window_hours: int = 60) -> tuple[datetime, datetime]:
    """
    获取时间范围
    Get time range for analysis
    
    Args:
        end_time: 结束时间，如果为None则使用当前时间
        window_hours: 时间窗口（小时）
        
    Returns:
        tuple: (开始时间, 结束时间)
    """
    if end_time is None:
        end_time = get_whattime_now()
    
    start_time = end_time - timedelta(hours=window_hours)
    return start_time, end_time


def format_timestamp(timestamp: int) -> str:
    """
    格式化时间戳为可读字符串
    Format timestamp to readable string
    
    Args:
        timestamp: Unix时间戳（毫秒）
        
    Returns:
        str: 格式化的时间字符串
    """
    # 转换毫秒时间戳为秒
    dt = datetime.fromtimestamp(timestamp / 1000)
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def get_trading_session_info() -> dict:
    """
    获取当前交易时段信息
    Get current trading session information
    
    Returns:
        dict: 交易时段信息
    """
    current_time = get_whattime_now()
    hour = current_time.hour
    
    # 定义交易时段
    if 0 <= hour < 8:
        session = "亚洲早盘"
    elif 8 <= hour < 16:
        session = "亚洲主盘"
    elif 16 <= hour < 24:
        session = "欧美盘"
    else:
        session = "未知时段"
    
    return {
        "session": session,
        "hour": hour,
        "timestamp": current_time.timestamp(),
        "formatted_time": current_time.strftime("%Y-%m-%d %H:%M:%S")
    } 