"""
异步回测执行系统
使用threading实现长时间回测任务的后台执行和进度跟踪
"""

import threading
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import json

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消

@dataclass
class BacktestTask:
    """回测任务数据类"""
    task_id: str
    config: Dict[str, Any]
    status: TaskStatus
    progress: float = 0.0
    message: str = ""
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'config': self.config,
            'status': self.status.value,
            'progress': self.progress,
            'message': self.message,
            'result': self.result,
            'error': self.error,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
        }

class AsyncBacktestManager:
    """异步回测管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, BacktestTask] = {}
        self.active_threads: Dict[str, threading.Thread] = {}
        self.lock = threading.Lock()
        self.max_concurrent_tasks = 3  # 最大并发任务数
    
    def submit_backtest(self, config: Dict[str, Any], 
                       progress_callback: Optional[Callable] = None) -> str:
        """
        提交回测任务
        
        Args:
            config: 回测配置
            progress_callback: 进度回调函数
            
        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        with self.lock:
            # 检查并发任务数限制
            running_tasks = sum(1 for task in self.tasks.values() 
                              if task.status == TaskStatus.RUNNING)
            
            if running_tasks >= self.max_concurrent_tasks:
                # 创建等待任务
                task = BacktestTask(
                    task_id=task_id,
                    config=config,
                    status=TaskStatus.PENDING,
                    message="等待执行中..."
                )
            else:
                # 立即开始执行
                task = BacktestTask(
                    task_id=task_id,
                    config=config,
                    status=TaskStatus.RUNNING,
                    message="正在初始化..."
                )
                
                # 启动执行线程
                thread = threading.Thread(
                    target=self._execute_backtest,
                    args=(task_id, progress_callback),
                    daemon=True
                )
                thread.start()
                self.active_threads[task_id] = thread
            
            self.tasks[task_id] = task
        
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self.lock:
            task = self.tasks.get(task_id)
            return task.to_dict() if task else None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                return False
            
            task.status = TaskStatus.CANCELLED
            task.message = "任务已取消"
            task.completed_at = datetime.now()
            
            # 如果任务正在运行，标记为取消（线程会检查这个状态）
            return True
    
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务状态"""
        with self.lock:
            return {task_id: task.to_dict() for task_id, task in self.tasks.items()}
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的旧任务"""
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        
        with self.lock:
            to_remove = []
            for task_id, task in self.tasks.items():
                if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                    task.completed_at and task.completed_at.timestamp() < cutoff_time):
                    to_remove.append(task_id)
            
            for task_id in to_remove:
                del self.tasks[task_id]
                if task_id in self.active_threads:
                    del self.active_threads[task_id]
    
    def _execute_backtest(self, task_id: str, progress_callback: Optional[Callable] = None):
        """执行回测任务（在后台线程中运行）"""
        try:
            with self.lock:
                task = self.tasks.get(task_id)
                if not task or task.status == TaskStatus.CANCELLED:
                    return
                
                task.started_at = datetime.now()
                task.status = TaskStatus.RUNNING
                task.message = "正在执行回测..."
            
            # 导入TBTrade集成
            from .tbtrade_integration import tbtrade_integration
            
            # 更新进度回调
            def update_progress(progress: float, message: str = ""):
                with self.lock:
                    current_task = self.tasks.get(task_id)
                    if current_task and current_task.status != TaskStatus.CANCELLED:
                        current_task.progress = progress
                        current_task.message = message
                
                if progress_callback:
                    progress_callback(task_id, progress, message)
            
            # 执行回测的各个阶段
            update_progress(10, "验证参数...")
            
            # 检查是否被取消
            with self.lock:
                if self.tasks[task_id].status == TaskStatus.CANCELLED:
                    return
            
            update_progress(20, "加载数据...")
            
            # 检查是否被取消
            with self.lock:
                if self.tasks[task_id].status == TaskStatus.CANCELLED:
                    return
            
            update_progress(30, "初始化策略...")
            
            # 执行实际回测
            update_progress(40, "执行回测计算...")
            result = tbtrade_integration.run_backtest(task.config)
            
            # 检查是否被取消
            with self.lock:
                if self.tasks[task_id].status == TaskStatus.CANCELLED:
                    return
            
            update_progress(90, "处理结果...")
            
            # 完成任务
            with self.lock:
                current_task = self.tasks.get(task_id)
                if current_task and current_task.status != TaskStatus.CANCELLED:
                    current_task.status = TaskStatus.COMPLETED
                    current_task.progress = 100.0
                    current_task.message = "回测完成"
                    current_task.result = result
                    current_task.completed_at = datetime.now()
            
            update_progress(100, "回测完成")
            
        except Exception as e:
            # 处理错误
            with self.lock:
                current_task = self.tasks.get(task_id)
                if current_task:
                    current_task.status = TaskStatus.FAILED
                    current_task.error = str(e)
                    current_task.message = f"执行失败: {str(e)}"
                    current_task.completed_at = datetime.now()
        
        finally:
            # 清理线程引用
            with self.lock:
                if task_id in self.active_threads:
                    del self.active_threads[task_id]
            
            # 检查是否有等待的任务可以开始执行
            self._start_pending_tasks()
    
    def _start_pending_tasks(self):
        """启动等待中的任务"""
        with self.lock:
            running_count = sum(1 for task in self.tasks.values() 
                              if task.status == TaskStatus.RUNNING)
            
            if running_count < self.max_concurrent_tasks:
                # 找到最早的等待任务
                pending_tasks = [(task_id, task) for task_id, task in self.tasks.items() 
                               if task.status == TaskStatus.PENDING]
                
                if pending_tasks:
                    # 按创建时间排序
                    pending_tasks.sort(key=lambda x: x[1].created_at)
                    task_id, task = pending_tasks[0]
                    
                    # 启动任务
                    task.status = TaskStatus.RUNNING
                    task.message = "正在初始化..."
                    
                    thread = threading.Thread(
                        target=self._execute_backtest,
                        args=(task_id,),
                        daemon=True
                    )
                    thread.start()
                    self.active_threads[task_id] = thread

# 创建全局异步回测管理器实例
async_backtest_manager = AsyncBacktestManager()
