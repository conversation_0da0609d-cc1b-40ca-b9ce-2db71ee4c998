"""
执行与告警管理器
Execution and Alert Manager

负责执行交易信号和发送多渠道告警通知
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import sqlite3

try:
    import smtplib
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False
    smtplib = None
    MimeText = None
    MimeMultipart = None

try:
    import discord
    from discord.ext import commands
    DISCORD_AVAILABLE = True
except ImportError:
    DISCORD_AVAILABLE = False

from .signal_generator import SignalRecord, StrategySignalGenerator
from ..strategies.base import SignalType

class NotificationChannel:
    """通知渠道基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    async def send(self, message: str, title: str = None, **kwargs) -> bool:
        """发送通知"""
        raise NotImplementedError
    
    def enable(self):
        """启用通知渠道"""
        self.enabled = True
    
    def disable(self):
        """禁用通知渠道"""
        self.enabled = False

class DiscordNotifier(NotificationChannel):
    """Discord通知器"""
    
    def __init__(self, webhook_url: str = None, bot_token: str = None, channel_id: int = None):
        super().__init__("Discord")
        self.webhook_url = webhook_url
        self.bot_token = bot_token
        self.channel_id = channel_id
        self.bot = None
        
        if not DISCORD_AVAILABLE:
            self.logger.warning("⚠️ Discord库未安装，Discord通知不可用")
            self.enabled = False
    
    async def send(self, message: str, title: str = None, **kwargs) -> bool:
        """发送Discord通知"""
        if not self.enabled or not DISCORD_AVAILABLE:
            return False
        
        try:
            if self.webhook_url:
                # 使用Webhook发送
                import aiohttp
                
                embed = {
                    "title": title or "交易信号",
                    "description": message,
                    "color": self._get_color(kwargs.get('signal_type')),
                    "timestamp": datetime.now().isoformat(),
                    "footer": {"text": "TradeApi Alert System"}
                }
                
                if kwargs.get('fields'):
                    embed["fields"] = kwargs['fields']
                
                payload = {"embeds": [embed]}
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(self.webhook_url, json=payload) as response:
                        if response.status == 204:
                            self.logger.info("✅ Discord通知发送成功")
                            return True
                        else:
                            self.logger.error(f"❌ Discord通知发送失败: {response.status}")
                            return False
            
            elif self.bot_token and self.channel_id:
                # 使用Bot发送
                if not self.bot:
                    self.bot = commands.Bot(command_prefix='!', intents=discord.Intents.default())
                
                channel = self.bot.get_channel(self.channel_id)
                if channel:
                    await channel.send(f"**{title}**\n{message}")
                    self.logger.info("✅ Discord Bot通知发送成功")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Discord通知发送失败: {e}")
            return False
    
    def _get_color(self, signal_type: str) -> int:
        """根据信号类型获取颜色"""
        color_map = {
            'BUY': 0x00ff00,      # 绿色
            'STRONG_BUY': 0x00aa00,  # 深绿色
            'SELL': 0xff0000,     # 红色
            'STRONG_SELL': 0xaa0000, # 深红色
            'NEUTRAL': 0x808080   # 灰色
        }
        return color_map.get(signal_type, 0x0099ff)  # 默认蓝色

class EmailNotifier(NotificationChannel):
    """邮件通知器"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, 
                 password: str, recipients: List[str]):
        super().__init__("Email")
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.recipients = recipients
    
    async def send(self, message: str, title: str = None, **kwargs) -> bool:
        """发送邮件通知"""
        if not self.enabled or not EMAIL_AVAILABLE:
            if not EMAIL_AVAILABLE:
                self.logger.warning("⚠️ 邮件功能不可用")
            return False
        
        try:
            # 创建邮件
            msg = MimeMultipart()
            msg['From'] = self.username
            msg['To'] = ', '.join(self.recipients)
            msg['Subject'] = title or "交易信号通知"
            
            # 添加HTML内容
            html_content = self._format_html_message(message, kwargs)
            msg.attach(MimeText(html_content, 'html', 'utf-8'))
            
            # 发送邮件
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            self.logger.info("✅ 邮件通知发送成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 邮件通知发送失败: {e}")
            return False
    
    def _format_html_message(self, message: str, kwargs: Dict) -> str:
        """格式化HTML邮件内容"""
        signal_type = kwargs.get('signal_type', 'NEUTRAL')
        color = '#00ff00' if 'BUY' in signal_type else '#ff0000' if 'SELL' in signal_type else '#808080'
        
        html = f"""
        <html>
        <body>
            <h2 style="color: {color};">交易信号通知</h2>
            <div style="font-family: Arial, sans-serif;">
                <p>{message.replace('\n', '<br>')}</p>
                <hr>
                <p style="font-size: 12px; color: #666;">
                    发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br>
                    系统: TradeApi Alert System
                </p>
            </div>
        </body>
        </html>
        """
        return html

class LogNotifier(NotificationChannel):
    """日志通知器"""
    
    def __init__(self, log_file: str = "./logs/alerts.log"):
        super().__init__("Log")
        self.log_file = Path(log_file)
        self.log_file.parent.mkdir(exist_ok=True)
        
        # 配置文件日志
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        self.file_logger = logging.getLogger(f"{__name__}.file")
        self.file_logger.addHandler(file_handler)
        self.file_logger.setLevel(logging.INFO)
    
    async def send(self, message: str, title: str = None, **kwargs) -> bool:
        """记录到日志文件"""
        if not self.enabled:
            return False
        
        try:
            log_message = f"[{title}] {message}" if title else message
            self.file_logger.info(log_message)
            return True
        except Exception as e:
            self.logger.error(f"❌ 日志记录失败: {e}")
            return False

class DatabaseLogger:
    """数据库日志记录器"""
    
    def __init__(self, db_path: str = "./data/alerts.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    symbol TEXT NOT NULL,
                    strategy TEXT NOT NULL,
                    signal_type TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    price REAL NOT NULL,
                    reason TEXT,
                    indicators TEXT,
                    notification_status TEXT,
                    execution_status TEXT
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_timestamp 
                ON alerts(timestamp)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_symbol 
                ON alerts(symbol)
            """)
    
    def log_signal(self, signal: SignalRecord, notification_status: str = "pending", 
                   execution_status: str = "pending") -> int:
        """记录信号到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    INSERT INTO alerts 
                    (symbol, strategy, signal_type, confidence, price, reason, 
                     indicators, notification_status, execution_status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    signal.symbol,
                    signal.strategy,
                    signal.signal.name,
                    signal.confidence,
                    signal.price,
                    signal.reason,
                    json.dumps(signal.indicators) if signal.indicators else None,
                    notification_status,
                    execution_status
                ))
                return cursor.lastrowid
        except Exception as e:
            logging.error(f"❌ 数据库记录失败: {e}")
            return -1
    
    def update_status(self, alert_id: int, notification_status: str = None, 
                     execution_status: str = None):
        """更新状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                if notification_status:
                    conn.execute(
                        "UPDATE alerts SET notification_status = ? WHERE id = ?",
                        (notification_status, alert_id)
                    )
                if execution_status:
                    conn.execute(
                        "UPDATE alerts SET execution_status = ? WHERE id = ?",
                        (execution_status, alert_id)
                    )
        except Exception as e:
            logging.error(f"❌ 状态更新失败: {e}")

class ExecutionManager:
    """执行管理器"""
    
    def __init__(self, signal_generator: StrategySignalGenerator):
        """
        初始化执行管理器
        
        Args:
            signal_generator: 信号生成器实例
        """
        self.signal_generator = signal_generator
        self.notifiers = []  # 通知渠道列表
        self.db_logger = DatabaseLogger()
        
        # 执行配置
        self.simulation_mode = True  # 模拟交易模式
        self.auto_execution = False  # 自动执行
        
        # 系统状态
        self.is_running = False
        self.last_health_check = datetime.now()
        self.health_check_interval = timedelta(minutes=30)
        
        # 统计信息
        self.stats = {
            'total_signals': 0,
            'notifications_sent': 0,
            'notifications_failed': 0,
            'executions_success': 0,
            'executions_failed': 0
        }
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
    
    def add_notifier(self, notifier: NotificationChannel):
        """添加通知渠道"""
        self.notifiers.append(notifier)
        self.logger.info(f"📢 添加通知渠道: {notifier.name}")
    
    def remove_notifier(self, notifier_name: str):
        """移除通知渠道"""
        self.notifiers = [n for n in self.notifiers if n.name != notifier_name]
        self.logger.info(f"🗑️ 移除通知渠道: {notifier_name}")
    
    async def process_signal(self, signal: SignalRecord) -> bool:
        """处理信号"""
        try:
            self.stats['total_signals'] += 1
            
            # 记录到数据库
            alert_id = self.db_logger.log_signal(signal)
            
            # 发送通知
            notification_success = await self._send_notifications(signal)
            
            # 更新通知状态
            self.db_logger.update_status(
                alert_id, 
                notification_status="success" if notification_success else "failed"
            )
            
            # 执行交易（如果启用）
            execution_success = False
            if self.auto_execution:
                execution_success = await self._execute_trade(signal)
                self.db_logger.update_status(alert_id, execution_status="success" if execution_success else "failed")
            
            self.logger.info(f"✅ 信号处理完成: {signal.symbol} {signal.signal.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 信号处理失败: {e}")
            return False
    
    async def _send_notifications(self, signal: SignalRecord) -> bool:
        """发送通知"""
        if not self.notifiers:
            return False
        
        # 格式化消息
        message = self._format_signal_message(signal)
        title = f"🚨 {signal.symbol} 交易信号"
        
        # 准备额外参数
        kwargs = {
            'signal_type': signal.signal.name,
            'fields': [
                {"name": "交易对", "value": signal.symbol, "inline": True},
                {"name": "策略", "value": signal.strategy, "inline": True},
                {"name": "置信度", "value": f"{signal.confidence:.1f}%", "inline": True},
                {"name": "价格", "value": f"{signal.price:.4f}", "inline": True},
                {"name": "原因", "value": signal.reason, "inline": False}
            ]
        }
        
        # 并发发送通知
        tasks = []
        for notifier in self.notifiers:
            if notifier.enabled:
                task = notifier.send(message, title, **kwargs)
                tasks.append(task)
        
        if not tasks:
            return False
        
        # 等待所有通知完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        success_count = sum(1 for r in results if r is True)
        failed_count = len(results) - success_count
        
        self.stats['notifications_sent'] += success_count
        self.stats['notifications_failed'] += failed_count
        
        self.logger.info(f"📢 通知发送完成: 成功 {success_count}, 失败 {failed_count}")
        
        return success_count > 0
    
    def _format_signal_message(self, signal: SignalRecord) -> str:
        """格式化信号消息"""
        signal_emoji = {
            'BUY': '📈',
            'STRONG_BUY': '🚀',
            'SELL': '📉',
            'STRONG_SELL': '🔻',
            'NEUTRAL': '📊'
        }
        
        emoji = signal_emoji.get(signal.signal.name, '📊')
        
        message = f"""
{emoji} **{signal.signal.name}** 信号

**交易对**: {signal.symbol}
**策略**: {signal.strategy}
**置信度**: {signal.confidence:.1f}%
**当前价格**: {signal.price:.4f}
**时间**: {signal.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

**原因**: {signal.reason}
        """.strip()
        
        # 添加指标信息
        if signal.indicators:
            message += "\n\n**技术指标**:"
            for key, value in signal.indicators.items():
                if isinstance(value, (int, float)):
                    message += f"\n• {key}: {value:.4f}"
                else:
                    message += f"\n• {key}: {value}"
        
        return message
    
    async def _execute_trade(self, signal: SignalRecord) -> bool:
        """执行交易"""
        try:
            if self.simulation_mode:
                # 模拟交易
                self.logger.info(f"🎮 模拟执行: {signal.symbol} {signal.signal.name}")
                self.stats['executions_success'] += 1
                return True
            else:
                # 实盘交易（需要实现交易所API调用）
                self.logger.warning("⚠️ 实盘交易功能尚未实现")
                self.stats['executions_failed'] += 1
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 交易执行失败: {e}")
            self.stats['executions_failed'] += 1
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """系统健康检查"""
        try:
            health_status = {
                'timestamp': datetime.now().isoformat(),
                'is_running': self.is_running,
                'simulation_mode': self.simulation_mode,
                'auto_execution': self.auto_execution,
                'notifiers': {
                    notifier.name: notifier.enabled 
                    for notifier in self.notifiers
                },
                'stats': self.stats.copy(),
                'signal_generator_status': self.signal_generator.get_statistics(),
                'portfolio_status': self.signal_generator.get_portfolio_status()
            }
            
            self.last_health_check = datetime.now()
            self.logger.info("💚 系统健康检查完成")
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"❌ 健康检查失败: {e}")
            return {'error': str(e)}
    
    async def send_health_report(self):
        """发送健康报告"""
        health_status = await self.health_check()
        
        message = f"""
📊 **系统健康报告**

**运行状态**: {'🟢 运行中' if health_status['is_running'] else '🔴 已停止'}
**交易模式**: {'🎮 模拟' if health_status['simulation_mode'] else '💰 实盘'}
**自动执行**: {'✅ 启用' if health_status['auto_execution'] else '❌ 禁用'}

**统计信息**:
• 总信号数: {health_status['stats']['total_signals']}
• 通知成功: {health_status['stats']['notifications_sent']}
• 通知失败: {health_status['stats']['notifications_failed']}
• 执行成功: {health_status['stats']['executions_success']}
• 执行失败: {health_status['stats']['executions_failed']}

**投资组合**:
• 总价值: ${health_status['portfolio_status']['total_value']:.2f}
• 现金: ${health_status['portfolio_status']['cash']:.2f}
• 持仓数: {health_status['portfolio_status']['positions_count']}
• 总收益率: {health_status['portfolio_status']['total_return_pct']:.2f}%
        """.strip()
        
        await self._send_notifications_direct(message, "📊 系统健康报告")
    
    async def _send_notifications_direct(self, message: str, title: str):
        """直接发送通知（不记录到数据库）"""
        tasks = []
        for notifier in self.notifiers:
            if notifier.enabled:
                task = notifier.send(message, title)
                tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    def start(self):
        """启动执行管理器"""
        self.is_running = True
        self.logger.info("🚀 执行管理器已启动")
    
    def stop(self):
        """停止执行管理器"""
        self.is_running = False
        self.logger.info("🛑 执行管理器已停止")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'stats': self.stats.copy(),
            'notifiers': [
                {'name': n.name, 'enabled': n.enabled} 
                for n in self.notifiers
            ],
            'is_running': self.is_running,
            'simulation_mode': self.simulation_mode,
            'auto_execution': self.auto_execution
        }

# 使用示例
if __name__ == "__main__":
    import asyncio
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    async def main():
        # 创建信号生成器
        signal_generator = StrategySignalGenerator()
        
        # 创建执行管理器
        manager = ExecutionManager(signal_generator)
        
        # 添加通知渠道
        log_notifier = LogNotifier()
        manager.add_notifier(log_notifier)
        
        # 启动管理器
        manager.start()
        
        # 发送健康报告
        await manager.send_health_report()
        
        print("🚀 执行管理器演示完成")
    
    asyncio.run(main()) 