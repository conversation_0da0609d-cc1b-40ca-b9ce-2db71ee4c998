#!/usr/bin/env python3
"""
测试策略系统
Test Strategy System
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.strategies import register_strategy, list_strategies, BaseStrategy, get_strategy

class TestStrategy(BaseStrategy):
    """测试策略"""
    
    @classmethod
    def get_default_parameters(cls):
        return {'test_param': 10}
    
    @classmethod
    def get_required_columns(cls):
        return ['close']
    
    def _validate_parameters(self):
        pass
    
    def calculate_indicators(self, data):
        return data
    
    def generate_signals(self, data):
        return data

def test_strategy_system():
    """测试策略系统"""
    print("🧪 测试策略系统")
    print("=" * 40)
    
    # 1. 测试初始状态
    print("\n1. 初始状态:")
    strategies = list_strategies()
    print(f"  可用策略: {strategies}")
    print(f"  策略数量: {len(strategies)}")
    
    # 2. 测试策略注册
    print("\n2. 注册测试策略:")
    register_strategy('Test', TestStrategy)
    strategies = list_strategies()
    print(f"  注册后策略: {strategies}")
    print(f"  策略数量: {len(strategies)}")
    
    # 3. 测试策略获取
    print("\n3. 获取策略实例:")
    try:
        strategy = get_strategy('Test')
        print(f"  策略实例: {strategy}")
        print(f"  策略类型: {type(strategy)}")
        print(f"  默认参数: {strategy.params}")
    except Exception as e:
        print(f"  ❌ 获取策略失败: {e}")
    
    # 4. 测试无效策略
    print("\n4. 测试无效策略:")
    try:
        invalid_strategy = get_strategy('NonExistent')
    except Exception as e:
        print(f"  ✅ 正确捕获错误: {e}")
    
    print("\n✅ 策略系统测试完成")

if __name__ == "__main__":
    test_strategy_system()
