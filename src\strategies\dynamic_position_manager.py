"""
动态仓位管理器
Dynamic Position Manager

实现全仓动态仓位管理系统：
- 总仓位 = 可用资金 + 所有持仓浮盈浮亏总和
- 单次交易额度 = 总仓位 × 0.1倍杠杆
- 最低交易额度保护：0.1万元
- 杠杆模拟：资金不足时可模拟增大杠杆
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from .base import BaseStrategy, SignalType


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    entry_time: datetime
    entry_price: float
    position_size: float  # 实际投入资金
    quantity: float  # 持仓数量（考虑杠杆）
    leverage: float  # 使用的杠杆倍数
    highest_price: float  # 最高价（用于移动止损）
    partial_profit_taken: bool = False  # 是否已分批止盈
    trailing_stop_active: bool = False  # 移动止损是否激活


class DynamicPositionManager:
    """动态仓位管理器"""
    
    def __init__(self, base_strategy: BaseStrategy, initial_capital: float = 10000):
        """
        初始化动态仓位管理器
        
        Args:
            base_strategy: 基础交易策略（如EMA策略）
            initial_capital: 初始资金
        """
        self.base_strategy = base_strategy
        self.initial_capital = initial_capital
        self.available_cash = initial_capital  # 可用现金
        self.positions: Dict[str, Position] = {}  # 当前持仓
        self.closed_trades = []  # 已平仓交易记录
        
        # 仓位管理参数
        self.position_ratio = 0.1  # 仓位比例（0.1倍杠杆）
        self.min_trade_amount = 1000  # 最低交易额度（0.1万）
        self.max_leverage = 5.0  # 最大杠杆倍数
        
    def get_total_portfolio_value(self, current_prices: Dict[str, float]) -> float:
        """
        计算总仓位价值
        
        Args:
            current_prices: 当前价格字典 {symbol: price}
            
        Returns:
            float: 总仓位价值
        """
        total_unrealized_pnl = 0
        
        # 计算所有持仓的浮盈浮亏
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                current_price = current_prices[symbol]
                # 计算持仓价值变化
                position_value = position.quantity * current_price
                invested_amount = position.position_size
                unrealized_pnl = position_value - invested_amount
                total_unrealized_pnl += unrealized_pnl
        
        # 总仓位 = 可用资金 + 所有持仓浮盈浮亏
        total_portfolio = self.available_cash + total_unrealized_pnl
        return total_portfolio
    
    def calculate_trade_amount(self, current_prices: Dict[str, float]) -> float:
        """
        计算下次交易额度
        
        Args:
            current_prices: 当前价格字典
            
        Returns:
            float: 交易额度
        """
        total_portfolio = self.get_total_portfolio_value(current_prices)
        
        # 标准交易额度 = 总仓位 × 0.1
        standard_amount = total_portfolio * self.position_ratio
        
        # 应用最低交易额度保护
        if total_portfolio >= self.min_trade_amount:
            return max(standard_amount, self.min_trade_amount)
        else:
            # 总仓位小于最低额度时，按比例计算
            return total_portfolio * self.position_ratio
    
    def can_open_position(self, trade_amount: float) -> tuple[bool, float]:
        """
        检查是否可以开仓，并计算所需杠杆
        
        Args:
            trade_amount: 需要的交易额度
            
        Returns:
            tuple: (是否可以开仓, 所需杠杆倍数)
        """
        if self.available_cash >= trade_amount:
            # 资金充足，无需杠杆
            return True, 1.0
        elif self.available_cash > 0:
            # 资金不足但有部分资金，计算所需杠杆
            required_leverage = trade_amount / self.available_cash
            if required_leverage <= self.max_leverage:
                return True, required_leverage
            else:
                return False, required_leverage
        else:
            # 无可用资金
            return False, 0.0
    
    def open_position(self, symbol: str, entry_price: float, entry_time: datetime,
                     trade_amount: float, leverage: float = 1.0) -> bool:
        """
        开仓
        
        Args:
            symbol: 交易对
            entry_price: 入场价格
            entry_time: 入场时间
            trade_amount: 交易额度
            leverage: 杠杆倍数
            
        Returns:
            bool: 是否成功开仓
        """
        # 计算实际投入资金和持仓数量
        actual_investment = min(trade_amount, self.available_cash)
        quantity = (trade_amount / entry_price)  # 考虑杠杆的持仓数量
        
        # 创建持仓记录
        position = Position(
            symbol=symbol,
            entry_time=entry_time,
            entry_price=entry_price,
            position_size=actual_investment,
            quantity=quantity,
            leverage=leverage,
            highest_price=entry_price
        )
        
        # 更新状态
        self.positions[symbol] = position
        self.available_cash -= actual_investment
        
        return True
    
    def close_position(self, symbol: str, exit_price: float, exit_time: datetime,
                      exit_reason: str, partial_ratio: float = 1.0) -> Optional[Dict]:
        """
        平仓（支持分批平仓）
        
        Args:
            symbol: 交易对
            exit_price: 出场价格
            exit_time: 出场时间
            exit_reason: 出场原因
            partial_ratio: 平仓比例（1.0=全部平仓，0.5=平仓一半）
            
        Returns:
            Dict: 交易记录，如果没有持仓则返回None
        """
        if symbol not in self.positions:
            return None
        
        position = self.positions[symbol]
        
        # 计算平仓数量和金额
        close_quantity = position.quantity * partial_ratio
        close_investment = position.position_size * partial_ratio
        
        # 计算收益
        gross_proceeds = close_quantity * exit_price
        pnl = gross_proceeds - close_investment
        pnl_pct = (pnl / close_investment * 100) if close_investment > 0 else 0
        
        # 更新可用资金
        self.available_cash += gross_proceeds
        
        # 创建交易记录
        trade_record = {
            'symbol': symbol,
            'entry_time': position.entry_time,
            'exit_time': exit_time,
            'entry_price': position.entry_price,
            'exit_price': exit_price,
            'quantity': close_quantity,
            'investment': close_investment,
            'proceeds': gross_proceeds,
            'pnl': pnl,
            'pnl_pct': pnl_pct,
            'leverage': position.leverage,
            'exit_reason': exit_reason,
            'partial_ratio': partial_ratio
        }
        
        self.closed_trades.append(trade_record)
        
        # 更新或移除持仓
        if partial_ratio >= 1.0:
            # 全部平仓
            del self.positions[symbol]
        else:
            # 部分平仓，更新持仓
            position.quantity *= (1 - partial_ratio)
            position.position_size *= (1 - partial_ratio)
            if partial_ratio >= 0.5:
                position.partial_profit_taken = True
        
        return trade_record
    
    def update_position_tracking(self, symbol: str, current_price: float):
        """
        更新持仓跟踪信息（最高价等）
        
        Args:
            symbol: 交易对
            current_price: 当前价格
        """
        if symbol in self.positions:
            position = self.positions[symbol]
            if current_price > position.highest_price:
                position.highest_price = current_price
    
    def get_portfolio_summary(self, current_prices: Dict[str, float]) -> Dict[str, Any]:
        """
        获取投资组合摘要
        
        Args:
            current_prices: 当前价格字典
            
        Returns:
            Dict: 投资组合摘要
        """
        total_portfolio = self.get_total_portfolio_value(current_prices)
        total_unrealized_pnl = total_portfolio - self.available_cash
        
        return {
            'available_cash': self.available_cash,
            'total_portfolio_value': total_portfolio,
            'total_unrealized_pnl': total_unrealized_pnl,
            'active_positions': len(self.positions),
            'total_trades': len(self.closed_trades),
            'next_trade_amount': self.calculate_trade_amount(current_prices)
        }
