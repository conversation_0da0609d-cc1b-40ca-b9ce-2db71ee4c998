#!/usr/bin/env python3
"""
测试优化后的EMA突破策略
Test Optimized EMA Breakout Strategy
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.strategies import list_strategies, get_strategy
# 导入策略以触发注册
from src.strategies.ema_breakout_strategy import EMABreakoutStrategy

def create_test_data_with_trend(periods=1500):
    """创建包含趋势变化的测试数据"""
    np.random.seed(42)
    
    # 创建基础价格数据，包含明显的趋势变化
    base_price = 100
    prices = [base_price]
    
    # 第一阶段：下跌趋势（熊市）
    for i in range(300):
        change = np.random.normal(-0.002, 0.015)  # 轻微下跌趋势
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)

    # 第二阶段：横盘整理
    for i in range(300):
        change = np.random.normal(0, 0.01)  # 横盘
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)

    # 第三阶段：上涨趋势（牛市）
    for i in range(600):
        change = np.random.normal(0.003, 0.015)  # 上涨趋势
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)

    # 第四阶段：回调
    for i in range(300):
        change = np.random.normal(-0.001, 0.012)  # 轻微回调
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    prices = prices[1:]  # 移除初始价格
    
    # 创建OHLCV数据
    data = []
    for i, close in enumerate(prices):
        high = close * (1 + abs(np.random.normal(0, 0.008)))
        low = close * (1 - abs(np.random.normal(0, 0.008)))
        open_price = close + np.random.normal(0, 0.003) * close
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'open': open_price,
            'high': max(open_price, high, close),
            'low': min(open_price, low, close),
            'close': close,
            'volume': volume,
            'datetime': pd.Timestamp('2023-01-01') + pd.Timedelta(hours=4*i)
        })
    
    return pd.DataFrame(data)

def test_optimized_ema_strategy():
    """测试优化后的EMA突破策略"""
    print("🧪 测试优化后的EMA突破策略")
    print("=" * 60)
    
    # 1. 检查策略注册
    print("\n1. 检查策略注册:")
    strategies = list_strategies()
    print(f"  可用策略: {strategies}")
    
    if 'EMABreakout' not in strategies:
        print("  ❌ EMABreakout策略未注册")
        return
    
    # 2. 创建策略实例
    print("\n2. 创建策略实例:")
    try:
        strategy = get_strategy('EMABreakout')
        print(f"  ✅ 策略实例创建成功: {type(strategy)}")
        print(f"  优化后参数: {strategy.params}")
    except Exception as e:
        print(f"  ❌ 创建策略失败: {e}")
        return
    
    # 3. 创建测试数据
    print("\n3. 创建测试数据:")
    test_data = create_test_data_with_trend(1500)  # 1500个4小时K线
    print(f"  测试数据: {len(test_data)} 条记录")
    print(f"  时间范围: {test_data['datetime'].min()} 到 {test_data['datetime'].max()}")
    print(f"  价格范围: {test_data['close'].min():.2f} - {test_data['close'].max():.2f}")
    
    # 4. 计算指标
    print("\n4. 计算技术指标:")
    try:
        df_with_indicators = strategy.calculate_indicators(test_data)
        print(f"  ✅ 指标计算成功")
        
        # 检查新的指标字段
        new_columns = [col for col in df_with_indicators.columns if col not in test_data.columns]
        print(f"  新增字段: {new_columns}")
        
        # 检查EMA值和新条件
        latest_row = df_with_indicators.iloc[-1]
        if not pd.isna(latest_row.get('ema21')):
            print(f"  EMA21: {latest_row['ema21']:.2f}")
            print(f"  EMA50: {latest_row['ema50']:.2f}")
            print(f"  EMA200: {latest_row['ema200']:.2f}")
            print(f"  价格在EMA200之上: {latest_row.get('price_above_ema200', False)}")
            print(f"  EMA21在EMA200之上: {latest_row.get('ema21_above_ema200', False)}")
            print(f"  在距离阈值内: {latest_row.get('within_distance_threshold', False)}")
            
            if not pd.isna(latest_row.get('ema200')):
                distance_pct = abs(latest_row['ema21'] - latest_row['ema200']) / latest_row['ema200'] * 100
                print(f"  EMA21与EMA200距离: {distance_pct:.1f}%")
        else:
            print("  ⚠️ EMA指标为空，可能数据不足")
            
    except Exception as e:
        print(f"  ❌ 指标计算失败: {e}")
        return
    
    # 5. 生成信号
    print("\n5. 生成交易信号:")
    try:
        df_with_signals = strategy.generate_signals(df_with_indicators)
        print(f"  ✅ 信号生成成功")
        
        # 统计信号
        buy_signals = df_with_signals[df_with_signals['signal'] == 1]
        sell_signals = df_with_signals[df_with_signals['signal'] == -1]
        
        print(f"  买入信号数量: {len(buy_signals)}")
        print(f"  卖出信号数量: {len(sell_signals)}")
        
        # 显示买入信号详情
        if len(buy_signals) > 0:
            print("\n  📈 买入信号详情:")
            for idx, signal in buy_signals.iterrows():
                print(f"    时间: {signal['datetime']}")
                print(f"    价格: {signal['close']:.2f}")
                print(f"    市场环境: {signal.get('market_env', 'N/A')}")
                print(f"    置信度: {signal['confidence']:.1%}")
                print(f"    原因: {signal['reason']}")
                print(f"    ---")
        
        # 显示卖出信号详情
        if len(sell_signals) > 0:
            print("\n  📉 卖出信号详情:")
            for idx, signal in sell_signals.iterrows():
                print(f"    时间: {signal['datetime']}")
                print(f"    价格: {signal['close']:.2f}")
                print(f"    止损价: {signal.get('stop_loss_price', 'N/A')}")
                print(f"    退出类型: {signal.get('exit_type', 'N/A')}")
                print(f"    原因: {signal['reason']}")
                print(f"    ---")
                
    except Exception as e:
        print(f"  ❌ 信号生成失败: {e}")
        return
    
    # 6. 测试实时信号生成
    print("\n6. 测试实时信号生成:")
    try:
        latest_signal = strategy.generate_signal(test_data)
        print(f"  ✅ 实时信号生成成功")
        print(f"  动作: {latest_signal['action']}")
        print(f"  置信度: {latest_signal['confidence']:.1f}%")
        print(f"  原因: {latest_signal['reason']}")
        print(f"  市场环境: {latest_signal.get('market_env', 'N/A')}")
        print(f"  当前价格: {latest_signal['price']:.2f}")
        
        if not pd.isna(latest_signal.get('stop_loss_price')):
            print(f"  止损价: {latest_signal['stop_loss_price']:.2f}")
            
    except Exception as e:
        print(f"  ❌ 实时信号生成失败: {e}")
        return
    
    # 7. 验证趋势过滤器效果
    print("\n7. 验证趋势过滤器效果:")
    try:
        # 统计不同市场环境下的信号分布
        market_env_stats = df_with_signals.groupby('market_env')['signal'].agg(['count', 'sum']).fillna(0)
        print("  市场环境统计:")
        for env, stats in market_env_stats.iterrows():
            total_signals = stats['count']
            buy_signals = stats['sum']  # 买入信号为1，所以sum就是买入信号数量
            print(f"    {env}: 总信号{total_signals}, 买入信号{buy_signals}")
        
        # 检查是否在熊市环境中避免了买入信号
        bear_market_buys = df_with_signals[
            (df_with_signals['market_env'] == 'BEAR') & 
            (df_with_signals['signal'] == 1)
        ]
        
        if len(bear_market_buys) == 0:
            print("  ✅ 趋势过滤器工作正常：熊市中无买入信号")
        else:
            print(f"  ⚠️ 趋势过滤器可能有问题：熊市中仍有{len(bear_market_buys)}个买入信号")
            
    except Exception as e:
        print(f"  ❌ 趋势过滤器验证失败: {e}")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    test_optimized_ema_strategy()
