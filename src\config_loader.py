"""
Configuration loader module for loading JSON configuration files.
"""
import json
import os
from pathlib import Path

def load_config(config_path=None):
    """
    Load configuration from JSON file.
    
    Args:
        config_path (str, optional): Path to config file. 
                                   If None, will try to find config.json in config/ directory.
    
    Returns:
        dict: Configuration data
    """
    if config_path is None:
        # Try to find config.json in the config directory
        current_dir = Path(__file__).parent.parent  # Go up from src/ to project root
        config_path = current_dir / "config" / "config.json"
    else:
        # Handle relative paths from the calling file's perspective
        if config_path.startswith('./'):
            current_dir = Path(__file__).parent.parent  # Go up from src/ to project root
            config_path = current_dir / "config" / config_path[2:]  # Remove './' prefix
        else:
            config_path = Path(config_path)
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        return config_data
    except FileNotFoundError:
        print(f"Configuration file not found: {config_path}")
        raise
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON configuration file: {e}")
        raise
    except Exception as e:
        print(f"Error loading configuration: {e}")
        raise

def get_config_value(key, default=None, config_path=None):
    """
    Get a specific configuration value.
    
    Args:
        key (str): Configuration key to retrieve
        default: Default value if key not found
        config_path (str, optional): Path to config file
    
    Returns:
        Configuration value or default
    """
    try:
        config = load_config(config_path)
        return config.get(key, default)
    except Exception:
        return default 