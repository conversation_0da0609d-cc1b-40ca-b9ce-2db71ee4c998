"""
策略模块
Trading Strategies Module

包含所有交易策略的实现
"""

from .base import BaseStrategy, SignalType
from .backtest_engine import BacktestEngine

# 策略注册表 - 清空，等待新策略注册
STRATEGY_REGISTRY = {
    # 新策略将在这里注册
    # 示例：'StrategyName': StrategyClass,
}

def get_strategy(strategy_name: str, config: dict = None, params: dict = None):
    """
    获取策略实例

    Args:
        strategy_name: 策略名称
        config: 系统配置
        params: 策略参数

    Returns:
        BaseStrategy: 策略实例
    """
    if strategy_name not in STRATEGY_REGISTRY:
        available_strategies = list(STRATEGY_REGISTRY.keys())
        if not available_strategies:
            raise ValueError(f"未知策略: {strategy_name}. 当前没有注册任何策略，请先添加策略。")
        else:
            raise ValueError(f"未知策略: {strategy_name}. 可用策略: {available_strategies}")

    strategy_class = STRATEGY_REGISTRY[strategy_name]
    return strategy_class(config or {}, params)

def register_strategy(strategy_name: str, strategy_class):
    """
    注册新策略

    Args:
        strategy_name: 策略名称
        strategy_class: 策略类
    """
    if not issubclass(strategy_class, BaseStrategy):
        raise ValueError(f"策略类 {strategy_class.__name__} 必须继承自 BaseStrategy")

    STRATEGY_REGISTRY[strategy_name] = strategy_class
    print(f"✅ 策略 '{strategy_name}' 已注册")

def list_strategies():
    """列出所有可用策略"""
    return list(STRATEGY_REGISTRY.keys())

__all__ = [
    'BaseStrategy',
    'SignalType',
    'BacktestEngine',
    'STRATEGY_REGISTRY',
    'get_strategy',
    'register_strategy',
    'list_strategies'
]