# EMA策略优先优化方案
# EMA Strategy Priority Optimization Plan

**制定时间**: 2025-01-05  
**优化重点**: 趋势过滤器 + 退出机制优化  
**预计工期**: 1天  

---

## 🎯 优化目标

### 优先级1: 趋势过滤器（EMA200）
**目标**: 只有当价格和短期EMA都运行在EMA200之上时，才执行买入信号

**核心逻辑**:
- 市场环境判断：价格 > EMA200 = 牛市环境
- 入场条件：EMA21 > EMA200 AND 当前价格 > EMA200
- 熊市过滤：价格 < EMA200时，完全避免开多单

### 优先级2: 退出机制优化
**目标**: 使用移动止损和多重退出条件，避免利润大幅回撤

**核心逻辑**:
- 移动止损：止损位跟随价格上涨而上移
- 多重退出：固定止损 + 移动止损 + 趋势反转
- 快速响应：不等死叉，提前锁定利润

---

## 📋 详细实施方案

### Phase 1: 趋势过滤器实现

#### 1.1 修改入场条件逻辑
```python
def _calculate_entry_conditions(self, daily_data):
    """计算入场条件（增加趋势过滤器）"""
    
    # 基础EMA计算
    daily_data['ema21'] = daily_data['close'].ewm(span=21).mean()
    daily_data['ema50'] = daily_data['close'].ewm(span=50).mean()
    daily_data['ema200'] = daily_data['close'].ewm(span=200).mean()
    
    # 趋势过滤器：价格必须在EMA200之上（牛市环境）
    daily_data['price_above_ema200'] = daily_data['close'] > daily_data['ema200']
    
    # 主要入场条件：EMA21 > EMA200
    daily_data['ema21_above_ema200'] = daily_data['ema21'] > daily_data['ema200']
    
    # 距离条件：EMA21与EMA200的距离在3%以内
    distance_pct = abs(daily_data['ema21'] - daily_data['ema200']) / daily_data['ema200']
    daily_data['within_distance_threshold'] = distance_pct <= 0.03
    
    # 综合入场条件（三个条件都必须满足）
    daily_data['entry_condition'] = (
        daily_data['price_above_ema200'] &      # 趋势过滤器
        daily_data['ema21_above_ema200'] &      # 主要条件
        daily_data['within_distance_threshold']  # 距离条件
    )
    
    # 信号触发（条件刚满足时）
    daily_data['entry_trigger'] = (
        daily_data['entry_condition'] & 
        ~daily_data['entry_condition'].shift(1).fillna(False)
    )
    
    return daily_data
```

#### 1.2 增加市场环境判断
```python
def _get_market_environment(self, current_price, ema200):
    """判断当前市场环境"""
    if pd.isna(ema200):
        return 'UNKNOWN'
    
    if current_price > ema200:
        return 'BULL'  # 牛市环境，可以做多
    else:
        return 'BEAR'  # 熊市环境，避免做多
```

### Phase 2: 退出机制优化

#### 2.1 移动止损机制
```python
class TrailingStopManager:
    """移动止损管理器"""
    
    def __init__(self, initial_stop_pct=0.05, trailing_pct=0.03):
        self.initial_stop_pct = initial_stop_pct  # 初始止损5%
        self.trailing_pct = trailing_pct          # 移动止损3%
        self.highest_price = None
        self.stop_loss_price = None
    
    def update(self, current_price, entry_price):
        """更新移动止损位"""
        if self.highest_price is None:
            # 初始化
            self.highest_price = max(current_price, entry_price)
            self.stop_loss_price = entry_price * (1 - self.initial_stop_pct)
        else:
            # 更新最高价
            if current_price > self.highest_price:
                self.highest_price = current_price
                # 上移止损位
                new_stop = self.highest_price * (1 - self.trailing_pct)
                self.stop_loss_price = max(self.stop_loss_price, new_stop)
        
        return self.stop_loss_price
    
    def should_exit(self, current_price):
        """判断是否应该止损退出"""
        if self.stop_loss_price is None:
            return False
        return current_price <= self.stop_loss_price
```

#### 2.2 多重退出条件
```python
def _check_exit_conditions_optimized(self, row, position_info, trailing_stop):
    """优化的退出条件检查"""
    current_price = row['close']
    entry_price = position_info['entry_price']
    
    # 1. 移动止损（最高优先级）
    if trailing_stop.should_exit(current_price):
        profit_pct = (current_price - entry_price) / entry_price * 100
        return {
            'condition': 'trailing_stop',
            'reason': f'移动止损触发，当前盈利{profit_pct:.1f}%',
            'type': 'trailing_stop'
        }
    
    # 2. 趋势反转（价格跌破EMA200）
    if not pd.isna(row['ema200']) and current_price < row['ema200']:
        return {
            'condition': 'trend_reversal',
            'reason': '价格跌破EMA200，趋势反转',
            'type': 'trend_reversal'
        }
    
    # 3. EMA21跌破EMA50（短期趋势恶化）
    if (not pd.isna(row['ema21']) and not pd.isna(row['ema50']) and 
        row['ema21'] < row['ema50']):
        return {
            'condition': 'ema21_below_ema50',
            'reason': 'EMA21跌破EMA50，短期趋势恶化',
            'type': 'short_term_reversal'
        }
    
    # 4. 固定止损（20%）
    loss_pct = (entry_price - current_price) / entry_price
    if loss_pct >= 0.20:
        return {
            'condition': 'fixed_stop_loss',
            'reason': f'固定止损触发，亏损{loss_pct*100:.1f}%',
            'type': 'fixed_stop_loss'
        }
    
    return None
```

### Phase 3: 策略整合

#### 3.1 更新主要参数
```python
@classmethod
def get_default_parameters(cls) -> Dict[str, Any]:
    return {
        'ema_short': 21,              # 短期EMA周期
        'ema_medium': 50,             # 中期EMA周期  
        'ema_long': 200,              # 长期EMA周期
        'distance_threshold': 0.03,   # 3%接近距离阈值
        'initial_stop_pct': 0.05,     # 初始止损5%
        'trailing_stop_pct': 0.03,    # 移动止损3%
        'fixed_stop_pct': 0.20,       # 固定止损20%
        'min_ema200_periods': 200,    # EMA200最少需要的数据点
        'daily_close_hour': 8         # 日线收盘时间
    }
```

#### 3.2 更新信号生成逻辑
```python
def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
    """生成交易信号（优化版）"""
    df = data.copy()
    
    # 初始化信号列
    df['signal'] = SignalType.NEUTRAL.value
    df['confidence'] = 0.0
    df['reason'] = ''
    df['market_env'] = ''
    df['stop_loss_price'] = np.nan
    
    # 交易状态管理
    current_position = None
    trailing_stop = None
    
    for i in range(len(df)):
        if not df.iloc[i]['valid_signal']:
            continue
        
        current_row = df.iloc[i]
        current_price = current_row['close']
        
        # 判断市场环境
        market_env = self._get_market_environment(current_price, current_row.get('ema200'))
        df.iloc[i, df.columns.get_loc('market_env')] = market_env
        
        # 检查入场信号（只在牛市环境中）
        if (current_row['entry_trigger'] and 
            current_position is None and 
            market_env == 'BULL'):
            
            # 开仓
            current_position = {
                'entry_time': current_row['datetime'],
                'entry_price': current_price,
                'entry_idx': i
            }
            
            # 初始化移动止损
            trailing_stop = TrailingStopManager(
                self.params['initial_stop_pct'],
                self.params['trailing_stop_pct']
            )
            
            # 记录买入信号
            confidence = 0.7  # 基础置信度
            df.iloc[i, df.columns.get_loc('signal')] = SignalType.BUY.value
            df.iloc[i, df.columns.get_loc('confidence')] = confidence
            df.iloc[i, df.columns.get_loc('reason')] = f"趋势突破买入 (市场环境: {market_env})"
        
        # 检查退出信号
        elif current_position is not None:
            # 更新移动止损
            stop_price = trailing_stop.update(current_price, current_position['entry_price'])
            df.iloc[i, df.columns.get_loc('stop_loss_price')] = stop_price
            
            # 检查退出条件
            exit_signal = self._check_exit_conditions_optimized(
                current_row, current_position, trailing_stop
            )
            
            if exit_signal:
                # 平仓
                df.iloc[i, df.columns.get_loc('signal')] = SignalType.SELL.value
                df.iloc[i, df.columns.get_loc('confidence')] = 0.8
                df.iloc[i, df.columns.get_loc('reason')] = f"退出: {exit_signal['reason']}"
                
                current_position = None
                trailing_stop = None
    
    return df
```

---

## 🧪 测试计划

### 1. 趋势过滤器测试
- 验证牛市环境下的信号生成
- 验证熊市环境下的信号过滤
- 测试EMA200数据不足的处理

### 2. 移动止损测试
- 测试止损位的正确更新
- 验证利润保护效果
- 测试极端价格波动情况

### 3. 综合回测
- 使用历史数据验证整体效果
- 对比优化前后的关键指标
- 分析不同市场环境下的表现

---

## 📊 预期效果

### 趋势过滤器效果
- **减少熊市亏损**: 避免在下跌趋势中开多单
- **提高胜率**: 只在有利环境中交易
- **降低回撤**: 减少逆势交易的损失

### 退出机制优化效果
- **保护利润**: 移动止损锁定大部分利润
- **快速响应**: 趋势反转时及时退出
- **风险控制**: 多重止损机制保护本金

---

**实施结论**: 这两个优化是最核心和最有效的改进，能够显著提升策略的实战表现。
